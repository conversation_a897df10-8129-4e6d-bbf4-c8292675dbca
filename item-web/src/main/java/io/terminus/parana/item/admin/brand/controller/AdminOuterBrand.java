package io.terminus.parana.item.admin.brand.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.exception.RestException;
import io.terminus.parana.item.admin.brand.bean.excel.OuterBrandExcelImportBO;
import io.terminus.parana.item.admin.brand.bean.excel.OuterBrandExportTemplate;
import io.terminus.parana.item.admin.brand.bean.excel.OuterBrandExcelImportProcessor;
import io.terminus.parana.item.admin.brand.bean.excel.OuterBrandExcelTemplate;
import io.terminus.parana.item.brand.api.bean.request.*;
import io.terminus.parana.item.brand.api.bean.response.BrandInfo;
import io.terminus.parana.item.brand.api.bean.response.OuterBrandInfo;
import io.terminus.parana.item.brand.api.facade.BrandReadFacade;
import io.terminus.parana.item.brand.api.facade.OuterBrandReadFacade;
import io.terminus.parana.item.brand.api.facade.OuterBrandWriteFacade;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.item.api.bean.request.item.ItemExcelReportCreateRequest;
import io.terminus.parana.item.item.api.facade.ItemWriteFacade;
import io.terminus.parana.item.item.enums.OuterBrandStatus;
import io.terminus.parana.item.common.util.excel.expor.ItemExcelExportHelper;
import io.terminus.parana.item.shop.api.bean.request.ShopSingleQueryByIdRequest;
import io.terminus.parana.item.shop.api.bean.response.ShopInfo;
import io.terminus.parana.item.shop.api.facade.ShopReadFacade;
import io.terminus.parana.item.web.excel.ProcessResult;
import io.terminus.parana.item.zhengqi.api.facade.ZhengQiItemWriteFacade;
import io.terminus.parana.trade.common.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :2021-01-13 17:44:24
 */

@Slf4j
@RestController
@RequestMapping("/api/admin/item/OuterBrand")
@Api(
        value = "外部品牌接口_AdminOuterBrand",
        tags = {"外部品牌接口_AdminOuterBrand"}
)
public class AdminOuterBrand {
    /**
     * 政企总部区运营id
     */
    public static final Long operatorId = 2100260002L;
    @Autowired
    private OuterBrandWriteFacade outerBrandWriteFacade;
    @Autowired
    private ShopReadFacade shopReadFacade;
    @Autowired
    private OuterBrandReadFacade outerBrandReadFacade;

    @Autowired
    private ItemExcelExportHelper itemExcelExportHelper;
    @Autowired
    private OuterBrandExcelImportProcessor outerBrandExcelImportProcessor;
    @Autowired
    private ItemWriteFacade itemWriteFacade;
    @Autowired
    private BrandReadFacade brandReadFacade;


    @ApiOperation("根据名字模糊查询")
    @RequestMapping(value = "/findBrandFuzzy", method = RequestMethod.GET)
    public List<OuterBrandInfo> fuzzyByNameQuery(OuterBrandFuzzyRequest outerBrandFuzzyRequest) {
        outerBrandFuzzyRequest.setTenantId(RequestContext.getTenantId());
        return Assert.take(outerBrandReadFacade.fuzzyByNameQuery(outerBrandFuzzyRequest));
    }

    @ApiOperation("外部品牌修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean updateOuterBrand(@RequestBody OuterBrandUpdateRequest outerBrandUpdateRequest) {

        outerBrandUpdateRequest.setUpdatedBy(RequestContext.getUserName());
        outerBrandUpdateRequest.setUpdatedByName(RequestContext.getNickName());

        outerBrandUpdateRequest.setTenantId(RequestContext.getTenantId());
        return Assert.take(outerBrandWriteFacade.updateOuterBrand(outerBrandUpdateRequest));
    }

    @ApiOperation("外部品牌名分页查询")
    @RequestMapping(value = "/pagingQuery", method = RequestMethod.GET)
    public Paging<OuterBrandInfo> pagingQuery(OuterBrandPagingRequest outerBrandPagingRequest) {

        outerBrandPagingRequest.setTenantId(RequestContext.getTenantId());
        return Assert.take(outerBrandReadFacade.pagingQuery(outerBrandPagingRequest));
    }

    @ApiOperation("创建外部品牌")
    @RequestMapping(value = "/create", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long create(@RequestBody OuterBrandCreateRequest outerBrandCreateRequest) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        outerBrandCreateRequest.setTenantId(RequestContext.getTenantId());

        outerBrandCreateRequest.setStatus(OuterBrandStatus.AVAILABLE.getValue());

        outerBrandCreateRequest.setCreatedBy(RequestContext.getUserId().toString());
        outerBrandCreateRequest.setUpdatedBy(outerBrandCreateRequest.getCreatedBy());
        outerBrandCreateRequest.setCreatedByName(RequestContext.getUserId().toString());
        outerBrandCreateRequest.setUpdatedByName(outerBrandCreateRequest.getCreatedByName());

        return Assert.take(outerBrandWriteFacade.createOuterBrand(outerBrandCreateRequest));
    }

    @ApiOperation("删除外部品牌")
    @RequestMapping(value = "/del", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean delOuterBrand(@RequestBody OuterBrandDeleteRequest outerBrandDeleteRequest) {

        outerBrandDeleteRequest.setTenateId(RequestContext.getTenantId());
        return Assert.take(outerBrandWriteFacade.deleteOuterBrand(outerBrandDeleteRequest));
    }

    @ApiOperation("恢复外部品牌")
    @RequestMapping(value = "/recover",method = RequestMethod.POST,produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean recoverOuterBrand(@RequestBody OuterBrandDeleteRequest outerBrandDeleteRequest){
        outerBrandDeleteRequest.setTenateId(RequestContext.getTenantId());
        return  Assert.take(outerBrandWriteFacade.recoverOuterBrand(outerBrandDeleteRequest));
    }

    @ApiOperation("根据ID查询外部品牌信息")
    @RequestMapping(value = "/findBrand", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public OuterBrandInfo findById(@RequestBody OuterBrandFindByIdRequest outerBrandFindByIdRequest) {
        outerBrandFindByIdRequest.setTenantId(RequestContext.getTenantId());
        return Assert.take(outerBrandReadFacade.findById(outerBrandFindByIdRequest));
    }

    @ApiOperation("数据导出")
    @GetMapping("/outerBrandExportExcel")
    public Boolean outerBrandExportExcel(OuterBrandExcelExportRequest request) {

        Integer tenantId = RequestContext.getTenantId();
        GetOuterBrandListRequest req = new GetOuterBrandListRequest();
        req.setOperatorId(operatorId);
        req.setTenantId(tenantId);
        req.setBrandName(request.getBrandName());
        log.info("outer brand list.export, request = {}", request);
        String reqParamJson = JSON.toJSONString(req);
        ItemExcelReportCreateRequest excelRequest = new ItemExcelReportCreateRequest();
        excelRequest.setOperatorId(operatorId);
        excelRequest.setUserId(RequestContext.getUserId());
        excelRequest.setFileType("外部品牌列表导出");
        String fileName = "OuterBrandListExport_" + TimeUtils.format(new Date(), "yyyy-MM-dd_HH-mm-ss");
        excelRequest.setFileName(fileName);
        excelRequest.setReportType(ExcelExportType.OUTER_BRAND_LIST_EXPORT.getReportType());
        excelRequest.setCenter("item");
        excelRequest.setRequestJson(reqParamJson);
        Response<Boolean> response = itemWriteFacade.excelCreate(excelRequest);
        if (!response.isSuccess()) {
            throw new RestException(response.getError());
        }
        return Boolean.TRUE;
    }

    @ApiOperation(value = "外部品牌导入")
    @PostMapping(value = "/categoryExcelImport", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "importfile", value = "文件流对象,接收数组格式", required = true, dataType = "__File")
    public Boolean categoryExcelImport(MultipartFile importfile ){
        try {
            ProcessResult<OuterBrandExcelImportBO> result = outerBrandExcelImportProcessor.process(importfile);
            Assert.isTrue(result.isSuccess(), result.getErrorMessage());
            List<OuterBrandExcelImportBO> excelData = result.getData();
            List<OuterBrandExcelImportRequest> requests = new ArrayList<>();
            if (excelData.size() > 500) {
                throw new ServiceException("批量导入数量不能超过500条！");
            }
            log.info("excelData.size()--->" + excelData.size());
            log.info("excelData--->" + excelData);
            //校验导入的内部品牌 表格内是否有重复数据
            List<String> brandNameList = Lists.newArrayList();
            for (OuterBrandExcelImportBO outBrand : excelData) {
                if (StringUtils.isBlank(outBrand.getOutBrandName())) {
                    throw new ServiceException("外部品牌名称必填！");
                }
                String brandNames = outBrand.getBrandNames();
                if (StringUtils.isNotEmpty(brandNames)) {
                    brandNames = brandNames.replaceAll("，", ",");
                    brandNames = brandNames.replaceAll(" ", "");
                    outBrand.setBrandNames(brandNames);
                    brandNameList.addAll(Arrays.stream(brandNames.split(",")).collect(Collectors.toList()));
                }
            }
            if(CollectionUtil.isNotEmpty(brandNameList)){
                StringBuilder sdu = new StringBuilder();
                Map<String, List<String>> brandNameMap = brandNameList.stream().collect(Collectors.groupingBy(String::valueOf));
                for (String key : brandNameMap.keySet()) {
                    if (brandNameMap.get(key).size() > 1) {
                        if (sdu.length() > 0) {
                            sdu.append("、");
                        }
                        sdu.append(key);
                    }
                }
                if (sdu.length() > 0) {
                    throw new ServiceException("内部品牌：" + sdu + "。名称有重复！");
                }
                BrandMultiGetRequest queryBrand = new BrandMultiGetRequest();
                List<String> distinctBrandNames = brandNameList.stream().distinct().collect(Collectors.toList());
                queryBrand.setNames(distinctBrandNames);
                List<BrandInfo> brandInfos = Assert.take(brandReadFacade.findByNames(queryBrand));
                if (CollectionUtil.isEmpty(brandInfos)) {
                    throw new ServiceException("内部品牌：" + distinctBrandNames + "未找到！");
                } else {
                    List<String> names = brandInfos.stream().map(BrandInfo::getName).collect(Collectors.toList());
                    if (names.size() != distinctBrandNames.size()) {
                        List<String> noNames = distinctBrandNames.stream().filter(f -> !names.contains(f)).collect(Collectors.toList());
                        throw new ServiceException("内部品牌：" + noNames + "未找到！");
                    }
                }
            }
            for (OuterBrandExcelImportBO bo : excelData) {
                OuterBrandExcelImportRequest req = new OuterBrandExcelImportRequest();
                req.setTenantId(RequestContext.getTenantId());
                req.setOperatorId(operatorId);
                req.setOutBrandName(bo.getOutBrandName());
                req.setOutId(bo.getOutId());
                req.setCreatedAt(new Date());
                req.setCreatedBy(RequestContext.getUserId().toString());
                req.setCreatedByName(RequestContext.getUserName());
                req.setUpdatedAt(new Date());
                req.setUpdatedBy(RequestContext.getUserId().toString());
                req.setUpdatedByName(RequestContext.getUserName());
                req.setBrandNames(bo.getBrandNames());
                requests.add(req);
            }
            return Assert.take(outerBrandWriteFacade.outerBrandExcelImport(requests));
        }catch (Exception e){
            log.error(Throwables.getStackTraceAsString(e));
            if(StringUtils.isNotEmpty(e.getMessage())){
                throw new RestException(e.getMessage());
            }
            log.error("导入外部品牌失败！ message:{} case:{}",e.getMessage(),Throwables.getStackTraceAsString(e));
            throw new RestException("导入外部品牌失败");
        }
    }

    @ApiOperation("模板导出")
    @GetMapping("/excelTemplate")
    public void exportOutBrand(HttpServletResponse response) {
        List<OuterBrandExportTemplate> datas = new ArrayList<>();
        String fileName = "outerBrand_template.xlsx";
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-disposition", "attachment;charset=utf-8;filename=" + fileName);
        try {
            if (datas.isEmpty()) {
                itemExcelExportHelper.downloadTemplate(OuterBrandExportTemplate.class, response.getOutputStream());
            } else {
                itemExcelExportHelper.downloadTemplate(datas, OuterBrandExportTemplate.class, response.getOutputStream());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RestException(e.getMessage());
        }
    }


}

package io.terminus.parana.item.admin.choice.bean;

import io.terminus.parana.item.common.excel.AbstractExcelFileProcessor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021.06.17
 */

@Component
public class ChoiceLotLibUnBindItemProcessor extends AbstractExcelFileProcessor<ChoiceLotLibUnbindItemExcelImportBO> {


    public ChoiceLotLibUnBindItemProcessor() {
        super(ChoiceLotLibUnbindItemExcelImportBO.class);
    }

    public ChoiceLotLibUnbindItemExcelImportBO createNewObject() {

        return new ChoiceLotLibUnbindItemExcelImportBO();
    }
}

package io.terminus.parana.item.mq.consumer;


import io.terminus.common.rocketmq.annotation.MQConsumer;
import io.terminus.common.rocketmq.annotation.MQSubscribe;
import io.terminus.common.rocketmq.autoconfigure.MQProperties;
import io.terminus.parana.item.item.canal.ESMQInjection;
import io.terminus.parana.item.item.canal.ItemMQTagConstant;
import io.terminus.parana.item.item.canal.areaSku.AreaSkuChangeMQPayload;
import io.terminus.parana.item.item.canal.areaSku.AreaSkuESUpdate;
import org.springframework.beans.factory.annotation.Autowired;

@MQConsumer
public class AreaSKuChangeMQConsumer {

	@Autowired
	private AreaSkuESUpdate areaSkuESUpdate;
	@Autowired
	private MQProperties mqProperties;


	@MQSubscribe(tag = ItemMQTagConstant.AREA_SKU_UPSERT)
	public void subscribe(AreaSkuChangeMQPayload event) {

		ESMQInjection.export(event, mqProperties.getTopic(), ItemMQTagConstant.AREA_SKU_UPSERT,"from", this.getClass().getSimpleName());
		
		areaSkuESUpdate.doWork(event);
	}

	@MQSubscribe(tag = ItemMQTagConstant.AREA_SKU_UPSERT_SKU_ID)
	public void subscribeBySkuId(AreaSkuChangeMQPayload event) {

		ESMQInjection.export(event, mqProperties.getTopic(), ItemMQTagConstant.AREA_SKU_UPSERT_SKU_ID,"from", this.getClass().getSimpleName());

		areaSkuESUpdate.doWork(event);
	}

	@MQSubscribe(tag = ItemMQTagConstant.AREA_SKU_UPSERT_OPERATOR_ID)
	public void subscribeByOperatorId(AreaSkuChangeMQPayload event) {

		ESMQInjection.export(event, mqProperties.getTopic(), ItemMQTagConstant.AREA_SKU_UPSERT_OPERATOR_ID,"from", this.getClass().getSimpleName());

		areaSkuESUpdate.doWorkByOperatorId(event);
	}

	@MQSubscribe(tag = ItemMQTagConstant.AREA_SKU_UPSERT_VENDOR_ID)
	public void subscribeByVendorId(AreaSkuChangeMQPayload event) {

		ESMQInjection.export(event, mqProperties.getTopic(), ItemMQTagConstant.AREA_SKU_UPSERT_VENDOR_ID,"from", this.getClass().getSimpleName());

		areaSkuESUpdate.doWorkByVendorId(event);
	}

	@MQSubscribe(tag = ItemMQTagConstant.AREA_SKU_UPSERT_OPERATOR_VENDOR_ID)
	public void subscribeByOperatorIdAndVendorId(AreaSkuChangeMQPayload event) {

		ESMQInjection.export(event, mqProperties.getTopic(), ItemMQTagConstant.AREA_SKU_UPSERT_OPERATOR_VENDOR_ID,"from", this.getClass().getSimpleName());

		areaSkuESUpdate.doWorkByOperatorIdAndVendorId(event);
	}

	/**
	 * 规格变动  更新选品库信息  发送消息通知渠道
	 * @param event
	 */
	@MQSubscribe(tag = ItemMQTagConstant.AREA_SKU_CHANGE)
	public void subAreaSkuChange(AreaSkuChangeMQPayload event){
		ESMQInjection.export(event, mqProperties.getTopic(), ItemMQTagConstant.AREA_SKU_CHANGE,"from", this.getClass().getSimpleName());
		areaSkuESUpdate.subAreaSkuChange(event);
	}
}

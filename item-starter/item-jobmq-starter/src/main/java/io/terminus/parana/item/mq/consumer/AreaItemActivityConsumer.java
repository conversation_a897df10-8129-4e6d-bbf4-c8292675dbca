package io.terminus.parana.item.mq.consumer;

import com.alibaba.fastjson.JSON;
import io.terminus.common.rocketmq.annotation.MQConsumer;
import io.terminus.common.rocketmq.annotation.MQSubscribe;
import io.terminus.common.rocketmq.autoconfigure.MQProperties;
import io.terminus.parana.item.common.canal.BBCGenAutoSequence;
import io.terminus.parana.item.item.canal.ESMQInjection;
import io.terminus.parana.item.item.canal.ItemMQTagConstant;
import io.terminus.parana.item.item.canal.areaItem.AreaItemActivityUpdate;
import io.terminus.parana.item.item.canal.areaItem.ItemConditionDumpMQPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2022-02-29 下午2:28
 */
@Slf4j
@MQConsumer
public class AreaItemActivityConsumer {
    @Autowired
    private MQProperties mqProperties;
    @Autowired
    private AreaItemActivityUpdate areaItemActivityUpdate;

    @MQSubscribe(tag = ItemMQTagConstant.CANAL_ITEM_CONDITION_DUMP)
    public void subscribe(ItemConditionDumpMQPayload event) {
        if (Objects.isNull(event)) {
            return;
        }

        String tag = ItemMQTagConstant.CANAL_ITEM_CONDITION_DUMP;
        String sendMQKey = tag + "-" + event.getOperatorId() + "_" + event.getPartId();
        String msgid = BBCGenAutoSequence.getNextval(tag);
        event.setMsgid(msgid);
        event.setUserName("活动更新");
        event.setShardno(String.valueOf(event.getOperatorId()));
        event.setSendMQKey(sendMQKey);

        ESMQInjection.export(event, mqProperties.getTopic(), ItemMQTagConstant.CANAL_ITEM_CONDITION_DUMP, "from", this.getClass().getSimpleName());
        areaItemActivityUpdate.doWork(event);
    }
}
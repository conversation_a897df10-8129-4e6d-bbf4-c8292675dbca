package io.terminus.parana.item.mq.consumer;

import com.google.common.collect.Lists;
import io.terminus.common.model.Response;
import io.terminus.common.rocketmq.annotation.MQConsumer;
import io.terminus.common.rocketmq.annotation.MQSubscribe;
import io.terminus.parana.item.common.agreement.SystemConfig;
import io.terminus.parana.item.common.base.IdVersionPair;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.cronjob.api.bean.dto.CronJobDTO;
import io.terminus.parana.item.cronjob.api.bean.request.CronJobQueryByTargetCodeRequest;
import io.terminus.parana.item.cronjob.api.facade.CronJobReadFacade;
import io.terminus.parana.item.cronjob.enums.CronJobStatus;
import io.terminus.parana.item.cronjob.enums.CronJobType;
import io.terminus.parana.item.item.api.bean.request.item.ItemQueryBySingleIdRequest;
import io.terminus.parana.item.item.api.bean.request.item.ItemUpdateStatusRequest;
import io.terminus.parana.item.item.api.bean.response.item.ItemInfo;
import io.terminus.parana.item.item.api.facade.ItemReadFacade;
import io.terminus.parana.item.item.api.facade.ItemWriteFacade;
import io.terminus.parana.item.item.enums.ItemStatus;
import io.terminus.parana.item.item.event.ItemAutoOffShelfEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2020/4/7
 */
@Slf4j
@MQConsumer
public class ItemAutoOffShelfConsumer {

    private static final Integer DEFAULT_TENANT_ID = 1;

    @Autowired
    private ItemReadFacade itemReadFacade;
    @Autowired
    private ItemWriteFacade itemWriteFacade;
    @Autowired
    private CronJobReadFacade cronJobReadFacade;

    @MQSubscribe(tag = ItemAutoOffShelfEvent.STANDARD_TAG)
    public void subscribe(ItemAutoOffShelfEvent event) {
        log.info("[ITEM]消费ITEM_AUTO_OFF_SHELF消息开始,param:{}", event);

        // 校验下架时间
        CronJobQueryByTargetCodeRequest cronJobRequest = new CronJobQueryByTargetCodeRequest();
        cronJobRequest.setTargetCodeList(Collections.singletonList(event.getItemId().toString()));
        cronJobRequest.setTenantId(DEFAULT_TENANT_ID);
        cronJobRequest.setTypeList(Collections.singletonList(CronJobType.ITEM_AUTOMATICALLY_OFF_SHELVES));
        cronJobRequest.setStatus(CronJobStatus.FINISH_SUCCEED);
        Response<List<CronJobDTO>> cronJobResponse = cronJobReadFacade.queryByTargetCodes(cronJobRequest);
        List<CronJobDTO> cronJobDTOS = Assert.take(cronJobResponse);

        Boolean executeAtCheck = Boolean.FALSE;
        for (CronJobDTO cronJobDTO : cronJobDTOS) {
            if (event.getExecuteAt().equals(cronJobDTO.getExecuteAt().getTime())) {
                executeAtCheck = Boolean.TRUE;
                break;
            }
        }

        if (!executeAtCheck) {
            log.info("[ITEM]消费ITEM_AUTO_OFF_SHELF消息,executeAt校验不通过");
            return;
        }

        ItemQueryBySingleIdRequest queryRequest = new ItemQueryBySingleIdRequest();
        queryRequest.setTenantId(DEFAULT_TENANT_ID);
        queryRequest.setId(event.getItemId());
        Response<ItemInfo> queryResponse = itemReadFacade.queryBySingleId(queryRequest);
        if (!queryResponse.isSuccess()) {
            log.error("[ITEM]消费ITEM_AUTO_OFF_SHELF消息,获取商品信息失败, param:{}, cause:{}",
                    event.getItemId(), queryResponse.getError());
            return;
        }
        ItemInfo itemInfo = queryResponse.getResult();
        if (itemInfo == null) {
            log.error("[ITEM]消费ITEM_AUTO_OFF_SHELF消息,商品不存在,param:{}", event.getItemId());
            return;
        }

        // 仅有上架状态才可以下架
        if (!itemInfo.getStatus().equals(ItemStatus.ON_SHELF.getValue())) {
            log.error("[ITEM]消费ITEM_AUTO_OFF_SHELF消息,商品状态不允许下架操作, 当前商品id:{}状态:{}",
                    event.getItemId(), itemInfo.getStatus());
            return;
        }

        IdVersionPair idVersionPair = new IdVersionPair(itemInfo.getId(), itemInfo.getVersion());

        ItemUpdateStatusRequest updateRequest = new ItemUpdateStatusRequest();
        updateRequest.setTargetList(Lists.newArrayList(idVersionPair));
        updateRequest.setTenantId(DEFAULT_TENANT_ID);
        updateRequest.setShopId(itemInfo.getShopId());
        updateRequest.setStatus(ItemStatus.OFF_SHELF.getValue());
        updateRequest.setUpdatedBy(SystemConfig.getSystemUserIdentity());
        Response<Boolean> updateResponse = itemWriteFacade.sellerUpdateStatus(updateRequest);
        if (!updateResponse.isSuccess()) {
            log.error("[ITEM]消费ITEM_AUTO_OFF_SHELF消息,更新商品状态失败, param:{}, cause:{}",
                    idVersionPair, updateResponse.getError());
            return;
        }
        log.info("[ITEM]消费ITEM_AUTO_OFF_SHELF消息结束");
    }


}

package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import lombok.Data;

import java.util.Set;

/**
 * @author: lql
 * @version: 2020/8/11 12:10 上午
 */
@Data
public class VendorPartnershipQueryListInfoRequest extends AbstractRequest {

    private static final long serialVersionUID = 2852408194045653448L;

    @ApiModelProperty(value = "供应商id")
    private Long vendorId;

    @ApiModelProperty(value = "区域运营id")
    private Long operatorId;

    @ApiModelProperty(value = "供应商商品id")
    private Long itemId;

    @ApiModelProperty(value = "供应商商品id-集合")
    private Set<Long> itemIdSet;

    @ApiModelProperty("忽略不查询的区域运营ID")
    private Long ignoreOperatorId;

    @Override
    public void checkParam() {
        if (vendorId == null && operatorId == null) {
            ParamUtil.nonNull(vendorId, "both.partnership.vendorId.and.operatorId.are.null");
        }
        ParamUtil.expectTrue(null != itemId || (null != itemIdSet && !itemIdSet.isEmpty()), "partnership.itemId.is.null");

    }

}

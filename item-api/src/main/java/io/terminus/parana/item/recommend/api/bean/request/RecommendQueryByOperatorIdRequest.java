package io.terminus.parana.item.recommend.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.consts.OperationTypes;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: lql
 * @version: 2020/8/7 2:42 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendQueryByOperatorIdRequest extends AbstractRequest {

    private static final long serialVersionUID = 5889288897834136947L;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @Override
    public OperationType getOperationType() {
        return OperationTypes.resolve(this.getClass(), false);
    }

    @Override
    public void checkParam() {
        super.checkParam();
        ParamUtil.nonNull(operatorId, "recommend.operator.id.is.null");
    }

}

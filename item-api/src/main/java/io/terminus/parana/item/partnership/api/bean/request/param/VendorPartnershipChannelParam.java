package io.terminus.parana.item.partnership.api.bean.request.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 供应商合作关系
 *
 * <AUTHOR>
 */
@Data
public class VendorPartnershipChannelParam extends VendorPartnershipParam {

    @ApiModelProperty(value = "渠道商品金额")
    private Long channelItemAmount;

    @ApiModelProperty(value = "渠道类型")
    private Integer channelType;

    @ApiModelProperty(value = "渠道服务ID")
    private Long channelItemId;
}

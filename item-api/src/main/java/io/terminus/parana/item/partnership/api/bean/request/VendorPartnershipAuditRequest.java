package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.partnership.api.bean.request.param.VendorPartnershipParam;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VendorPartnershipAuditRequest extends AbstractRequest {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("审核结果:是否通过")
    private Boolean isPass;

    @Override
    public void checkParam() {
        ParamUtil.nonNull(id, "partnership.id.is.null");
        ParamUtil.nonNull(isPass, "is.pass.is.null");
        ParamUtil.nonNull(getUpdatedBy(), "updated.by.is.null");
    }
}

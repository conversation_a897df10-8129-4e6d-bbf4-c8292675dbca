package io.terminus.parana.item.partnership.events;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.parana.item.common.events.MQReadable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName InventoryLogisticsModeUpdateEvent
 * @Description 物流模式库存变更事件
 * <AUTHOR>
 * @Date 2020-08-14 14:34
 */

@Data
@ApiModel("物流模式库存更新事件")
@NoArgsConstructor
@AllArgsConstructor
public class InventoryLogisticsModeUpdateEvent implements MQReadable {

    public static final String TAG = "MQ_INVENTORY_LOGISTICS_MODE_UPDATE";

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @ApiModelProperty("物流模式")
    private Integer logisticsMode;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @Override
    public String getTag() {
        return TAG;
    }
}

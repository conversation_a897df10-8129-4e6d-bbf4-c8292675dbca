package io.terminus.parana.item.recommend.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.recommend.api.bean.request.param.RecommendDetailParam;
import io.terminus.parana.item.recommend.enums.RecommendOperateType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendUpdateRequest extends AbstractRequest {

    @ApiModelProperty("推荐组id")
    private Long id;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @ApiModelProperty("推荐组名称")
    private String name;

    @ApiModelProperty("推荐组类型")
    private String type;

    @ApiModelProperty("推荐内容的类型")
    private String targetType;

    @ApiModelProperty("系列中的商品")
    private List<RecommendDetailParam> recommendDetailParamList;

    @Override
    public OperationType getOperationType() {
        return RecommendOperateType.UPDATE;
    }

    @Override
    public void checkParam() {
        super.checkParam();

        ParamUtil.nonNull(id, "recommend.id.is.null");
        ParamUtil.nonNull(type, "recommend.type.is.null");
        ParamUtil.nonNull(name, "recommend.name.is.null");
        ParamUtil.nonNull(operatorId, "recommend.operator.id.is.null");
        ParamUtil.nonNull(getTenantId(), "tenant.id.is.null");
        ParamUtil.nonNull(getUpdatedBy(), "updated.by.is.null");
    }
}

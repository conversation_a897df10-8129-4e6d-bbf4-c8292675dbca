package io.terminus.parana.item.footprint.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.footprint.enums.FootprintOperateType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 足迹创建入参
 *
 * <AUTHOR>
 * @since 2019-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FootprintDeleteOnRedisRequest extends AbstractRequest {

    @ApiModelProperty("足迹key集合：key = itemId_operatorId")
    private List<String> targetIdList;

    private Integer targetType;

    private Long userId;

    private Long operatorId;

    private Long currentOperatorId;

    @Override
    public OperationType getOperationType() {
        return FootprintOperateType.CREATE;
    }

    @Override
    public void checkParam() {
        super.checkParam();
        ParamUtil.notEmpty(targetIdList, "target.id.is.empty");
        ParamUtil.nonNull(targetType, "target.type.is.null");
        ParamUtil.nonNull(userId, "user.id.is.null");
        ParamUtil.nonNull(operatorId, "operator.id.is.null");
        ParamUtil.nonNull(getTenantId(), "tenant.id.is.null");
    }
}

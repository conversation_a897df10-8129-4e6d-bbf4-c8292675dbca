package io.terminus.parana.item.recommend.api.bean.response;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.response.info.ApiExtInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendIndexInfo extends ApiExtInfo {

    @ApiModelProperty("推荐组id")
    private Long id;

    @ApiModelProperty("推荐组名称")
    private String name;

    @ApiModelProperty("推荐组类型")
    private String recommendTargetType;

    @ApiModelProperty("推荐目标id")
    private String recommendTargetId;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @ApiModelProperty("创建时间")
    private Date createdAt;

    @ApiModelProperty("更新时间")
    private Date updatedAt;
}

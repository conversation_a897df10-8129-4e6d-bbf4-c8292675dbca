package io.terminus.parana.item.recommend.api.facade;

import io.swagger.annotations.Api;
import io.terminus.common.model.Response;
import io.terminus.parana.item.recommend.api.bean.request.RecommendCreateRequest;
import io.terminus.parana.item.recommend.api.bean.request.RecommendDeleteRequest;
import io.terminus.parana.item.recommend.api.bean.request.RecommendUpdateRequest;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/23
 */
@Api("推荐写服务")
public interface RecommendWriteFacade {

    /**
     * 全量创建推荐组
     *
     * @param request
     * @return
     */
    Response<Long> createRecommend(RecommendCreateRequest request);

    /**
     * 更新推荐组信息（包括推荐组类型改动、推荐组名称改动、商品的增删）
     *
     * @param request
     * @return
     */
    Response<Boolean> updateRecommend(RecommendUpdateRequest request);

    /**
     * 删除推荐组
     *
     * @param request
     * @return
     */
    Response<Boolean> deleteRecommend(RecommendDeleteRequest request);

}

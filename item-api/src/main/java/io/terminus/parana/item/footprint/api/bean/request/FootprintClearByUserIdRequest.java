package io.terminus.parana.item.footprint.api.bean.request;

import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.footprint.enums.FootprintOperateType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 足迹创建入参
 *
 * <AUTHOR>
 * @since 2019-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FootprintClearByUserIdRequest extends AbstractRequest {

    private Long userId;

    private Long operatorId;

    private Long currentOperatorId;

    private List<Integer> targetTypeList;

    @Override
    public OperationType getOperationType() {
        return FootprintOperateType.CREATE;
    }

    @Override
    public void checkParam() {
        super.checkParam();
        ParamUtil.nonNull(userId, "user.id.is.null");
        ParamUtil.notEmpty(targetTypeList, "target.type.list.is.empty");
        ParamUtil.nonNull(getTenantId(), "tenant.id.is.null");
    }
}

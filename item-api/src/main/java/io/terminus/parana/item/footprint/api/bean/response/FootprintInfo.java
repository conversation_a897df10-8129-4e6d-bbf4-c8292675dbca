package io.terminus.parana.item.footprint.api.bean.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
public class FootprintInfo implements Serializable {

    private static final long serialVersionUID = 2648086374441856926L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Integer tenantId;

    /**
     * 目标id
     */
    private Long targetId;

    /**
     * 目标类型
     */
    private Integer targetType;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 扩展字段
     */
    private Map<String, String> extra;

    /**
     * 访问时间
     */
    private Date visitAt;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    private Long operatorId;

    private Long choiceLotLibId;

}

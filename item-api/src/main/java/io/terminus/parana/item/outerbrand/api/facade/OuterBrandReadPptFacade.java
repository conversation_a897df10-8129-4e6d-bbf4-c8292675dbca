package io.terminus.parana.item.outerbrand.api.facade;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.outerbrand.api.bean.request.OuterBrandPageRequest;
import io.terminus.parana.item.outerbrand.api.bean.request.OuterBrandQueryRequest;
import io.terminus.parana.item.outerbrand.api.bean.response.OuterBrandInfoResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OuterBrandReadPptFacade {
    /**
     * 查询外部品牌表
     *
     * @param request
     * @return
     */
    Response<OuterBrandInfoResponse> view(OuterBrandQueryRequest request);

    /**
     * 查询列表外部品牌表
     *
     * @param request
     * @return
     */
    Response<List<OuterBrandInfoResponse>> list(OuterBrandQueryRequest request);

    /**
     * 分页查询列表外部品牌表
     *
     * @param request
     * @return
     */
    Response<Paging<OuterBrandInfoResponse>> page(OuterBrandPageRequest request);
}

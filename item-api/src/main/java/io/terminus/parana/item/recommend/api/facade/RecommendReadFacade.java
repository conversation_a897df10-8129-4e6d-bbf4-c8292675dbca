package io.terminus.parana.item.recommend.api.facade;


import io.swagger.annotations.Api;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.recommend.api.bean.request.RecommendIndexPageRequest;
import io.terminus.parana.item.recommend.api.bean.request.RecommendQueryByIdRequest;
import io.terminus.parana.item.recommend.api.bean.request.RecommendQueryByTargetRequest;
import io.terminus.parana.item.recommend.api.bean.request.RecommendQueryByTypeRequest;
import io.terminus.parana.item.recommend.api.bean.response.RecommendIndexInfo;
import io.terminus.parana.item.recommend.api.bean.response.RecommendInfo;

import java.util.List;

/**
 * 目前只有商品推荐组
 */
@Api("推荐读服务")
public interface RecommendReadFacade {

    /**
     * 根据条件分页查找推荐组信息
     *
     * @param request
     * @return
     */
    Response<Paging<RecommendIndexInfo>> pagingIndex(RecommendIndexPageRequest request);

    /**
     * 根据id查找推荐组
     *
     * @param request
     * @return
     */
    Response<RecommendInfo> findById(RecommendQueryByIdRequest request);

    /**
     * 根据id实时查找推荐组
     *
     * @param request
     * @return
     */
    Response<RecommendInfo> findByIdNoCache(RecommendQueryByIdRequest request);

    /**
     * 通过绑定目标获取推荐组信息，并限制推荐数量
     *
     * @param request
     * @return
     */
    Response<RecommendInfo> findByTargetAndLimitCountAndOperatorId(RecommendQueryByTargetRequest request);

    /**
     * 根据推荐类型，获取推荐组列表
     *
     * @param request
     * @return
     */
    Response<List<RecommendIndexInfo>> findByTypeAndOperatorId(RecommendQueryByTypeRequest request);

    /**
     * 通过绑定对象，查找推荐组基础信息
     *
     * @param request
     * @return
     */
    Response<RecommendIndexInfo> findIndexByBindingTargetAndOperatorId(RecommendQueryByTargetRequest request);
}

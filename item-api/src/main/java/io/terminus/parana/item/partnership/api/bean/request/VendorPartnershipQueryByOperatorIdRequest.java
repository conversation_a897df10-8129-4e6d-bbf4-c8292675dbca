package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import lombok.Data;

/**
 * @author: lql
 * @version: 2020/8/11 12:10 上午
 */
@Data
public class VendorPartnershipQueryByOperatorIdRequest extends AbstractRequest {

    private static final long serialVersionUID = 8141616851054770783L;

    @ApiModelProperty("区域运营id")
    private Long operatorId;
    @Override
    public void checkParam() {
        if (operatorId == null) {
            ParamUtil.nonNull(operatorId, "partnership.operatorId.is.null");
        }
    }

}

package io.terminus.parana.item.footprint.api.bean.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class FullFootPrintDTO implements Serializable {

    private static final long serialVersionUID = 434001621131403017L;

    /**
     * 商品足迹树状结构
     * key：商品浏览日期
     * value：当天商品足迹集合
     */
    private Map<Date, List<FootPrintDTO>> footPrintDTOsWithDate;

}

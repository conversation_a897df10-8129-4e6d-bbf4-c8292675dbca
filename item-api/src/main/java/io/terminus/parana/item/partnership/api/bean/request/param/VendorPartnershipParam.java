package io.terminus.parana.item.partnership.api.bean.request.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 供应商合作关系
 *
 * <AUTHOR>
 */
@Data
public class VendorPartnershipParam implements Serializable {

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @ApiModelProperty("合作模式")
    private Integer cooperationMode;

    @ApiModelProperty("物流模式")
    private Integer logisticsMode;

    @ApiModelProperty(value = "费率")
    private Double feeRate;

    @ApiModelProperty(value = "结算周期")
    private String settlementPeriod;

    //政企业务时 调整供应商属性
    @ApiModelProperty(value = "是否政企业务")
    private String isZq;

    @ApiModelProperty("供应商类型 1-厂家 2-代理商")
    private Integer vendorType;

    @ApiModelProperty(value = "是否是品牌供应商")
    private String isBrand;

    @ApiModelProperty(value = "是否配送")
    private String isDeliery;

    @ApiModelProperty(value = "状态 VendorPartnershipStatus")
    private Integer status;

}

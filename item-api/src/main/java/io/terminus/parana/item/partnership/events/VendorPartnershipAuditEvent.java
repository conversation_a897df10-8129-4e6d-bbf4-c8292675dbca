/*
 * Copyright (c) 2018. 杭州端点网络科技有限公司.  All rights reserved.
 */

package io.terminus.parana.item.partnership.events;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.parana.item.common.events.MQReadable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
@ApiModel("供应商合作关系审核事件")
@NoArgsConstructor
@AllArgsConstructor
public class VendorPartnershipAuditEvent implements MQReadable {

    public static final String TAG = "MQ_VENDOR_PARTNERSHIP_AUDIT";

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @ApiModelProperty("合作模式")
    private Integer cooperationMode;

    @ApiModelProperty("物流模式")
    private Integer logisticsMode;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "审核是否通过")
    private Boolean isPass;

    @Override
    public String getTag() {
        return TAG;
    }
}

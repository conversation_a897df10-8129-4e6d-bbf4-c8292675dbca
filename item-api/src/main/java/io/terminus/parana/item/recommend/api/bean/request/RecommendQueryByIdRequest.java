package io.terminus.parana.item.recommend.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.recommend.enums.RecommendOperateType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendQueryByIdRequest extends AbstractRequest {

    @ApiModelProperty("主键id")
    private Long id;

    @Override
    public OperationType getOperationType() {
        return RecommendOperateType.FIND_BY_ID;
    }

    @Override
    public void checkParam() {
        super.checkParam();
        ParamUtil.nonNull(id, "recommend.id.is.null");
    }
}

package io.terminus.parana.item.footprint.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.footprint.api.bean.request.FootprintClearByUserIdRequest;
import io.terminus.parana.item.footprint.api.bean.request.FootprintCreateOnRedisRequest;
import io.terminus.parana.item.footprint.api.bean.request.FootprintDeleteOnRedisRequest;
import io.terminus.parana.item.footprint.api.bean.request.FootprintPersistRequest;

/**
 * 足迹写服务
 *
 * <AUTHOR>
 */
public interface
FootprintWriteFacade {

    /**
     * 添加商品足迹 访问商详页用
     *
     * @param request
     * @return
     */
    Response<Boolean> createOnRedis(FootprintCreateOnRedisRequest request);

    /**
     * 删除指定商品足迹 足迹页用
     *
     * @param request
     * @return
     */
    Response<Boolean> batchDeleteOnRedis(FootprintDeleteOnRedisRequest request);

    /**
     * 清空足迹 足迹页用
     *
     * @param request
     * @return
     */
    Response<Boolean> clearByUserId(FootprintClearByUserIdRequest request);

    /**
     * 持久化足迹
     *
     * @param request
     * @return
     */
    Response<Boolean> dataPersistToDB(FootprintPersistRequest request);

}

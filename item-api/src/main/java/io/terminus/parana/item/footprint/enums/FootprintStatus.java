package io.terminus.parana.item.footprint.enums;

import com.google.common.base.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 标准状态
 *
 * <AUTHOR>
 * @since 2018-6-6
 */
@Getter
@AllArgsConstructor
public enum FootprintStatus {

    /**
     * 正常状态
     */
    NORMAL(1),

    /**
     * 删除状态
     */
    DELETED(-3);

    private Integer value;

    /**
     * 将整型值转换成为对应的ShopType枚举值
     *
     * @param value 整型值
     * @return 对应的枚举值
     */
    public static FootprintStatus fromValue(int value) {
        for (FootprintStatus generalStatus: values()) {
            if (Objects.equal(generalStatus.value, value)) {
                return generalStatus;
            }
        }
        throw new IllegalArgumentException("unknown general status: " + value);
    }

}

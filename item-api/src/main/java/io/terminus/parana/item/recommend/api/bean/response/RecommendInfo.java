package io.terminus.parana.item.recommend.api.bean.response;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.response.info.ApiExtInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RecommendInfo extends ApiExtInfo {

    @ApiModelProperty("推荐组信息")
    private RecommendIndexInfo itemRecommendIndex;

    @ApiModelProperty("推荐组详情")
    private List<RecommendDetailInfo> itemRecommendDetails;
}

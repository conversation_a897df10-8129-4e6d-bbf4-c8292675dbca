package io.terminus.parana.item.outerbrand.api.bean.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractPageRequest;
import io.terminus.api.request.AbstractRequest;
import io.terminus.parana.item.outerbrand.enums.OuterBrandOperationType;
import lombok.Data;

@Data
@ApiModel(value = "OuterBrandPageRequest", description = "外部品牌表分页查询请求", parent = AbstractRequest.class)
public class OuterBrandPageRequest extends AbstractPageRequest {

    @ApiModelProperty(value = "")
    private Long id;
    @ApiModelProperty(value = "供应商id")
    private Long vendorId;
    @ApiModelProperty(value = "品牌id")
    private Long brandId;
    @ApiModelProperty(value = "品牌名称")
    private String name;
    @ApiModelProperty(value = "品牌图片地址")
    private String logoPath;
    @ApiModelProperty(value = "创建人")
    private Long createdBy;
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createdAt;
    @ApiModelProperty(value = "修改人")
    private String updatedBy;
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updatedAt;

    @Override
    public void checkParam() {
        super.checkParam();
    }

    @Override
    public OperationType getOperationType() {
        return OuterBrandOperationType.OUTER_BRAND_PAGE;
    }
}

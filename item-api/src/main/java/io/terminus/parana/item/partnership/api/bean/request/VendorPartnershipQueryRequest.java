package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class VendorPartnershipQueryRequest extends AbstractRequest {

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @ApiModelProperty("区域运营id")
    private Set<Long> operatorIdSet;

    @ApiModelProperty("合作模式")
    private Integer cooperationMode;

    @ApiModelProperty("物流模式")
    private Integer logisticsMode;

    @ApiModelProperty("仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "状态", notes = "默认查所有状态,需要查指定状态请使用VendorPartnershipStatus指定")
    private Integer status;

    @Override
    public void checkParam() {

    }

}

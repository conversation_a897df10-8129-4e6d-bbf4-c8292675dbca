package io.terminus.parana.item.outerbrand.api.bean.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class OuterBrandInfoResponse implements Serializable {

    @ApiModelProperty(value = "")
    private Long id;
    @ApiModelProperty(value = "供应商id")
    private Long vendorId;
    @ApiModelProperty(value = "品牌id")
    private String brandId;//此处原本是Long，现在改为String，因为前端类型超过了Long的范围不能进行转换
    @ApiModelProperty(value = "品牌名称")
    private String name;
    @ApiModelProperty(value = "品牌图片地址")
    private String logoPath;
    @ApiModelProperty(value = "创建人")
    private Long createdBy;
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createdAt;
    @ApiModelProperty(value = "修改人")
    private Long updatedBy;
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updatedAt;
    @ApiModelProperty(value = "绑定品牌id")
    private Long categoryId;
}

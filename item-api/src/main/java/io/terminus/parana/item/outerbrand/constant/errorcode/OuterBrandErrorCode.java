package io.terminus.parana.item.outerbrand.constant.errorcode;

public interface OuterBrandErrorCode {

    String _IS_BLANK = ".is.blank";
    public static final String ID_IS_BLANK = "id.is.blank";
    public static final String VENDOR_ID_IS_BLANK = "vendor_id.is.blank";
    public static final String BRAND_ID_IS_BLANK = "brand_id.is.blank";
    public static final String NAME_IS_BLANK = "name.is.blank";
    public static final String LOGO_PATH_IS_BLANK = "logo_path.is.blank";
    public static final String CREATED_BY_IS_BLANK = "created_by.is.blank";
    public static final String CREATED_AT_IS_BLANK = "created_at.is.blank";
    public static final String UPDATED_BY_IS_BLANK = "updated_by.is.blank";
    public static final String UPDATED_AT_IS_BLANK = "updated_at.is.blank";
}

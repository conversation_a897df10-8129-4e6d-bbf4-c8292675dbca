package io.terminus.parana.item.partnership.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.partnership.api.bean.request.*;
import io.terminus.parana.item.partnership.api.bean.request.param.VendorPartnershipChannelParam;

/**
 * 供应商合作关系 写服务
 *
 * <AUTHOR>
 */
public interface VendorPartnershipWriteFacade {

    /**
     * 创建供应商合作关系
     *
     * @param request
     * @return
     */
    Response<Boolean> create(VendorPartnershipCreateRequest request);

    Response<Boolean> createForShop(VendorPartnershipChannelParam param);

    /**
     * 区域运营发起的绑定（可不需审核直接绑定成功）
     * @param request
     * @return
     */
    Response<Boolean> binding(VendorPartnershipCreateRequest request);

    /**
     * 区域运营发起的解绑（可不需审核直接解绑）
     * @param request
     * @return
     */
    Response<Boolean> unBinding(VendorPartnershipCreateRequest request);


    /**
     * 申请合作关系
     *
     * @param request
     * @return
     */
    Response<Boolean> apply(VendorPartnershipCreateRequest request);

    /**
     * 删除供应商合作关系
     *
     * @param request
     * @return
     */
    Response<Boolean> delete(VendorPartnershipDeleteRequest request);

    /**
     * 更新供应商合作关系
     *
     * @param request
     * @return
     */
    Response<Boolean> update(VendorPartnershipUpdateRequest request);


    /**
     * 审核
     *
     * @param request
     * @return
     */
    Response<Boolean> audit(VendorPartnershipAuditRequest request);

    /**
     * 再次提审
     */
    Response<Boolean> auditStatus(VendorPartnershipAuditUpdateRequest request);

}

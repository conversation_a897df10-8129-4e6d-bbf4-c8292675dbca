package io.terminus.parana.item.favorites.enums;

import io.terminus.api.consts.OperationType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2019-05-26
 */
@Getter
@AllArgsConstructor
public enum FavoritesOperateType implements OperationType {

    CREATE("创建收藏", true),
    UPDATE("更新收藏", true),
    DELETE("删除收藏", true),
    QUERY("查询收藏", false),
    CHECK("检查收藏情况", false),
    COUNT("统计收藏数量", false);

    private String description;
    private boolean isWrite;

}

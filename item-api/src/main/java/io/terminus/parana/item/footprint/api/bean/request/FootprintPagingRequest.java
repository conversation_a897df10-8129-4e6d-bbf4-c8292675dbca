package io.terminus.parana.item.footprint.api.bean.request;

import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractPageRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.common.dimension.DimensionContext;
import io.terminus.parana.item.footprint.enums.FootprintOperateType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * 足迹查询入参
 *
 * <AUTHOR>
 * @since 2019-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FootprintPagingRequest extends AbstractPageRequest {

    private Long userId;

    private Long operatorId;

    private Long currentOperatorId;

    private Integer targetType;

    private Long referrerId;

    private DimensionContext dimensionContext;

    private Long authId;

    @Override
    public OperationType getOperationType() {
        return FootprintOperateType.QUERY;
    }

    @Override
    public void checkParam() {
        super.checkParam();
        ParamUtil.nonNull(userId, "user.id.is.null");
        ParamUtil.nonNull(targetType, "target.type.is.null");
        ParamUtil.nonNull(getTenantId(), "tenant.id.is.null");
    }
}

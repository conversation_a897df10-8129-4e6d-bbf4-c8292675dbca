package io.terminus.parana.item.footprint.api.facade;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.footprint.api.bean.request.FootprintCountRequest;
import io.terminus.parana.item.footprint.api.bean.request.FootprintPagingRequest;
import io.terminus.parana.item.footprint.api.bean.response.FootprintInfo;

/**
 * 足迹读服务
 *
 * <AUTHOR>
 */
public interface FootprintReadFacade {

    /**
     * 足迹分页
     *
     * @param request
     * @return
     */
    Response<Paging<FootprintInfo>> pagingOnRedis(FootprintPagingRequest request);

    /**
     * 足迹统计
     *
     * @param request
     * @return
     */
    Response<Long> count(FootprintCountRequest request);

}

package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.common.request.AbstractReadRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class PartnershipListByVendorIdsRequest extends AbstractReadRequest {

    private static final long serialVersionUID = 7193185855295909710L;

    @ApiModelProperty("供应商id")
    private List<Long> vendorIdList;

}

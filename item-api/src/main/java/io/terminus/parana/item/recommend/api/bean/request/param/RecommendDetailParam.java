package io.terminus.parana.item.recommend.api.bean.request.param;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.response.info.ApiExtInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendDetailParam extends ApiExtInfo {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("推荐组id")
    private Long recommendId;

    @ApiModelProperty("商品id")
    private Long itemId;

    @ApiModelProperty("区域运营id")
    private Long operatorId;
}

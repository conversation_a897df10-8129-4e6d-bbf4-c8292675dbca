package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.common.request.AbstractPagingRequest;
import lombok.Data;

import java.util.Set;

/**
 * @author: lql
 * @version: 2020/8/26 2:27 下午
 */
@Data
public class PartnershipVendorRequest extends AbstractPagingRequest {

    private static final long serialVersionUID = 6107165515246363832L;

    @ApiModelProperty("id集合")
    private Set<Long> idSet;

    @ApiModelProperty("父ID")
    private Long pid;

    @ApiModelProperty("供应商id")
    private Set<Long> vendorIds;

    @ApiModelProperty("仓库编码")
    private Set<String> wnos;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @ApiModelProperty("合作模式")
    private Integer cooperationMode;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("供应商显示名称")
    private String shopNames;

    @ApiModelProperty("店铺类型")
    private Integer type;

    @ApiModelProperty("店铺名称")
    private String name;

    @ApiModelProperty("区域运营公司名称")
    private String regionalOperationName;

    @Override
    public void checkParam() {
        ParamUtil.nonNull(operatorId, "partnership.operator.id.is.null");
    }

}

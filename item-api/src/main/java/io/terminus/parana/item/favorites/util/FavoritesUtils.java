package io.terminus.parana.item.favorites.util;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.favorites.api.bean.request.FavoritesAddRequest;
import org.springframework.util.StringUtils;

/**
 * 足迹工具类
 *
 * <AUTHOR>
 * @since 2019-07-02
 */
public class FavoritesUtils {

    public static String favoritesIdAssemble(FavoritesAddRequest request) {
        return favoritesIdAssemble(request.getTenantId(), request.getUserId(),
                request.getTargetId(), request.getTargetType(), request.getOperatorId());
    }

    public static String favoritesIdAssemble(Integer tenantId, Long userId,
                                             Long targetId, Integer targetType, Long operatorId) {
        if (tenantId == null || userId == null || targetId == null || targetType == null) {
            throw new ServiceException("favorites.id.assemble.param.illegal");
        }

        return favoritesIdAssemble(tenantId.toString(), userId.toString(),
                targetId.toString(), targetType.toString(), operatorId == null ? "" : operatorId.toString());

    }

    public static String favoritesIdAssemble(String tenantId, String userId,
                                             String targetId, String targetType, String operatorId) {
        if (StringUtils.isEmpty(tenantId)
                || StringUtils.isEmpty(userId)
                || StringUtils.isEmpty(targetId)
                || StringUtils.isEmpty(targetType)
                || StringUtils.isEmpty(operatorId)) {
            throw new ServiceException("favorites.id.assemble.param.illegal");
        }
        StringBuilder idBuilder = new StringBuilder()
                .append(tenantId).append("_")
                .append(userId).append("_")
                .append(targetId).append("_")
                .append(targetType).append("_")
                .append(operatorId);

        return idBuilder.toString();
    }



}

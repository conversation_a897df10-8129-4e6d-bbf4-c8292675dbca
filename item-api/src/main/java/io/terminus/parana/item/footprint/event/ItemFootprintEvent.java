package io.terminus.parana.item.footprint.event;

import io.terminus.parana.item.common.events.MQReadable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019-04-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemFootprintEvent implements MQReadable {

    private static final long serialVersionUID = -4053574206502754506L;

    public static final String TAG = "MQ_ITEM_FOOTPRINT";

    private Long userId;

    private Long itemId;

    private String itemName;

    private String itemImageUrl;

    @Override
    public String getTag() {
        return TAG;
    }
}

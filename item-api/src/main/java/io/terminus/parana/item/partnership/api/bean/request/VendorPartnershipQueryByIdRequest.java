package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VendorPartnershipQueryByIdRequest extends AbstractRequest {

    @ApiModelProperty("合作关系id")
    private Long id;

    @Override
    public void checkParam() {
        ParamUtil.nonNull(id, "partnership.id.is.null");
    }

}

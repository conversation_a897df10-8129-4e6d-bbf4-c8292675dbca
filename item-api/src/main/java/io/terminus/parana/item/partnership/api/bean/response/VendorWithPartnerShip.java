package io.terminus.parana.item.partnership.api.bean.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: lql
 * @version: 2020/8/16 9:28 上午
 */
@Data
public class VendorWithPartnerShip extends VendorPartnershipInfo implements Serializable{

    private static final long serialVersionUID = 6160674251036478460L;

    @ApiModelProperty(value = "名称（供应商/运营商）")
    private String name;

    @ApiModelProperty(value = "联系人")
    private String userName;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "联系地址")
    private String address;


}

package io.terminus.parana.item.footprint.api.bean.request;

import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.footprint.enums.FootprintOperateType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 足迹创建入参
 *
 * <AUTHOR>
 * @since 2019-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FootprintCreateOnRedisRequest extends AbstractRequest {

    private Long userId;

    private Long operatorId;

    private Long currentOperatorId;

    private Long targetId;

    private Integer targetType;

    private Long choiceLotLibId;

    @Override
    public OperationType getOperationType() {
        return FootprintOperateType.CREATE;
    }

    @Override
    public void checkParam() {
        super.checkParam();
        ParamUtil.nonNull(userId, "user.id.is.null");
        ParamUtil.nonNull(operatorId, "operator.id.is.null");
        ParamUtil.nonNull(currentOperatorId, "current.operator.id.is.null");
        ParamUtil.nonNull(targetId, "target.id.is.null");
        ParamUtil.nonNull(targetType, "target.type.is.null");
        ParamUtil.nonNull(getTenantId(), "tenant.id.is.null");
    }
}

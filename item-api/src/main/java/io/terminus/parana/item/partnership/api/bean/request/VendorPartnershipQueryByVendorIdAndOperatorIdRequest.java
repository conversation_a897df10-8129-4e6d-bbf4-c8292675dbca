package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import lombok.Data;

/**
 * @author: lql
 * @version: 2020/8/11 12:10 上午
 */
@Data
public class VendorPartnershipQueryByVendorIdAndOperatorIdRequest extends AbstractRequest {

    private static final long serialVersionUID = 2852408194045653448L;

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @Override
    public void checkParam() {
        if (vendorId == null && operatorId == null) {
            ParamUtil.nonNull(vendorId, "both.partnership.vendorId.and.operatorId.are.null");
        }
    }

}

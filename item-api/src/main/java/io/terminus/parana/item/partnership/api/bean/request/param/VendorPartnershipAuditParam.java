package io.terminus.parana.item.partnership.api.bean.request.param;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * 供应商合作关系
 *
 * <AUTHOR>
 */
@Data
public class VendorPartnershipAuditParam extends AbstractRequest implements Serializable {

    private static final long serialVersionUID = -3802612545824191112L;

    @ApiModelProperty("主键id")
    private Long id;
    @ApiModelProperty(value = "审核结果:是否通过")
    private Boolean isPass;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @Override
    public void checkParam() {
        ParamUtil.nonNull(id, "partnership.id.is.null");
        ParamUtil.nonNull(isPass, "partnership.isPass.is.null");
    }

}

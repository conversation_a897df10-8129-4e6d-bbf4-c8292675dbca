package io.terminus.parana.item.recommend.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendQueryByIdAndLimitCountRequest extends AbstractRequest {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("推荐商品数量限制")
    private Integer count;

    @Override
    public OperationType getOperationType() {
        return null;
    }

    @Override
    public void checkParam() {
        super.checkParam();

        ParamUtil.nonNull(id, "id.is.null");
        ParamUtil.nonNull(count, "limit.count.is.null");
    }
}

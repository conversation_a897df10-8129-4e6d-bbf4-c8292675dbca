package io.terminus.parana.item.recommend.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.recommend.enums.RecommendOperateType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendQueryByTargetRequest extends AbstractRequest {

    @ApiModelProperty("对象类型")
    private String targetType;

    @ApiModelProperty("对象id")
    private Long targetId;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @Override
    public OperationType getOperationType() {
        return RecommendOperateType.FIND_BY_TARGET;
    }

    @Override
    public void checkParam() {
        super.checkParam();

        ParamUtil.nonNull(targetType, "target.type.is.null");
        ParamUtil.nonNull(operatorId, "operator.id.is.null");
    }
}

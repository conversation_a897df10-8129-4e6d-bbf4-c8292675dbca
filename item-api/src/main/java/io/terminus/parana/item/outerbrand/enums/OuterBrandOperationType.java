package io.terminus.parana.item.outerbrand.enums;

import io.terminus.api.consts.OperationType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @date
 */
@Getter
@AllArgsConstructor
public enum OuterBrandOperationType implements OperationType {

    OUTER_BRAND_CREATE("创建", false),
    OUTER_BRAND_UPDATE("修改", false),
    OUTER_BRAND_DELETE("删除", false),
    OUTER_BRAND_QUERY("查询", false),
    OUTER_BRAND_PAGE("分页查询", false);

    private final String description;
    private final boolean write;
}

package io.terminus.parana.item.recommend.api.bean.response;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.response.info.ApiExtInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendDetailInfo extends ApiExtInfo {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("推荐组id")
    private Long recommendId;

    @ApiModelProperty("商品id")
    private Long itemId;

    @ApiModelProperty("商品名")
    private String itemName;

    @ApiModelProperty("商品主图")
    private String mainImage;

    @ApiModelProperty("最低价")
    private Long lowPrice;

    @ApiModelProperty("最高价")
    private Long highPrice;

    @ApiModelProperty("广告语")
    private String advertise;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @ApiModelProperty("创建时间")
    private Date createdAt;

    @ApiModelProperty("更新时间")
    private Date updatedAt;

    @ApiModelProperty("是否有货")
    private Integer inStock;

    @ApiModelProperty("是否是国代商品")
    private Integer isNationwideAgencyItem;

    @ApiModelProperty("最小起购数量")
    private Integer minQuantity;

    @ApiModelProperty("单位：默认'件'")
    private String unit;

    @ApiModelProperty("供应商名称")
    private String vendorName;

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @ApiModelProperty("真实供应商id")
    private Long realVendorId;

//    @ApiModelProperty("商品属性")
//    private Map<String, Map<String, OtherAttributeInfo>> otherAttributeMap;
}

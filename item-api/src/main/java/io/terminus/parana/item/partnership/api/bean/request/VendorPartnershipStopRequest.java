package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.common.request.AbstractPagingRequest;
import lombok.Data;

/**
 * @author: lql
 * @version: 2020/8/4 3:24 下午
 */
@Data
public class VendorPartnershipStopRequest extends AbstractPagingRequest {
    @ApiModelProperty("合作关系id")
    private Long id;

    @Override
    public void checkParam() {
        ParamUtil.nonNull(id, "partnership.param.id.is.null");
    }
}

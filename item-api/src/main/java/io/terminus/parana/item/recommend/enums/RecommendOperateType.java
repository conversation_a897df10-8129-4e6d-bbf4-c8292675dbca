package io.terminus.parana.item.recommend.enums;

import io.terminus.api.consts.OperationType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/24
 */
@Getter
@AllArgsConstructor
public enum RecommendOperateType implements OperationType {

    CREATE("创建推荐组", true),
    CREATE_DETAIL("添加推荐组商品", true),
    UPDATE("更新推荐组", true),
    DELETE("删除推荐组", true),

    FIND_BY_ID("根据id查找", false),
    FIND_BY_TARGET("根据目标查找", false),
    FIND_BY_TYPE_NAME("根据推荐组类型或名称查找", false),
    PAGING("分页", false)
    ;

    private final String description;
    private final boolean write;
}

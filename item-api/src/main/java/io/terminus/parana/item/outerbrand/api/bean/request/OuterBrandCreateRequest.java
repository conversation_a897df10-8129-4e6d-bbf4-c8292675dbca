package io.terminus.parana.item.outerbrand.api.bean.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.outerbrand.constant.errorcode.OuterBrandErrorCode;
import io.terminus.parana.item.outerbrand.enums.OuterBrandOperationType;
import lombok.Data;

@Data
@ApiModel(value = "OuterBrandCreateRequest", description = "外部品牌表创建请求", parent = AbstractRequest.class)
public class OuterBrandCreateRequest extends AbstractRequest {

    @ApiModelProperty(value = "")
    private Long id;
    @ApiModelProperty(value = "供应商id")
    private Long vendorId;
    @ApiModelProperty(value = "品牌id")
    private Long brandId;
    @ApiModelProperty(value = "品牌名称")
    private String name;
    @ApiModelProperty(value = "品牌图片地址")
    private String logoPath;
    @ApiModelProperty(value = "创建人")
    private Long createdBy;
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createdAt;
    @ApiModelProperty(value = "修改人")
    private String updatedBy;
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updatedAt;

    @Override
    public void checkParam() {
        super.checkParam();
        ParamUtil.nonNull(id, OuterBrandErrorCode.ID_IS_BLANK);
        ParamUtil.nonNull(vendorId, OuterBrandErrorCode.VENDOR_ID_IS_BLANK);
        ParamUtil.nonNull(brandId, OuterBrandErrorCode.BRAND_ID_IS_BLANK);
        ParamUtil.notBlank(name, OuterBrandErrorCode.NAME_IS_BLANK);
        ParamUtil.notBlank(logoPath, OuterBrandErrorCode.LOGO_PATH_IS_BLANK);
        ParamUtil.nonNull(createdBy, OuterBrandErrorCode.CREATED_BY_IS_BLANK);
        ParamUtil.nonNull(createdAt, OuterBrandErrorCode.CREATED_AT_IS_BLANK);
        ParamUtil.nonNull(updatedBy, OuterBrandErrorCode.UPDATED_BY_IS_BLANK);
        ParamUtil.nonNull(updatedAt, OuterBrandErrorCode.UPDATED_AT_IS_BLANK);
    }

    @Override
    public OperationType getOperationType() {
        return OuterBrandOperationType.OUTER_BRAND_CREATE;
    }
}

package io.terminus.parana.item.outerbrand.api.bean.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.parana.item.outerbrand.enums.OuterBrandOperationType;
import lombok.Data;

@Data
@ApiModel(value = "OuterBrandQueryRequest", description = "外部品牌表查询请求", parent = AbstractRequest.class)
public class OuterBrandQueryRequest extends AbstractRequest {

    @ApiModelProperty(value = "Id")
    private Long id;

    @Override
    public void checkParam() {
        super.checkParam();
    }

    @Override
    public OperationType getOperationType() {
        return OuterBrandOperationType.OUTER_BRAND_QUERY;
    }
}

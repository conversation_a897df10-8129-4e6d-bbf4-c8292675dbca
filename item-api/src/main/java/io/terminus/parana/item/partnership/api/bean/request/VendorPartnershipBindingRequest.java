package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VendorPartnershipBindingRequest extends AbstractRequest {

    private static final long serialVersionUID = 8704316737264721751L;

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @ApiModelProperty("合作模式")
    private Integer cooperationMode;

    @ApiModelProperty("物流模式")
    private Integer logisticsMode;

    @ApiModelProperty(value = "费率")
    private Double feeRate;

    @ApiModelProperty(value = "结算周期")
    private String settlementPeriod;

    //政企业务时 调整供应商属性
    @ApiModelProperty(value = "是否政企业务")
    private String isZq;

    @ApiModelProperty("供应商类型 1-厂家 2-代理商")
    private Integer vendorType;

    @ApiModelProperty(value = "是否是品牌供应商")
    private String isBrand;

    @ApiModelProperty(value = "是否配送")
    private String isDeliery;

    @Override
    public void checkParam() {
        ParamUtil.nonNull(vendorId, "partnership.vendor.id.is.null");
        ParamUtil.nonNull(cooperationMode, "partnership.cooperationMode.is.null");
        ParamUtil.nonNull(logisticsMode, "partnership.logisticsMode.is.null");
        ParamUtil.nonNull(feeRate, "partnership.feeRate.is.null");
        ParamUtil.nonNull(settlementPeriod, "partnership.settlementPeriod.is.null");
    }
}

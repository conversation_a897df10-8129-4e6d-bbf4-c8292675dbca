package io.terminus.parana.item.recommend.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.recommend.enums.RecommendOperateType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendDeleteRequest extends AbstractRequest {

    @ApiModelProperty("推荐组id")
    private Long id;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @Override
    public OperationType getOperationType() {
        return RecommendOperateType.DELETE;
    }

    @Override
    public void checkParam() {
        super.checkParam();
        ParamUtil.nonNull(id, "recommend.id.is.null");
        ParamUtil.nonNull(operatorId, "recommend.operator.id.is.null");
        ParamUtil.nonNull(getTenantId(), "tenant.id.is.null");
        ParamUtil.nonNull(getUpdatedBy(), "updated.by.is.null");
    }
}

package io.terminus.parana.item.partnership.api.bean.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * @author: lql
 * @version: 2020/8/26 4:41 下午
 */
@Data
public class VendorWithPartnerShipInfo extends VendorPartnershipInfo implements Serializable {

    private static final long serialVersionUID = 6160674251036478460L;

    @ApiModelProperty(value = "名称（供应商/运营商）")
    private String name;

    @ApiModelProperty("区域运营name")
    private String operatorName;

    @ApiModelProperty(value = "联系人")
    private String userName;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "联系地址")
    private String address;

    @ApiModelProperty(value = "销售区域（类型为区域运营时有值）")
    private String salesAreaName;

    @ApiModelProperty(value = "行业id（类型为区域运营时有值）")
    private Long industryId;

    @ApiModelProperty(value = "供应商创建人")
    private String vendorCreatedBy;

    @ApiModelProperty(value = "区域运营创建人")
    private String operatorCreateBy;

    @ApiModelProperty(value = "联系人")
    private String contactName;

    @ApiModelProperty(value = "联系电话")
    private String contactMobile;

    @ApiModelProperty(value = "是否是品牌供应商")
    private String isBrand;

    @ApiModelProperty(value = "是否配送")
    private String isDeliery;

    @ApiModelProperty(value = "是否政企")
    private String isZq;

    @ApiModelProperty(value = "渠道商品金额")
    private Long channelItemAmount;

    @ApiModelProperty(value = "渠道类型")
    private Integer channelType;

    @ApiModelProperty(value = "渠道服务ID")
    private Long channelItemId;

    @ApiModelProperty(value = "供应商显示名称")
    private String shopNames;

    @ApiModelProperty(value = "银行账号")
    private String bankAccount;

    @ApiModelProperty(value = "银行开户名")
    private String bankAccountName;

    @ApiModelProperty(value = "开户银行名称")
    private String bankName;

    @ApiModelProperty(value = "开户支行名称")
    private String bankSubBranchName;

    @ApiModelProperty(value = "社会统一信用代码")
    private String tin;

    @ApiModelProperty(value = "入驻合同期限")
    private String contractPeriodAt;

    @ApiModelProperty(value = "区域运营公司名称")
    private String regionalOperationName;

    private String businessType;

    private String extraJson;

    @ApiModelProperty(value = "结算周期 1周结 2半月结 3月结")
    private Integer billingCycle;

    //申请人
    private String applyName;

    //申请人手机号
    private String applyPhone;

    //法人姓名
    private String legalPersonName;

    //公司类型
    private String enterpriseType;

    //注册资本
    private String registrationCapital;

    //成立日期
    private java.util.Date enterpriseRegistrationTime;

    //企业实际经营地址编码
    private String enterpriseBusinessAddressCode;

    //企业实际经营地址
    private String enterpriseBusinessAddress;

    //合同
    private String contract;

    @ApiModelProperty(value = "类型")
    private Integer settleStatus;

    @ApiModelProperty(value = "申请时间")
    private Date appliedTime;

    @ApiModelProperty(value = "审核状态 0：草稿 1：待审核 2：审核通过 3：审核驳回")
    private Integer auditStatus;

    @ApiModelProperty(value = "驳回理由")
    private String auditOpinion;

    @ApiModelProperty(value = "审核时间")
    private Date auditEndTime;

    private String shopExtraJson;

    private Integer shopStatus;

    @ApiModelProperty(value = "是否展示按钮")
    private Integer accNumberButton;

    /**
     * 品牌商密钥信息
     */
    private Map<String,String> secretConfig;

    @ApiModelProperty(value = "是否外部品牌商 Y 是 N 否")
    private String inType;

    @ApiModelProperty(value = "是否同步SD Y 是 N 否")
    private String syncSd;

    @ApiModelProperty(value = "sd客户流水号")
    private String sdNo;

    @ApiModelProperty(value = "sd客户名称")
    private String sdName;

}

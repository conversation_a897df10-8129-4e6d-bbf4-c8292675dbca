package io.terminus.parana.item.partnership.api.bean.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: lql
 * @version: 2020/8/13 11:28 上午
 */
@Data
@AllArgsConstructor
public class VendorPartnershipListInfo implements Serializable {

    private static final long serialVersionUID = -5141403591995877011L;

    @ApiModelProperty("供应商合作关系")
    private VendorPartnershipInfo vendorPartnershipInfo;

    @ApiModelProperty("区域运营名称")
    private String areaOperatorName;

    @ApiModelProperty("区域商品状态")
    private Integer areaOperatorStatus;


    public VendorPartnershipListInfo() {

    }

}

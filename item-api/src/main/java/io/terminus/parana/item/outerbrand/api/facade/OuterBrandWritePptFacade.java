package io.terminus.parana.item.outerbrand.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.outerbrand.api.bean.request.OuterBrandCreateRequest;
import io.terminus.parana.item.outerbrand.api.bean.request.OuterBrandDeleteRequest;
import io.terminus.parana.item.outerbrand.api.bean.request.OuterBrandUpdateRequest;

public interface OuterBrandWritePptFacade {
    /**
     * 创建外部品牌表
     *
     * @param request
     * @return
     */
    Response<Boolean> create(OuterBrandCreateRequest request);

    /**
     * 修改外部品牌表
     *
     * @param request
     * @return
     */
    Response<Boolean> update(OuterBrandUpdateRequest request);

    /**
     * 修改外部品牌表
     *
     * @param request
     * @return
     */
    Response<Boolean> delete(OuterBrandDeleteRequest request);
}

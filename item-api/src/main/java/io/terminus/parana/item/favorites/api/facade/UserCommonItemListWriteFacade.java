package io.terminus.parana.item.favorites.api.facade;

import io.swagger.annotations.Api;
import io.terminus.common.model.Response;
import io.terminus.parana.item.favorites.api.bean.request.UserCommonItemListAddRequest;

import java.util.List;

@Api("用户常用清单写功能")
public interface UserCommonItemListWriteFacade {

    /**
     * 新增用户常用商品清单
     *
     * @param request
     * @return
     */
    Response<Boolean> add(UserCommonItemListAddRequest request);

    /**
     * 物理删除清单
     * @param userId
     * @param itemId
     * @return
     */
    Response<Boolean> remove(Long userId, List<Long> itemId, Long operatorId, Integer source);

    /**
     * 批量添加
     * @param requests
     * @return
     */
    Response<Boolean> addBatch(List<UserCommonItemListAddRequest> requests);
}

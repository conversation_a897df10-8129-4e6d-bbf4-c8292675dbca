package io.terminus.parana.item.configuration.api.bean.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.configuration.constant.errorcode.ParanaItemConfigurationErrorCode;
import io.terminus.parana.item.configuration.enums.ParanaItemConfigurationOperationType;
import lombok.Data;

@Data
@ApiModel(value = "ParanaItemConfigurationDeleteRequest", description = "配置中心-商品配置表删除请求", parent = AbstractRequest.class)
public class ParanaItemConfigurationDeleteRequest extends AbstractRequest {

	@ApiModelProperty(value = "Id")
	private Long id;

	@Override
	public void checkParam() {
		super.checkParam();
		ParamUtil.nonNull(id, ParanaItemConfigurationErrorCode._IS_BLANK);
	}
	
    @Override
    public OperationType getOperationType() {
        return ParanaItemConfigurationOperationType.PARANA_ITEM_CONFIGURATION_DELETE;
    }
}

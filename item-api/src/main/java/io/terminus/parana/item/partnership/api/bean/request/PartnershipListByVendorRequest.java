package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.common.request.AbstractReadRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: lql
 * @version: 2020/8/11 12:10 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PartnershipListByVendorRequest extends AbstractReadRequest {
    private static final long serialVersionUID = -5312580365361482980L;

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @ApiModelProperty("合作模式 1:底价 2:服务费")
    private Integer cooperationMode;

    @ApiModelProperty("忽略不查询的区域运营ID")
    private Long ignoreOperatorId;

    @Override
    public void checkParam() {
        ParamUtil.nonNull(vendorId, "vendor.id.is.null");
    }
}

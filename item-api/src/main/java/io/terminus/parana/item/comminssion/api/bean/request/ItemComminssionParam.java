package io.terminus.parana.item.comminssion.api.bean.request;

import io.terminus.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = true)
public class ItemComminssionParam extends AbstractRequest {

    private static final long serialVersionUID = 104313187193003093L;


    private Set<Long> itemIds;
    private Long operatorId;
}

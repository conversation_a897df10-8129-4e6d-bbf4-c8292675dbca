package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.parana.item.common.request.AbstractPagingRequest;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VendorPartnershipPagingRequest extends AbstractPagingRequest {

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @ApiModelProperty("合作模式")
    private Integer cooperationMode;

    @ApiModelProperty("物流模式")
    private Integer logisticsMode;

    @ApiModelProperty("仓库code")
    private Long warehouseCode;

    @ApiModelProperty("状态")
    private Integer status;

    @Override
    public void checkParam() {

    }
}

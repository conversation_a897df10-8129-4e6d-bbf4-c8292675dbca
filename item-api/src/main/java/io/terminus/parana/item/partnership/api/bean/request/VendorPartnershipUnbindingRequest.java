package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VendorPartnershipUnbindingRequest extends AbstractRequest {

    private static final long serialVersionUID = -581278516477580318L;

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @Override
    public void checkParam() {
        ParamUtil.nonNull(vendorId, "partnership.vendor.id.is.null");
    }
}

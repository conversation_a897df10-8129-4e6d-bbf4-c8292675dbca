package io.terminus.parana.item.partnership.api.facade;

import io.swagger.annotations.ApiOperation;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.partnership.api.bean.request.*;
import io.terminus.parana.item.partnership.api.bean.response.VendorPartnershipInfo;
import io.terminus.parana.item.partnership.api.bean.response.VendorPartnershipListInfo;
import io.terminus.parana.item.partnership.api.bean.response.VendorWithPartnerShip;
import io.terminus.parana.item.partnership.api.bean.response.VendorWithPartnerShipInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 供应商合作关系 读服务
 *
 * <AUTHOR>
 */
public interface VendorPartnershipReadFacade {

    /**
     * 供应商合作关系分页查询
     *
     * @param request PartnershipVendorListPagingRequest
     * @return
     */
    Response<Paging<VendorWithPartnerShipInfo>> vendorPaging(PartnershipVendorListPagingRequest request);

    /**
     * 区域运营合作关系分页查询
     *
     * @param request PartnershipOperatorListPagingRequest
     * @return
     */

    Response<Paging<VendorWithPartnerShipInfo>> operatorPaging(PartnershipOperatorListPagingRequest request);

    Response<List<VendorWithPartnerShipInfo>> operatorList(PartnershipOperatorListPagingRequest request);

    /**
     * 根据id查询供应商合作关系
     *
     * @param request
     * @return
     */
    Response<VendorPartnershipInfo> queryById(VendorPartnershipQueryByIdRequest request);

    /**
     * 供应商合作关系查询
     *
     * @param request
     * @return
     */
    Response<List<VendorPartnershipInfo>> findByVendorIdOrOperatorId(VendorPartnershipQueryByVendorIdAndOperatorIdRequest request);

    /**
     * 根据供应商id和供应商商品id 查询合作关系(商品列表页面)
     *
     * @param request
     * @return
     */
    Response<List<VendorPartnershipListInfo>> findByVendorIdAndOperatorId(VendorPartnershipQueryListInfoRequest request);

    /**
     * 获取区域与供应商的关系-政企使用
     *
     * @param request
     * @return
     */
    Response<List<VendorPartnershipListInfo>> findByVendorIdAndOperatorIdForZhengQi(VendorPartnershipQueryListInfoRequest request);

    /**
     * 通过ID查询合作关系
     *
     * @param request
     * @return
     */
    Response<VendorWithPartnerShip> queryVendorShipById(VendorPartnershipQueryByIdRequest request);

    /**
     * 通过区供应商id查询有合作关系的域运营ID集合
     *
     * @param request
     * @return
     */
    Response<Set<Long>> findOperatorIdsByVendorId(VendorPartnershipQueryByVendorIdRequest request);

    Response<Set<Long>> findOperatorIdsByVendorIdNotStatus(VendorPartnershipQueryByVendorIdRequest request);

    /**
     * 通过区域运营ID查询有合作关系的供应商id集合
     *
     * @param request
     * @return
     */
    Response<Set<Long>> findVendorIdsByOperatorId(VendorPartnershipQueryByOperatorIdRequest request);

    @ApiOperation("获取供应商的合作关系列表")
    Response<List<VendorPartnershipInfo>> listByVendor(PartnershipListByVendorRequest request);

    @ApiOperation("获取供应商的合作关系列表")
    Response<List<VendorPartnershipInfo>> listByVendorOrCooperationMode(PartnershipListByVendorRequest request);

    @ApiOperation("批量获取供应商的合作关系列表")
    Response<Map<Long, List<VendorPartnershipInfo>>> listByVendorIds(PartnershipListByVendorIdsRequest request);

    @ApiOperation("获取运营商ids")
    Response<List<Long>> listByVendorId(PartnershipListByVendorIdsRequest request);

    Response<Paging<VendorWithPartnerShipInfo>> operatorPagingByParam(PartnershipOperatorListPagingRequest request);

    @ApiOperation("导出全部供应商")
    List<VendorWithPartnerShipInfo> getList(PartnershipVendorListPagingRequest request);

    @ApiOperation("根据区域id跟warehouseCode拿到供应商id")
    Response<List<VendorPartnershipInfo>> findOperatorIdAndWnos(PartnershipVendorRequest request);
}

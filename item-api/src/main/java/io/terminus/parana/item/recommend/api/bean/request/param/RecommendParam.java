package io.terminus.parana.item.recommend.api.bean.request.param;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.param.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendParam extends ApiParam {

    @ApiModelProperty("租户id")
    private Integer tenantId;

    @ApiModelProperty("商品推荐信息")
    private RecommendIndexParam itemRecommendIndexThin;

    @ApiModelProperty("商品推荐详情")
    private List<RecommendDetailParam> itemRecommendDetailThins;
}

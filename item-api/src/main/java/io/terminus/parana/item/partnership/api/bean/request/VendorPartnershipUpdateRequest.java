package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.partnership.api.bean.request.param.VendorPartnershipParam;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VendorPartnershipUpdateRequest extends AbstractRequest {

    @ApiModelProperty("合作关系")
    private VendorPartnershipParam param;

    @Override
    public void checkParam() {
        ParamUtil.nonNull(param, "partnership.param.is.null");
        ParamUtil.nonNull(getUpdatedBy(), "updated.by.is.null");
    }
}

package io.terminus.parana.item.comminssion.api.facade;

import io.swagger.annotations.Api;
import io.terminus.common.model.Response;
import io.terminus.parana.item.comminssion.api.bean.request.ItemComminssionParam;
import io.terminus.parana.item.comminssion.api.bean.response.ItemComminssionInfo;

import java.util.List;

@Api("佣金读服务")
public interface ItemComminssionFacade {

    Response<List<ItemComminssionInfo>> getComminssion(ItemComminssionParam param);
}

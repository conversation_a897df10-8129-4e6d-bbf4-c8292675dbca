package io.terminus.parana.item.footprint.api.bean.request;

import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.footprint.enums.FootprintOperateType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 足迹查询入参
 *
 * <AUTHOR>
 * @since 2019-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FootprintCountRequest extends AbstractRequest {

    private Long userId;

    private Long operatorId;

    private Integer targetType;

    @Override
    public OperationType getOperationType() {
        return FootprintOperateType.COUNT;
    }

    @Override
    public void checkParam() {
        super.checkParam();
        ParamUtil.nonNull(userId, "user.id.is.null");
        ParamUtil.nonNull(operatorId, "operator.id.is.null");
        ParamUtil.nonNull(targetType, "target.type.is.null");
        ParamUtil.nonNull(getTenantId(), "tenant.id.is.null");
    }
}

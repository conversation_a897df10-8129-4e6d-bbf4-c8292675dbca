package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.common.request.AbstractPagingRequest;
import lombok.Data;

import java.util.Set;

/**
 * @author: lql
 * @version: 2020/8/26 2:27 下午
 */
@Data
public class PartnershipVendorListPagingRequest extends AbstractPagingRequest {

    private static final long serialVersionUID = 6107165515246363832L;

    @ApiModelProperty("id集合")
    private Set<Long> idSet;

    @ApiModelProperty("父ID")
    private Long pid;

    @ApiModelProperty("供应商id")
    private Set<Long> vendorIds;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @ApiModelProperty("合作模式")
    private Integer cooperationMode;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("供应商显示名称")
    private String shopNames;

    @ApiModelProperty("店铺类型")
    private Integer type;

    @ApiModelProperty("店铺名称")
    private String name;

    @ApiModelProperty("区域运营公司名称")
    private String regionalOperationName;

    private String statusList;


    @ApiModelProperty(value = "企业简称")
    private String enterpriseNameAbbreviation;

    @ApiModelProperty(value = "企业全称")
    private String enterpriseName;

    @ApiModelProperty(value = "结算周期 1周结 2半月结 3月结")
    private Integer billingCycle;

    @ApiModelProperty(value = "入驻时间起")
    private String settleTimeBefore;

    @ApiModelProperty(value = "入驻时间止")
    private String settleTimeAfter;

    @ApiModelProperty(value = "更新时间起")
    private String updateTimeBefore;

    @ApiModelProperty(value = "更新时间止")
    private String updateTimeAfter;

    @ApiModelProperty(value = "申请时间起")
    private String appliedTimeBefore;

    @ApiModelProperty(value = "申请时间止")
    private String appliedTimeAfter;

    @ApiModelProperty(value = "审核时间起")
    private String auditTimeBefore;

    @ApiModelProperty(value = "审核时间止")
    private String auditTimeAfter;

    @ApiModelProperty(value = "审核状态 0：草稿 1：待审核 2：审核通过 3：审核驳回")
    private Integer auditStatus;

    @ApiModelProperty(value = "入驻类型0：入驻1：修改")
    private Integer settleStatus;

    @ApiModelProperty("品牌商管理 入驻待审核不展示")
    private String manage;

    @ApiModelProperty(value = "是否外部品牌商 Y 是 N 否")
    private String inType;

    @ApiModelProperty(value = "是否同步SD Y 是 N 否")
    private String syncSd;

    @ApiModelProperty(value = "sd客户流水号")
    private String sdNo;

    @ApiModelProperty(value = "sd客户名称")
    private String sdName;

    @Override
    public void checkParam() {
//        ParamUtil.nonNull(operatorId, "partnership.operator.id.is.null");
    }

}

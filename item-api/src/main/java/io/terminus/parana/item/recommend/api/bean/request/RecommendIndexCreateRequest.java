package io.terminus.parana.item.recommend.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.recommend.enums.RecommendOperateType;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR> ymk
 * @date: 2019/7/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendIndexCreateRequest extends AbstractRequest {

    @ApiModelProperty("推荐组名称")
    private String name;

    @ApiModelProperty("推荐组类型")
    private String type;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @ApiModelProperty("推荐组内容，如：商品、店铺")
    private String targetType;

    @Override
    public OperationType getOperationType() {
        return RecommendOperateType.CREATE;
    }

    @Override
    public void checkParam() {
        super.checkParam();

        ParamUtil.nonNull(type, "recommend.type.is.null");
        ParamUtil.nonNull(name, "recommend.name.is.null");
        ParamUtil.nonNull(getTenantId(), "tenantId.by.is.null");
    }
}

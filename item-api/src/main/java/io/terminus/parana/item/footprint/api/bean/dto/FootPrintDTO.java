package io.terminus.parana.item.footprint.api.bean.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;

@Data
public class FootPrintDTO implements Serializable {

    private static final long serialVersionUID = 2648086374441856926L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 目标id
     */
    private Long targetId;

    /**
     * 目标类型
     */
    private Integer targetType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 扩展字段
     */
    private Map<String, String> extra;

    /**
     * 访问时间
     */
    private Date visitAt;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

}

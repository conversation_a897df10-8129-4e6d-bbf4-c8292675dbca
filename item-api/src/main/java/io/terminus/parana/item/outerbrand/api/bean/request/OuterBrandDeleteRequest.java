package io.terminus.parana.item.outerbrand.api.bean.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.outerbrand.constant.errorcode.OuterBrandErrorCode;
import io.terminus.parana.item.outerbrand.enums.OuterBrandOperationType;
import lombok.Data;

@Data
@ApiModel(value = "OuterBrandDeleteRequest", description = "外部品牌表删除请求", parent = AbstractRequest.class)
public class OuterBrandDeleteRequest extends AbstractRequest {

    @ApiModelProperty(value = "Id")
    private Long id;

    @Override
    public void checkParam() {
        super.checkParam();
        ParamUtil.nonNull(id, OuterBrandErrorCode._IS_BLANK);
    }

    @Override
    public OperationType getOperationType() {
        return OuterBrandOperationType.OUTER_BRAND_DELETE;
    }
}

package io.terminus.parana.item.partnership.api.bean.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 供应商合作关系
 *
 * <AUTHOR>
 */
@Data
public class VendorPartnershipInfo implements Serializable {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @ApiModelProperty("合作模式")
    private Integer cooperationMode;

    @ApiModelProperty("物流模式")
    private Integer logisticsMode;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "费率")
    private Double feeRate;

    @ApiModelProperty(value = "结算周期")
    private String settlementPeriod;

    @ApiModelProperty("创建时间")
    private Date createdAt;

    @ApiModelProperty("修改时间")
    private Date updatedAt;

    @ApiModelProperty("更新者id")
    private String updatedBy;

    @ApiModelProperty(value = "渠道商品金额")
    private Long channelItemAmount;

    @ApiModelProperty(value = "渠道类型")
    private Integer channelType;

    @ApiModelProperty(value = "渠道服务ID")
    private Long channelItemId;

    private String extraJson;
}

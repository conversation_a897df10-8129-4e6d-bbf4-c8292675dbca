package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.partnership.api.bean.request.param.VendorPartnershipParam;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VendorPartnershipCreateRequest extends AbstractRequest {

    @ApiModelProperty("合作关系")
    private VendorPartnershipParam param;

    @Override
    public void checkParam() {
        ParamUtil.nonNull(param, "partnership.param.is.null");
        ParamUtil.nonNull(param.getVendorId(), "partnership.param.vendor.id.is.null");
        ParamUtil.nonNull(param.getOperatorId(), "partnership.param.operator.id.is.null");
    }
}

package io.terminus.parana.item.configuration.api.bean.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.parana.item.configuration.enums.ParanaItemConfigurationOperationType;
import lombok.Data;

@Data
@ApiModel(value = "ParanaItemConfigurationCreateRequest", description = "配置中心-商品配置表创建请求", parent = AbstractRequest.class)
public class ParanaItemConfigurationCreateRequest extends AbstractRequest {

	@ApiModelProperty(value = "主键ID")
	private Long id;
	@ApiModelProperty(value = "运营商ID")
	private Long operatorId;
	@ApiModelProperty(value = "运营商名称")
	private String operatorName;
	@ApiModelProperty(value = "配置ID（品牌商/平台）")
	private Long vendorId;
	@ApiModelProperty(value = "配置名称（品牌商/平台）")
	private String vendorName;
	@ApiModelProperty(value = "简称")
	private String shortName;
	@ApiModelProperty(value = "配置类型（1-审核配置；2-品牌入驻配置；3-商品复审配置; 4-运营商供货配置；5-绑定配置）")
	private Integer type;
	@ApiModelProperty(value = "绑定状态（0-冻结；1-正常）")
	private Integer bindingStatus;
	@ApiModelProperty(value = "绑定入口（0-关闭；1-开启）")
	private Integer bindingIntel;
	@ApiModelProperty(value = "绑定平台id集合")
	private String bindingPlatform;
	@ApiModelProperty(value = "发布自动审核（0-关闭；1-开启）")
	private Integer autoItemAudit;
	@ApiModelProperty(value = "修改自动审核（0-关闭；1-开启）")
	private Integer editAutoItemAudit;
	@ApiModelProperty(value = "价格自动审核（0-关闭；1-涨价降价开启; 2-涨价开启; 3-降价开启）")
	private Integer priceAudit;
	@ApiModelProperty(value = "品牌入驻权限（0-关闭；1-开启）")
	private Integer activeRelationshipOperator;
	@ApiModelProperty(value = "发布复审自动审核（0-关闭；1-开启）")
	private Integer platformItemReview;
	@ApiModelProperty(value = "修改复审自动审核（0-关闭；1-开启）")
	private Integer editPlatformItemReview;
	@ApiModelProperty(value = "平台价格审核（0-关闭；1-涨价降价开启; 2-涨价开启; 3-降价开启）")
	private Integer platformPriceAudit;
	@ApiModelProperty(value = "创建时间")
	private java.util.Date createdAt;
	@ApiModelProperty(value = "创建者")
	private String createdBy;
	@ApiModelProperty(value = "最后更新时间")
	private java.util.Date updatedAt;
	@ApiModelProperty(value = "更新者")
	private String updatedBy;

	@Override
	public void checkParam() {
		super.checkParam();
	}

    @Override
    public OperationType getOperationType() {
        return ParanaItemConfigurationOperationType.PARANA_ITEM_CONFIGURATION_CREATE;
    }
}

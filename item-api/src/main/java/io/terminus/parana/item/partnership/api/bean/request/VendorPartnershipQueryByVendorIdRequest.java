package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import lombok.Data;

/**
 * @author: lql
 * @version: 2020/8/11 12:10 上午
 */
@Data
public class VendorPartnershipQueryByVendorIdRequest extends AbstractRequest {

    private static final long serialVersionUID = 1129933706417979179L;

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @Override
    public void checkParam() {
        if (vendorId == null) {
            ParamUtil.nonNull(vendorId, "partnership.vendorId.is.null");
        }
    }

}

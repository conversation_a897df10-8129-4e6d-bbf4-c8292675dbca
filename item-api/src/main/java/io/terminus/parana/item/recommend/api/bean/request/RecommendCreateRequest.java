package io.terminus.parana.item.recommend.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.recommend.api.bean.request.param.RecommendDetailParam;
import io.terminus.parana.item.recommend.api.bean.request.param.RecommendIndexParam;
import io.terminus.parana.item.recommend.enums.RecommendOperateType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> ymk
 * @date: 2019/9/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendCreateRequest extends AbstractRequest {

    @ApiModelProperty("推荐组信息")
    private RecommendIndexParam recommendIndexParam;

    @ApiModelProperty("推荐组详情")
    private List<RecommendDetailParam> recommendDetailParamList;

    @Override
    public OperationType getOperationType() {
        return RecommendOperateType.CREATE;
    }

    @Override
    public void checkParam() {
        super.checkParam();

        ParamUtil.nonNull(recommendIndexParam, "recommend.index.in.null");
        ParamUtil.nonNull(recommendIndexParam.getName(), "recommend.name.is.null");
        ParamUtil.nonNull(recommendIndexParam.getOperatorId(), "recommend.operator.id.is.null");
        ParamUtil.nonNull(getTenantId(), "tenantId.by.is.null");
    }
}

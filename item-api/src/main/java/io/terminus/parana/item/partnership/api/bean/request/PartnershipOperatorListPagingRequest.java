package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.common.request.AbstractPagingRequest;
import lombok.Data;

import java.util.Set;

/**
 * @author: lql
 * @version: 2020/8/26 2:27 下午
 */
@Data
public class PartnershipOperatorListPagingRequest extends AbstractPagingRequest {
    private static final long serialVersionUID = 7194755107627492434L;

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @ApiModelProperty("区域运营id")
    private Set<Long> operatorIds;

    @ApiModelProperty("合作模式")
    private Integer cooperationMode;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("查询商城购买数据")
    private String channelItemIdIsNotNul;

    @ApiModelProperty("忽略不查询的区域运营ID")
    private Long ignoreOperatorId;

    @Override
    public void checkParam() {
        ParamUtil.nonNull(vendorId, "partnership.vendor.id.is.null");
    }

}

package io.terminus.parana.item.recommend.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.common.request.AbstractPagingRequest;
import io.terminus.parana.item.recommend.enums.RecommendOperateType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecommendIndexPageRequest extends AbstractPagingRequest {

    @ApiModelProperty("推荐组类型")
    private String type;

    @ApiModelProperty("推荐组名称")
    private String name;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @Override
    public OperationType getOperationType() {
        return RecommendOperateType.PAGING;
    }

    @Override
    public void checkParam() {
        super.checkParam();
        ParamUtil.nonNull(operatorId, "operator.id.is.null");
    }
}

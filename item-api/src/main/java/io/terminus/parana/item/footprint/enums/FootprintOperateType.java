package io.terminus.parana.item.footprint.enums;

import io.terminus.api.consts.OperationType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2019-05-26
 */
@Getter
@AllArgsConstructor
public enum FootprintOperateType implements OperationType {

    PERSIST("持久化足迹", true),
    CREATE("创建足迹", false),
    UPDATE("更新足迹", false),
    DELETE("删除足迹", false),
    QUERY("查询足迹", false),
    CHECK("检查足迹情况", false),
    COUNT("统计足迹数量", false);

    private String description;
    private boolean isWrite;

}

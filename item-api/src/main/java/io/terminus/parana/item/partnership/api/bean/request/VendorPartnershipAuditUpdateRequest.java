package io.terminus.parana.item.partnership.api.bean.request;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VendorPartnershipAuditUpdateRequest extends AbstractRequest {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("状态")
    private Integer status;



    @Override
    public void checkParam() {
        ParamUtil.nonNull(id, "partnership.id.is.null");
        ParamUtil.nonNull(status, "is.status.is.null");
    }
}

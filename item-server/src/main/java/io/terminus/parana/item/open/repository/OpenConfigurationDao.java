package io.terminus.parana.item.open.repository;

import com.google.common.collect.ImmutableMap;
import io.terminus.parana.item.common.base.AbstractMybatisDao;
import io.terminus.parana.item.open.model.OpenConfiguration;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2019-11-11
 */
@Repository
public class OpenConfigurationDao extends AbstractMybatisDao<OpenConfiguration> {

    public OpenConfiguration queryByEntity(Long entityId, Integer entityType) {
        return getSqlSession().selectOne(sqlId("queryByEntity"),
                ImmutableMap.of("entityId", entityId, "entityType", entityType));
    }

    public OpenConfiguration queryByAppKey(String appKey) {
        return getSqlSession().selectOne(sqlId("queryByAppKey"), appKey);
    }

}

package io.terminus.parana.item.common.filter;

import com.google.common.base.Throwables;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.JsonUtil;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.server.consts.Loggers;
import io.terminus.server.consts.ResultType;
import io.terminus.server.log.model.entity.OperationLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.*;
import org.apache.dubbo.rpc.service.GenericService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-13
 */
@Slf4j
public abstract class AbstractWithSentinelApiAop {

    private ConcurrentMap<String, MethodHolder> methodReflectMap = new ConcurrentHashMap<>();

    // TODO 这里加个缓存，提升下效率
    protected String getResourceName(Invoker<?> invoker, Invocation invocation) {
        StringBuilder buf = new StringBuilder(64);
        buf.append(invoker.getInterface().getName())
                .append(":")
                .append(invocation.getMethodName())
                .append("(");
        boolean isFirst = true;
        for (Class<?> clazz : invocation.getParameterTypes()) {
            if (!isFirst) {
                buf.append(",");
            }
            buf.append(clazz.getName());
            isFirst = false;
        }
        buf.append(")");
        return buf.toString();
    }


    private String buildMethodSignatureKey(ProceedingJoinPoint pjp, String method) {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        return buildMethodSignatureKey(pjp.getTarget().getClass(), method, signature.getParameterTypes());
    }

    private String buildMethodSignatureKey(Class<?> clazz, String name, Class<?>[] paramTypes) {
        Assert.nonNull(clazz);
        StringBuilder sb = new StringBuilder();
        sb.append(clazz.getCanonicalName())
                .append(":")
                .append(name)
                .append("(");
        sb = AssembleDataUtils.join(sb, paramTypes, ",", Class::getCanonicalName);
        sb.append(")");

        return sb.toString();
    }

    /**
     * 解析方法
     *
     * @param pjp       切面
     * @param findSuper 是否寻找父类
     * @return 找到返回方法，否则null
     */
    protected Method resolveMethod(ProceedingJoinPoint pjp, boolean findSuper) {
        return resolveMethod(pjp, findSuper, false);
    }

    private Method resolveMethod(ProceedingJoinPoint pjp, boolean findSuper, boolean cacheIt) {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        String methodName = signature.getName();
        Class<?>[] paramTypes = signature.getParameterTypes();
        Class<?> targetClass = pjp.getTarget().getClass();


        Method method = null;
        if (!findSuper) {
            try {
                method = targetClass.getMethod(methodName, paramTypes);
            } catch (NoSuchMethodException e) {
                // eat it.
            }
        } else {
            method = getDeclaredMethod(targetClass, methodName, paramTypes);
        }

        if (cacheIt) {
            String key = buildMethodSignatureKey(targetClass, methodName, paramTypes);
            if (methodReflectMap.containsKey(key)) {
                throw new RuntimeException("不允许对同一个方法做多次缓存操作");
            }

            synchronized (AbstractWithSentinelApiAop.class) {
                if (!methodReflectMap.containsKey(key)) {
                    MethodHolder mh = MethodHolder.wrap(method);
                    methodReflectMap.put(key, mh);
                }
            }
        }

        return method;
    }


    private Method getDeclaredMethod(Class<?> clazz, String name, Class<?>... parameterTypes) {
        try {
            return clazz.getDeclaredMethod(name, parameterTypes);
        } catch (NoSuchMethodException e) {
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null) {
                return getDeclaredMethod(superClass, name, parameterTypes);
            }
        }
        return null;
    }

    protected Result internalInvoke(Invoker<?> invoker, Invocation invocation) {
        Date start = new Date();
        AbstractRequest request = (AbstractRequest) invocation.getArguments()[0];

        try {
            request.checkParam();
        } catch (Exception e) {
            this.failLog(start, e, request);
            return AsyncRpcResult.newDefaultAsyncResult(Response.fail(e.getMessage()), invocation);
        }

        RequestContext.setRequest(request);

        try {
            Result result = invoker.invoke(invocation);
            if (result.hasException() && GenericService.class != invoker.getInterface()) {
                Throwable exception = result.getException();

                if (!(exception instanceof Exception)) {
                    // 不处理错误，留给ExceptionFilter
                    return result;
                }

                this.failLog(start, (Exception) exception, request);
                if (exception instanceof ServiceException) {
                    return AsyncRpcResult.newDefaultAsyncResult(Response.fail(exception.getMessage()), invocation);
                } else {
                    logInvokeError(invoker, invocation, (Exception) exception);
                    return AsyncRpcResult.newDefaultAsyncResult(Response.fail("item.server.error"), invocation);
                }
            } else {
                // 只在info级别下打印日志，平时不输出
                if (log.isInfoEnabled()) {
                    okLog(start, result.getValue(), request);
                }

                return result;
            }
        } catch (RpcException e) {
            logInvokeError(invoker, invocation, e);
            return AsyncRpcResult.newDefaultAsyncResult(Response.fail("item.server.error"), invocation);
        }
    }

    private void logInvokeError(Invoker<?> invoker, Invocation invocation, Exception e) {
        log.error("[RPC] Fail to invoke service: {}#{} with args: {}, cause: {}", invoker.getInterface(),
                invocation.getMethodName(), invocation.getArguments(), Throwables.getStackTraceAsString(e));
    }

    private void okLog(Date startTime, Object response, AbstractRequest request) {
        this.log(startTime, null, response, request);
    }

    private void failLog(Date startTime, Exception e, AbstractRequest request) {
        this.log(startTime, e, null, request);
    }

    private void log(Date startTime, Exception exception, Object response, AbstractRequest request) {
        Date endTime = new Date();
        OperationLog operationLog = null;

        try {
            operationLog = OperationLog
                    .builder()
                    .startTime(startTime)
                    .endTime(endTime)
                    .costTimeMills(endTime.getTime() - startTime.getTime())
                    .reqId(request.getReqId())
                    .callerIp(request.getCallerIp())
                    .callerType(request.getCallerType())
                    .isWrite(request.getOperationType().isWrite())
                    .operationName(request.getOperationType().name())
                    .request(JsonUtil.getNonIndentJsonString(request))
                    .build();
            if (exception != null) {
                operationLog.setResult(ResultType.FAIL);
                operationLog.setErrorStack(exception.getMessage());
            } else {
                operationLog.setResult(ResultType.OK);
                operationLog.setResponse(JsonUtil.getNonIndentJsonString(response));
            }

            this.doOperationLog(operationLog, request);
        } catch (Exception e) {
            Loggers.Monitor.error("AOP log fail\noperationLog=" + operationLog, e);
        }
    }

    private void doOperationLog(OperationLog operationLog, AbstractRequest request) {
        String logMsg = "\nOperationLog:\n" + operationLog;
        if (ResultType.OK == operationLog.getResult()) {
//            Loggers.Monitor.info(logMsg);
        } else {
//            Loggers.Monitor.warn(logMsg);
        }
    }
}

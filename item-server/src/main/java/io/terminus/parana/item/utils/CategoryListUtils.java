package io.terminus.parana.item.utils;

import io.terminus.parana.common.lang.util.JsonUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2019-07-25 下午4:26
 */
public final class CategoryListUtils {

    public static List getCategoryList(Object categories) {
        if (null == categories) {
            return null;
        }
        if (categories instanceof List) {
            return (List) categories;
        }
        if (categories instanceof String) {
            return JsonUtils.fromJsonArray(categories.toString(), ArrayList.class, Long.class);
        }
        return null;
    }

}

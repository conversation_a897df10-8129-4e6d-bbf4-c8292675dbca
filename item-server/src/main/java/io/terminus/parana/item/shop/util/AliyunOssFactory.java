package io.terminus.parana.item.shop.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Calendar;

/**
 * 阿里云对象存储服务
 *
 * <AUTHOR>
 * @date 2020/03/27
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "item.third-party.storage.provider", havingValue = "aliyun", matchIfMissing = true)
public class AliyunOssFactory{

    @Autowired
    private AcsOssClient ossClient;

    public String uploadFile(String objectName, InputStream is) {
        String bucketName = ossClient.getBucketName();
        ossClient.getInstance().putObject(bucketName, objectName, is);
        log.info("endpoint::::::::::::::::::::::::::"+ossClient.getEndpoint());
        log.info("accessKeyId::::::::::::::::::::::::::"+ossClient.getAccessKeyId());
        log.info("accessKeySecret::::::::::::::::::::::::::"+ossClient.getAccessKeySecret());
        log.info("bucketName::::::::::::::::::::::::::"+ossClient.getBucketName());
        return "https://" + bucketName + "." + ossClient.getEndpoint() + "/" + objectName;
    }

    public String getAccessUrl(String objectName) {
        String bucketName = ossClient.getBucketName();

        // 默认一小时后过期
        Calendar now = Calendar.getInstance();
        now.add(Calendar.HOUR, 1);

        return ossClient.getInstance().generatePresignedUrl(bucketName, objectName, now.getTime()).toString();
    }
}

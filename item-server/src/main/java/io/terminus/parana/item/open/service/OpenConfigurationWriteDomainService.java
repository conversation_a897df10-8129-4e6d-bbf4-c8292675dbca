package io.terminus.parana.item.open.service;

import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.open.cache.CacheOpenConfigurationByAppKey;
import io.terminus.parana.item.open.cache.CacheOpenConfigurationByEntity;
import io.terminus.parana.item.open.model.OpenConfiguration;
import io.terminus.parana.item.open.repository.OpenConfigurationDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OpenConfigurationWriteDomainService extends AbsServiceBase {

    @Autowired
    private OpenConfigurationDao openConfigurationDao;
    @Autowired
    private CacheOpenConfigurationByEntity cacheOpenConfigurationByEntity;
    @Autowired
    private CacheOpenConfigurationByAppKey cacheOpenConfigurationByAppKey;

    public OpenConfiguration initOpenConfiguration(Long entityId, Integer entityType, String updatedBy) {
        OpenConfiguration openConfiguration = setOpenConfiguration(entityId, entityType, updatedBy);
        openConfigurationDao.create(openConfiguration);
        return openConfiguration;
    }

    public OpenConfiguration updateOpenConfiguration(Long entityId, Integer entityType, String updatedBy) {
        OpenConfiguration oldOpenConfiguration = openConfigurationDao.queryByEntity(entityId, entityType);
        if (oldOpenConfiguration == null) {
            throw new ServiceException("open.configuration.not.found");
        }
        OpenConfiguration openConfiguration = setOpenConfiguration(entityId, entityType, updatedBy);
        openConfigurationDao.update(openConfiguration);
        cacheOpenConfigurationByEntity.remove(entityId, entityType);
        cacheOpenConfigurationByAppKey.remove(oldOpenConfiguration.getAppKey());

        return openConfiguration;
    }

    private OpenConfiguration setOpenConfiguration(Long entityId, Integer entityType, String updatedBy) {
        OpenConfiguration openConfiguration = new OpenConfiguration();
        openConfiguration.setEntityId(entityId);
        openConfiguration.setEntityType(entityType);
        openConfiguration.setAppKey(appKey());
        openConfiguration.setAppSecret(appSecret(openConfiguration.getAppKey(), entityId, entityType));
        openConfiguration.setUpdatedBy(updatedBy);
        return openConfiguration;
    }

    private String appKey() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    private String appSecret(String appKey, Long entityId, Integer entityType) {
        return Hashing.md5().newHasher()
                .putString(appKey, Charsets.UTF_8)
                .putLong(entityId)
                .putInt(entityType)
                .hash().toString();
    }
}

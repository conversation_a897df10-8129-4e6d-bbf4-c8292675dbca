package io.terminus.parana.item.common.activity.api.facade.manage;

import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.parana.item.channel.model.Channel;
import io.terminus.parana.item.channel.service.ChannelReadDomainService;
import io.terminus.parana.item.common.activity.api.bean.request.CommonActivityAddRequest;
import io.terminus.parana.item.common.activity.api.bean.request.CommonActivityUpdateRequest;
import io.terminus.parana.item.common.activity.api.bean.request.CommonActivityUpdateStatusRequest;
import io.terminus.parana.item.common.activity.api.facade.CommonActivityWriteFacade;
import io.terminus.parana.item.common.activity.enums.CommonActivityStatus;
import io.terminus.parana.item.common.activity.model.CommonActivityManage;
import io.terminus.parana.item.common.activity.service.CommonActivityChannelBindingService;
import io.terminus.parana.item.common.activity.service.CommonActivityManageService;
import io.terminus.parana.item.common.activity.service.strategy.AbstractCommonActivityStrategy;
import io.terminus.parana.item.common.activity.service.strategy.CommonActivityStrategyChoose;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 常用活动写服务
 *
 * <AUTHOR>
 * @date 2021-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonActivityWriteFacadeImpl implements CommonActivityWriteFacade {

    private final CommonActivityStrategyChoose commonActivityStrategyChoose;
    private final CommonActivityManageService commonActivityManageService;
    private final CommonActivityChannelBindingService commonActivityChannelBindingService;
    private final ChannelReadDomainService channelReadDomainService;

    @Override
    public Response<Boolean> create(CommonActivityAddRequest request) {
        log.info("CommonActivityWriteFacadeImpl add request：{}", request);

        validTime(request.getStartAt(), request.getExpiredAt());

        // 校验渠道活动是否重复
        request.setChannelIds(checkChannelValid(request.getCommonType(), request.getStartAt(), request.getExpiredAt(),
                request.getChannelIds(), request.getOperatorId(), null, false));

        // 创建常用活动
        AbstractCommonActivityStrategy choose = commonActivityStrategyChoose.choose(request.getCommonType());
        return choose.create(request) ? Response.ok(true) : Response.fail("create common activity fail");
    }

    @Override
    public Response<Boolean> update(CommonActivityUpdateRequest request) {
        log.info("CommonActivityWriteFacadeImpl update request：{}", request);

        validTime(request.getStartAt(), request.getExpiredAt());

        CommonActivityManage commonActivityManage = commonActivityManageService.findById(request.getId());
        if(null == commonActivityManage) {
            return Response.fail("数据不存在，请刷新后重试！");
        }

        // 校验渠道活动是否重复
        request.setChannelIds(checkChannelValid(request.getCommonType(), request.getStartAt(), request.getExpiredAt(),
                request.getChannelIds(), request.getOperatorId(), commonActivityManage.getId(), false));

        // 修改常用活动
        AbstractCommonActivityStrategy choose = commonActivityStrategyChoose.choose(request.getCommonType());
        return choose.update(request) ? Response.ok(true) : Response.fail("update common activity fail");
    }

    /**
     * 校验活动起止时间
     * @param startAt 开始时间
     * @param expiredAt 结束时间
     */
    private void validTime(Date startAt, Date expiredAt) {
        if (startAt.before(new Date())) {
            throw new ServiceException("活动开始时间小于当前时间！");
        }
        if (expiredAt.compareTo(startAt) < 1) {
            throw new ServiceException("活动结束时间必须大于开始时间！");
        }
    }

    /**
     * 校验渠道活动是否重复
     * @param commonType 活动类型
     * @param startAt 开始时间
     * @param expiredAt 结束时间
     * @param newChannelIds 渠道IDs
     * @param isChildChannel 传进来的是否是子级渠道，不是则需要获取子级渠道
     * @param id 不需要查询的ID
     */
    private List<Long> checkChannelValid(String commonType, Date startAt, Date expiredAt, List<Long> newChannelIds,
                                   Long operatorId, Long id, boolean isChildChannel) {
        log.info("CommonActivityWriteFacadeImpl.checkChannelValid() newChannelIds：{}, commonActivityId：{}", newChannelIds, id);
        if (CollectionUtils.isEmpty(newChannelIds)) {
            return Collections.emptyList();
        }
        // 查询子级渠道
        if (!isChildChannel) {
            newChannelIds = getValidChildChannel(newChannelIds);
        }
        // 查询该时间范围内的活动
        List<Long> commonActivityIds = commonActivityManageService.checkTimeRangeValidActive(commonType, operatorId, startAt, expiredAt, id);
        if (CollectionUtils.isEmpty(commonActivityIds)) {
            return newChannelIds;
        }
        log.info("该起止时间范围内重复的活动ID有：{}", commonActivityIds);
        // 判断是否有重复的渠道已在该时间范围内关联活动
        for (Long commonActivityId : commonActivityIds) {
            List<Long> copyChannelIds = new ArrayList<>(newChannelIds);
            List<Long> channelIds = commonActivityChannelBindingService.findChannelIdsByCommonRelationId(commonActivityId, operatorId);
            log.info("sourceChannelIds:{}, newChannelIds:{}", channelIds, copyChannelIds);
            // 与入参的渠道ID进行匹配判断是否重复
            if (CollectionUtils.isEmpty(channelIds)) {
                continue;
            }
            copyChannelIds.retainAll(channelIds);
            if (!copyChannelIds.isEmpty()) {
                String channelNames = getChannelNames(copyChannelIds);
                throw new ServiceException(String.format("渠道[%s]在该活动范围内已在其他活动中存在", channelNames));
            }
        }
        return newChannelIds;
    }

    /**
     * 获取有效的子级渠道ID
     *
     * @param channelIds 渠道IDs
     * @return 子即渠道ID
     */
    private List<Long> getValidChildChannel(List<Long> channelIds) {
        List<Long> resultList = new ArrayList<>(10);
        for (Long channelId : channelIds) {
            Channel channel = channelReadDomainService.findById(channelId);
            if (channel == null) {
                throw new ServiceException(String.format("该渠道id{%d}异常，请重新设置", channelId));
            }
            // 获取子级渠道ID
            if (channel.getLevel() != 2) {
                List<Channel> childrenList = channelReadDomainService.findByPid(channelId);
                if (!CollectionUtils.isEmpty(childrenList)) {
                    resultList.addAll(childrenList.stream().map(Channel::getId).collect(Collectors.toList()));
                } else {
                    throw new ServiceException(String.format("操作失败，【%s】没有子级渠道", channel.getName()));
                }
            } else {
                resultList.add(channelId);
            }
        }
        return resultList;
    }

    /**
     * 根据渠道IDs获取渠道名称
     * @param ids 渠道IDs
     * @return 渠道名称
     */
    private String getChannelNames(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return "";
        }
        List<Channel> channels = channelReadDomainService.findByIds(new HashSet<>(ids));
        if (CollectionUtils.isEmpty(channels)) {
            return ids.toString();
        }
        return channels.stream().map(Channel::getName).collect(Collectors.joining(","));
    }

    /**
     * 更新活动状态
     */
    @Override
    public Response<Boolean> updateCommonActivityStatus(CommonActivityUpdateStatusRequest request) {
        log.info("updateCommonActivityStatus param={}", request);
        CommonActivityManage commonActivityManage = commonActivityManageService.findById(request.getId());
        if(null == commonActivityManage) {
            return Response.fail("数据不存在，刷新后重试！");
        }
        List<Long> channelIds = commonActivityChannelBindingService
                .findChannelIdsByCommonRelationId(commonActivityManage.getId(), request.getOperatorId());
        if (request.getStatus().equals(CommonActivityStatus.OPEN.getValue())) {
            if (commonActivityManage.getExpiredAt().compareTo(new Date()) < 1) {
                return Response.fail("当前活动已失效，请重新设置");
            }
            this.checkChannelValid(commonActivityManage.getCommonType(), commonActivityManage.getStartAt(),
                    commonActivityManage.getExpiredAt(), channelIds, request.getOperatorId(), commonActivityManage.getId(), true);
        }
        CommonActivityManage manage = new CommonActivityManage();
        manage.setId(request.getId());
        manage.setStatus(request.getStatus());
        return Response.ok(commonActivityManageService.updateCommonActivityStatus(manage));
    }
}

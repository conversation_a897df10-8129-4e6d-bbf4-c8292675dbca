package io.terminus.parana.item.common.flow;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.enums.ConditionReadable;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 模型状态流的抽象类，用来更好的完成代码编写
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2018-11-28
 */
public abstract class AbstractStatusFlowResolve implements StatusFlowResolve, StatusFlow {

    @Override
    public Integer nextStatus(Class<?> clazz, Integer currentStatus, Integer expectStatus, Object... args) {
        if (expectStatus != null) {
            if (!available(clazz, currentStatus, expectStatus, args)) {
                throw new IllegalArgumentException("预期状态不允许");
            } else {
                return expectStatus;
            }
        }

        List<Integer> available = availableStatus(currentStatus);

        if (CollectionUtils.isEmpty(available)) {
            throw new ServiceException("当前状态无法再进行变更");
        }

        if (available.size() > 1) {
            throw new ServiceException("下一个状态非唯一，请手动指定");
        }

        return available.iterator().next();
    }

    /**
     * 转换枚举为编码集合
     *
     * @param conditions 枚举集合
     * @return 编码集合
     */
    protected List<Integer> toCodeList(ConditionReadable... conditions) {
        if (conditions.length == 0) {
            return Collections.emptyList();
        }

        return Arrays.stream(conditions).map(ConditionReadable::getValue).collect(Collectors.toList());
    }
}

package io.terminus.parana.item.shop.util;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 阿里云服务客户端
 *
 * <AUTHOR>
 * @date 2020/03/09
 */
@Data
@Component
@ConfigurationProperties(prefix = "item.third-party.client.aliyuncs")
public class AcsClient {

    private String regionId;

    private String accessKeyId;

    private String accessKeySecret;

    private IAcsClient acsClient;

    public IAcsClient getInstance(){
        if (acsClient == null) {
            IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
            acsClient = new DefaultAcsClient(profile);
        }

        return acsClient;
    }

}

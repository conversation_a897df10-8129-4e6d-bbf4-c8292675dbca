package io.terminus.parana.item.shop.manager;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.partnership.repository.VendorPartnershipDao;
import io.terminus.parana.item.shop.bo.ShopCreateBO;
import io.terminus.parana.item.shop.bo.ShopUpdateBO;
import io.terminus.parana.item.shop.enums.ShopType;
import io.terminus.parana.item.shop.repository.ShopDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopTransactionManager {

    private final ShopDao shopDao;
    private final VendorPartnershipDao vendorPartnershipDao;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Boolean create(ShopCreateBO shopCreateBO) {
        log.info("shopDao.create{}",shopCreateBO.getShop());
        shopDao.create(shopCreateBO.getShop());

        if (shopCreateBO.getShop().getType().equals(ShopType.VENDOR.getValue())) {
            boolean vendorCreateResult = vendorPartnershipDao.create(shopCreateBO.getVendorPartnership());
            if (!vendorCreateResult) {
                log.error("vendor partnership create failed, param:{}", shopCreateBO.getVendorPartnership());
                throw new ServiceException("vendor.partnership.create.failed");
            }
        }

        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Boolean update(ShopUpdateBO shopUpdateBO) {

        shopDao.update(shopUpdateBO.getShop());
        if (shopUpdateBO.getShop().getType().equals(ShopType.VENDOR.getValue())) {
            boolean vendorCreateResult = vendorPartnershipDao.update(shopUpdateBO.getVendorPartnership());
            if (!vendorCreateResult) {
                log.error("vendor partnership create failed, param:{}", shopUpdateBO.getVendorPartnership());
                throw new ServiceException("vendor.partnership.update.failed");
            }
        }


        return Boolean.TRUE;
    }


}

package io.terminus.parana.item.shop.api.facade;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.category.api.bean.response.BackCategoryInfo;
import io.terminus.parana.item.common.converter.GeneralConverter;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.industry.api.bean.request.ShopQueryByIndustryRequest;
import io.terminus.parana.item.partnership.api.bean.request.PartnershipVendorListPagingRequest;
import io.terminus.parana.item.partnership.api.bean.response.VendorWithPartnerShipInfo;
import io.terminus.parana.item.partnership.service.VendorPartnershipReadDomainService;
import io.terminus.parana.item.shop.api.bean.request.*;
import io.terminus.parana.item.shop.api.bean.request.param.ShopQueryStatusByIdRequest;
import io.terminus.parana.item.shop.api.bean.response.SdCustomerDimensionInfo;
import io.terminus.parana.item.shop.api.bean.response.ShopInfo;
import io.terminus.parana.item.shop.api.bean.response.ShopTreeInfo;
import io.terminus.parana.item.shop.api.converter.ShopApiInfoConverter;
import io.terminus.parana.item.shop.api.converter.ShopTreeApiInfoConverter;
import io.terminus.parana.item.shop.api.converter.SupplierInfoApiConverter;
import io.terminus.parana.item.shop.app.ShopApp;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.model.SupplierInfoModel;
import io.terminus.parana.item.shop.service.ShopReadDomainService;
import io.terminus.parana.item.shop.service.SupplierInfoReadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-02-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopReadFacadeImpl implements ShopReadFacade {

    private final ShopApp shopApp;
    private final ShopApiInfoConverter shopApiInfoConverter;
    private final ShopTreeApiInfoConverter shopTreeApiInfoConverter;
    private final ShopReadDomainService shopReadDomainService;
    private final VendorPartnershipReadDomainService vendorPartnershipReadDomainService;
    private final SupplierInfoReadService supplierInfoReadService;
    private final SupplierInfoApiConverter supplierInfoApiConverter;
    @Value("${tongka.vendorId:1100871001}")
    private Long vendorId;

    @Override
    public Response<Paging<ShopInfo>> page(ShopPagingRequest request) {
        Map<String, Object> params = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(request.getIdSet())) {
            params.put("idSet", request.getIdSet());
        }

        if (ObjectUtil.isNotNull(request.getBillingCycle())){
            SupplierInfoQueryListRequest supplierInfoQueryListRequest = new SupplierInfoQueryListRequest();
            if (!CollectionUtils.isEmpty(request.getIdSet())) {
                supplierInfoQueryListRequest.setIds(new ArrayList<>(request.getIdSet()));
            }
            supplierInfoQueryListRequest.setTenantId(request.getTenantId());
            supplierInfoQueryListRequest.setBillingCycle(request.getBillingCycle());
            List<SupplierInfoModel> supplierInfoModelList = supplierInfoReadService.listByRequest(supplierInfoQueryListRequest);
            if (CollectionUtils.isEmpty(supplierInfoModelList)){
                return Response.ok(new Paging<ShopInfo>());
            }
            params.put("idSet", supplierInfoModelList.stream().map(SupplierInfoModel::getId).collect(Collectors.toList()));
        }


        params.put("tenantId", request.getTenantId());
//        params.put("operatorId", request.getOperationId());
        params.put("outerId", request.getOuterId());
        params.put("name", request.getName());
        params.put("shopNames", request.getShopNames());
        params.put("userId", request.getUserId());
        params.put("type", request.getType());
        params.put("status", request.getStatus());
        params.put("isZq", request.getIsZq());
        params.put("sort", request.getSort());
        params.put("pageNo", request.getPageNo());
        params.put("pageSize", request.getPageSize());
        params.put("isFilterPlatform", request.getIsFilterPlatform());
        params.put("regionalOperationName", request.getRegionalOperationName());
        if (request.getPid() != null) {
            params.put("pidStr", "," + request.getPid() + ",");
        }
        params.put("excludeIds", request.getExcludeIds());
        params.put("statusNo",request.getStatusNo());

        params.put("startCreatedAt", request.getStartCreatedAt());
        params.put("endCreatedAt",request.getEndCreatedAt());

        //是否末级区域标识
        String isLastLevel="N";
        if(!StringUtils.isEmpty(request.getIsLastLevel())) {
            isLastLevel=request.getIsLastLevel();
        }
        params.put("isLastLevel", isLastLevel);
        log.info("params:{}",params);
        Paging<Shop> pagination = shopReadDomainService.paging(params);
        List<Shop> shopList = pagination.getData();
        List<ShopInfo> infoList = Lists.newArrayList();
        for (Shop shop : shopList) {
            ShopInfo info = shopApiInfoConverter.domain2info(shop);
            if (shop.getExtra() != null && shop.getExtra().containsKey("salesAreas")) {
                String json = shop.getExtra().get("salesAreas");
                List<Map<String, String>> salesArea = JSON.parseObject(json, new TypeReference<List<Map<String, String>>>() {});
                info.setSalesAreas(salesArea);
            }
            if (shop.getExtra() != null && shop.getExtra().containsKey("noSalesAreas")) {
                String json = shop.getExtra().get("noSalesAreas");
                List<Map<String, String>> noSalesArea = JSON.parseObject(json, new TypeReference<List<Map<String, String>>>() {});
                info.setNoSalesAreas(noSalesArea);
            }
            infoList.add(info);
        }
        setSupplierInfoInfo(infoList);
        return Response.ok(new Paging<>(pagination.getTotal(), infoList, request.getPageSize()));
    }

    private void  setSupplierInfoInfo(List<ShopInfo> shopInfoList){
        if (!CollectionUtils.isEmpty(shopInfoList)) {
            List<Long> shopIdList = shopInfoList.stream().map(ShopInfo::getId).collect(Collectors.toList());
            SupplierInfoQueryListRequest supplierInfoQueryListRequest = new SupplierInfoQueryListRequest();
            supplierInfoQueryListRequest.setIds(shopIdList);
            supplierInfoQueryListRequest.setTenantId(shopInfoList.get(0).getTenantId());
            List<SupplierInfoModel> supplierInfoModelList = supplierInfoReadService.list(supplierInfoQueryListRequest);
            Map<Long, SupplierInfoModel> supplierInfoModelMap = supplierInfoModelList.stream().collect(Collectors.toMap(SupplierInfoModel::getId, Function.identity()));
            shopInfoList.stream().forEach(shopInfo -> {
                SupplierInfoModel supplierInfoModel = supplierInfoModelMap.get(shopInfo.getId());
                shopInfo.setSupplierInfoInfoResponse(supplierInfoApiConverter.model2InfoResponse(supplierInfoModel));
            });
        }
    }

    @Override
    public Response<List<ShopInfo>> list(ShopPagingRequest request) {
        Map<String, Object> params = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(request.getIdSet())) {
            params.put("idSet", request.getIdSet());
        }
        params.put("tenantId", request.getTenantId());
        params.put("outerId", request.getOuterId());
        params.put("name", request.getName());
        params.put("userId", request.getUserId());
        params.put("type", request.getType());
        params.put("status", request.getStatus());
        params.put("sort", request.getSort());
        params.put("appId", request.getAppId());
        params.put("statusNo",request.getStatusNo());
        //是否末级区域标识
        String isLastLevel="N";
        if(!StringUtils.isEmpty(request.getIsLastLevel())) {
            isLastLevel=request.getIsLastLevel();
        }
        params.put("isLastLevel", isLastLevel);
        params.put("shopNames", request.getShopNames());
        List<Shop> list = shopReadDomainService.list(params);
        log.info("shopList:{}",list);
        return Response.ok(GeneralConverter.batchConvert(list, shopApiInfoConverter::domain2info));
    }

    @Override
    public Response<ShopInfo> querySingleById(ShopSingleQueryByIdRequest request) {
        log.info("querySingleById=====================");
        Shop shop = shopReadDomainService.findById(request.getId(), request.getTenantId(),null);
        ShopInfo shopInfo = shopApiInfoConverter.domain2info(shop);
        if (shop.getExtra() != null && shop.getExtra().containsKey("salesAreas")) {
            String json = shop.getExtra().get("salesAreas");
            List<Map<String, String>> salesArea = JSON.parseObject(json, new TypeReference<List<Map<String, String>>>() {});
            shopInfo.setSalesAreas(salesArea);
        }
        if (shop.getExtra() != null && shop.getExtra().containsKey("noSalesAreas")) {
            String json = shop.getExtra().get("noSalesAreas");
            List<Map<String, String>> noSalesArea = JSON.parseObject(json, new TypeReference<List<Map<String, String>>>() {});
            shopInfo.setNoSalesAreas(noSalesArea);
        }
        if(!StringUtils.isEmpty(shop.getExtraJson())){
            String extraJson = shop.getExtraJson();
            shopInfo.setExtra(JSON.parseObject(extraJson,new TypeReference<Map<String,String>>(){}));
        }
        setSupplierInfoInfo(Arrays.asList(shopInfo));
        if(request.getId().equals(vendorId)){
            shopInfo.setIfTongKa("Y");
        }else{
            shopInfo.setIfTongKa("N");
        }
        return Response.ok(shopInfo);
    }

    @Override
    public Response<List<ShopInfo>> queryById(ShopQueryByIdRequest request) {
        List<Shop> shops = shopReadDomainService.findByIds(Lists.newArrayList(request.getIdSet()));
        return Response.ok(GeneralConverter.batchConvert(shops, shopApiInfoConverter::domain2info));
    }

    @Override
    public Response<ShopInfo> queryByOuterId(ShopQueryByOuterIdRequest request) {
        Shop shop = shopReadDomainService.findByOuterId(request.getOuterId(), request.getTenantId());
        return Response.ok(shopApiInfoConverter.domain2info(shop));
    }

    @Override
    public Response<ShopInfo> queryByUserId(ShopQueryByUserIdRequest request) {
        Shop shop = shopReadDomainService.findByUserId(request.getUserId(), request.getTenantId());
        return Response.ok(shopApiInfoConverter.domain2info(shop));
    }

    @Override
    public Response<ShopInfo> queryByName(ShopQueryByNameRequest request) {
        Shop shop = shopReadDomainService.findByName(request.getName(), request.getType(), request.getTenantId());
        return Response.ok(shopApiInfoConverter.domain2info(shop));
    }

    @Override
    public Response<Boolean> checkNameAvailable(ShopCheckNameRequest request) {
        return Response.ok(shopReadDomainService.checkNameAvailable(
                request.getId(), request.getName(), request.getType(), request.getTenantId()));
    }

    @Override
    public Response<List<ShopInfo>> findShopByFuzzName(FindShopByFuzzNameRequest request) {
        List<Long> result = null;
        if (request.getType()==2){
            ShopListQueryByIdRequest shopListQueryByIdRequest = new ShopListQueryByIdRequest();
            shopListQueryByIdRequest.setTenantId(request.getTenantId());
            shopListQueryByIdRequest.setId(request.getId());
            Response<List<Long>> listResponse = queryChildShopIdListById(shopListQueryByIdRequest);
            result = listResponse.getResult();
        }
        List<Shop> shops = shopReadDomainService.findByFuzzName(result,
                request.getName(), request.getType(), request.getTenantId(), request.getSize());
        return Response.ok(GeneralConverter.batchConvert(shops, shopApiInfoConverter::domain2info));
    }

    @Override
    public Response<List<ShopInfo>> findShopByFuzzNameFilterByIds(FindShopByFuzzNameFilterByIdsRequest request) {
        List<Shop> shops = shopReadDomainService.findByFuzzNameFilterByIds(
                request.getId(),request.getCooperationMode(), request.getName(), request.getType(), request.getTenantId(), request.getSize(), request.getIgnoreOperatorId());
        return Response.ok(GeneralConverter.batchConvert(shops, shopApiInfoConverter::domain2info));
    }

    @Override
    public Response<List<ShopInfo>> findShopByFuzzNameAllFilterByIds(FindShopByFuzzNameFilterByIdsRequest request) {
        ShopListQueryByIdRequest shopListQueryByIdRequest = new ShopListQueryByIdRequest();
        shopListQueryByIdRequest.setTenantId(request.getTenantId());
        shopListQueryByIdRequest.setId(request.getId());
        Response<List<Long>> listResponse = queryChildShopIdListById(shopListQueryByIdRequest);
        List<Long> result = listResponse.getResult();
        List<Shop> shops = shopReadDomainService.findByFuzzNameAllFilterByIds(result,
                request.getId(),request.getCooperationMode(), request.getName(), request.getType(), request.getTenantId(), request.getSize(), request.getIgnoreOperatorId());
        return Response.ok(GeneralConverter.batchConvert(shops, shopApiInfoConverter::domain2info));
    }

    @Override
    public Response<List<BackCategoryInfo>> renderAuthCategory(ShopRenderAuthCategoryRequest request) {
        return Response.ok(shopApp.renderShopAuthCategory(request.getShopId(), request.getPid(), request.getExtensionType()));
    }

    @Override
    public Response<List<Long>> queryAuthCategory(ShopQueryAuthCategoryRequest request) {
        return Response.ok(shopReadDomainService.queryAuthCategoryId(request.getShopId()));
    }

    @Override
    public Response<ShopInfo> querySingleVendorByPartnerShipId(VendorSingleQueryByVendorShipIdRequest request) {

        return Response.ok(shopApp.findVendorPartnershipInfo(request.getId()));
    }

    @Override
    public Response<List<ShopTreeInfo>> queryTreeShop(ShopTreeQueryRequest request) {
        Long operatorId= request.getId();
        List<Shop> shopList = shopReadDomainService.findTreeShopList(request.getTenantId());

        List<ShopTreeInfo> rootList=new ArrayList<ShopTreeInfo>();//所有根节点
        List<ShopTreeInfo> otherList =new ArrayList<ShopTreeInfo>();//其他节点
        shopList.stream().forEach(shop ->{
            ShopTreeInfo shopTreeInfo=shopTreeApiInfoConverter.DO2Domain(shop);
            String pid=shopTreeInfo.getPid();
            String id= shopTreeInfo.getId();

            if(operatorId!=null){
                if(id.equals(String.valueOf(operatorId))){
                    rootList.add(shopTreeInfo);
                }else{
                    otherList.add(shopTreeInfo);
                }
            }else if("0".equals(pid)){
                rootList.add(shopTreeInfo);
            }else{
                otherList.add(shopTreeInfo);
            }
        });

        if(otherList != null && !otherList.isEmpty()){
            Map<String,String> map = Maps.newHashMapWithExpectedSize(otherList.size());
            rootList.forEach(shopTreeInfo -> getChild(shopTreeInfo,map,otherList));//传递根对象和一个空map
        }
        return Response.ok(rootList);
    }

    public void getChild(ShopTreeInfo treeInfo,Map<String,String> map,List<ShopTreeInfo> otherList){
        List<ShopTreeInfo> childList = Lists.newArrayList();
        otherList.stream()
                .filter(otherInfo -> !map.containsKey(otherInfo.getId()))
                .filter(otherInfo ->otherInfo.getPid().equals(treeInfo.getId()))
                .forEach(otherInfo ->{
                    map.put(otherInfo.getId(),otherInfo.getPid());
                    getChild(otherInfo,map,otherList);//递归调用
                    childList.add(otherInfo);
                });
        treeInfo.setChildren(childList);
    }

    public void getChild(ShopTreeInfo treeInfo,Map<String,String> map,List<ShopTreeInfo> otherList, List<Long> shopIds){
        shopIds.add(Long.valueOf(treeInfo.getId()));
        otherList.stream()
                .filter(otherInfo -> !map.containsKey(otherInfo.getId()))
                .filter(otherInfo ->otherInfo.getPid().equals(treeInfo.getId()))
                .forEach(otherInfo ->{
                    map.put(otherInfo.getId(),otherInfo.getPid());
                    getChild(otherInfo,map,otherList, shopIds);//递归调用
                    shopIds.add(Long.valueOf(otherInfo.getId()));
                });
    }

    @Override
    public Response<List<ShopInfo>> getExistCityList(ShopQueryByCityRequest request) {
        List<Shop> shops = shopReadDomainService.getExistCityList(request.getTenantId(),null,null,request.getCity(),request.getType());
        return Response.ok(GeneralConverter.batchConvert(shops, shopApiInfoConverter::domain2info));
    }

    @Override
    public Response<List<Long>> queryLastLevelShopListById(ShopListQueryByIdRequest request) {
        List<Long> childrenList=new ArrayList<Long>();//叶子节点

        Long operatorId= request.getId();//传入区域ID
        if(operatorId!=null) {
            List<Shop> shopList = shopReadDomainService.findTreeShopList(request.getTenantId());

            List<Long> rootList = new ArrayList<Long>();//所有根节点
            List<Shop> otherList = new ArrayList<Shop>();//其他节点
            shopList.stream().forEach(shop -> {
                Long shopId = shop.getId();
                if (shopId.longValue() == operatorId.longValue()) {
                    rootList.add(shopId);
                } else {
                    otherList.add(shop);
                }
            });

            log.info("------------rootList:"+ JSON.toJSONString(rootList));
            log.info("------------otherList:"+ JSON.toJSONString(otherList));

            //获取最末级节点的父级ID
            Map<Long, Long> map = Maps.newHashMapWithExpectedSize(otherList.size());
            List<Long> pIdList = new ArrayList<Long>();
            rootList.forEach(id -> {
                getChild(id, map, otherList, pIdList);
            });
            log.info("------------pIdList:"+ JSON.toJSONString(pIdList));

            if (!pIdList.isEmpty()) {
                Long lastPid = pIdList.get(0);
                for (Map.Entry<Long, Long> entry : map.entrySet()) {
                    Long id = entry.getKey();
                    Long pid = entry.getValue();
                    if (lastPid.longValue() == pid.longValue()) {
                        childrenList.add(id);
                    }
                }
            }
            log.info("------------childrenList:"+ JSON.toJSONString(childrenList));

        }
        return Response.ok(childrenList);
    }

    @Override
    public Response<List<ShopInfo>> queryLastLevelListByOperatorId(ShopListQueryByIdRequest request) {

        Response<List<Long>> queryResp = queryLastLevelShopListById(request);
        List<Long> operatoridsList = Assert.take(queryResp);

        List<ShopInfo> result = new ArrayList<>();
        if(null!= operatoridsList && !operatoridsList.isEmpty()){
            List<Shop> shops = shopReadDomainService.findByIds(operatoridsList);
            result = GeneralConverter.batchConvert(shops, shopApiInfoConverter::domain2info);
        }
        return Response.ok(result);
    }

    @Override
    public Response<List<Long>> queryChildShopIdListById(ShopListQueryByIdRequest request) {

        Long operatorId= request.getId();
        List<Shop> shopList = shopReadDomainService.findTreeShopList(request.getTenantId());
        List<Long> childShopIds = new ArrayList<>();
        List<ShopTreeInfo> rootList=new ArrayList<ShopTreeInfo>();//所有根节点
        List<ShopTreeInfo> otherList =new ArrayList<ShopTreeInfo>();//其他节点
        shopList.stream().forEach(shop ->{
            ShopTreeInfo shopTreeInfo=shopTreeApiInfoConverter.DO2Domain(shop);
            String pid=shopTreeInfo.getPid();
            String id= shopTreeInfo.getId();

            if(operatorId!=null){
                if(id.equals(String.valueOf(operatorId))){
                    rootList.add(shopTreeInfo);
                }else{
                    otherList.add(shopTreeInfo);
                }
            }else if("0".equals(pid)){
                rootList.add(shopTreeInfo);
            }else{
                otherList.add(shopTreeInfo);
            }
        });

        if(otherList != null && !otherList.isEmpty()){
            Map<String,String> map = Maps.newHashMapWithExpectedSize(otherList.size());
            rootList.forEach(shopTreeInfo -> getChild(shopTreeInfo,map,otherList, childShopIds));//传递根对象和一个空map
        }
        return Response.ok(childShopIds);
    }

    public void getChild(Long id,Map<Long,Long> map,List<Shop> otherList,List<Long> pidList){
        otherList.stream()
                .filter(otherInfo -> !map.containsKey(otherInfo.getId()))
                .filter(otherInfo ->otherInfo.getPid().equals(id))
                .forEach(otherInfo ->{
                    map.put(otherInfo.getId(),otherInfo.getPid());
                    getChild(otherInfo.getId(),map,otherList,pidList);//递归调用
                    pidList.add(otherInfo.getPid());
                });
    }

    @Override
    public Response<Long> queryOperatorIdByDomainAndAddress(ShopOperatorIdQueryByUrlRequest request) {
        return Response.ok(shopReadDomainService.queryOperatorIdByDomainAndAddress(request.getUrl(),request.getAddress()));
    }

    @Override
    public Response<List<ShopInfo>> queryOperatorIdByDomainAndAddressV2(ShopOperatorIdQueryByUrlRequest request) {
        return Response.ok(shopApiInfoConverter.listdomain2info(shopReadDomainService.queryOperatorIdByDomainAndAddressV2(request.getUrl(),request.getAddress())));
    }

    @Override
    public Response<ShopInfo> queryShopInfoByUrl(ShopOperatorIdQueryByUrlRequest request) {
        return Response.ok(shopApiInfoConverter.domain2info(shopReadDomainService.queryShopInfoByUrl(request.getUrl())));
    }

    @Override
    public Response<ShopInfo> queryShopInfoByKey(ShopOperatorIdQueryByKeyRequest request) {
        return Response.ok(shopApiInfoConverter.domain2info(shopReadDomainService.queryShopInfoByKey(request.getKey())));
    }

    @Override
    public Response<List<ShopInfo>> listByIndustryId(ShopQueryByIndustryRequest request) {
        return Response.ok(shopApiInfoConverter.listdomain2info(shopReadDomainService.listByIndustryId(request.getIndustry())));
    }

    /**
     * 通过区域运营查询供应商信息
     *
     * @param request
     * @return
     */
    @Override
    public Response<List<ShopInfo>> queryVendorByOperatorId(ShopQueryByOperatorIdRequest request) {
        List<Long> vendorIdList = vendorPartnershipReadDomainService.queryVendorIdListByOperator(
                request.getOperatorId());
        return Response.ok(shopApiInfoConverter.listdomain2info(shopReadDomainService.findByIds(vendorIdList)));
    }

    @Override
    public Response<List<ShopInfo>> queryOperatorByAppIndustryUrl(ShopQueryOperatorByAppIndustryUrlRequest request) {
        return Response.ok(shopApiInfoConverter.listdomain2info(shopReadDomainService.listByAppIndustryUrl(request.getAppId(), request.getIndustryId(), request.getUrl())));
    }

    public Response<List<ShopInfo>> listByZqAndIsBrandAndIsDeliery(String isBrand,String isDeliery,String isZq,Integer tenantId) {
        return Response.ok(shopApiInfoConverter.listdomain2info(shopReadDomainService.listByZqAndIsBrandAndIsDeliery(isBrand,isDeliery,isZq,tenantId)));
    }

    @Override
    public Response<List<ShopInfo>> queryOperatorByApp(ShopQueryOperatorByAppIRequest request) {
        return Response.ok(shopApiInfoConverter.listdomain2info(shopReadDomainService.listByAppId(request.getAppId())));
    }

    @Override
    public Response<Boolean> queryProviderStatusById(ShopQueryStatusByIdRequest request) {
        return Response.ok(shopReadDomainService.queryProviderStatus(request.getId()));
    }

    @Override
    public Response<Map<Long, Long>> queryIdByOperatorAndVendors(FindByOperatorAndVendorsRequest request) {
        return Response.ok(vendorPartnershipReadDomainService.queryIdByVendorsAndOperator(request.getVendorIdList(), request.getOperatorId()));
    }

    @Override
    public List<VendorWithPartnerShipInfo> getList(PartnershipVendorListPagingRequest request) {
        List<VendorWithPartnerShipInfo> list = shopReadDomainService.getList(request);
        for (VendorWithPartnerShipInfo vendorWithPartnerShipInfo : list) {
            String extraJson = vendorWithPartnerShipInfo.getExtraJson();
            Map<String,String> extra = com.aliyun.openservices.shade.com.alibaba.fastjson.JSON.parseObject(extraJson, HashMap.class);
            if (!CollectionUtils.isEmpty(extra)) {
                vendorWithPartnerShipInfo.setContractPeriodAt(extra.get("contractPeriodAt")==null?"":extra.get("contractPeriodAt"));
                vendorWithPartnerShipInfo.setAddress(extra.get("detailAddress")==null?"":extra.get("detailAddress"));
                vendorWithPartnerShipInfo.setBankAccount(extra.get("bankAccount")==null?"":extra.get("bankAccount"));
                vendorWithPartnerShipInfo.setBankAccountName(extra.get("bankAccountName")==null?"":extra.get("bankAccountName"));
                vendorWithPartnerShipInfo.setBankName(extra.get("bankName")==null?"":extra.get("bankName"));
                vendorWithPartnerShipInfo.setBankSubBranchName(extra.get("bankBranchName")==null?"":extra.get("bankBranchName"));
                vendorWithPartnerShipInfo.setContractPeriodAt(extra.get("contractPeriodAt")==null?"":extra.get("contractPeriodAt"));
                vendorWithPartnerShipInfo.setContactName(extra.get("contactName")==null?"":extra.get("contactName"));
                vendorWithPartnerShipInfo.setContactMobile(extra.get("contactMobile")==null?"":extra.get("contactMobile"));
            }
        }
        return list;
    }

    @Override
    public Response<List<ShopInfo>> findShopByShopNameFilterByIds(FindShopByFuzzNameFilterByIdsRequest request) {
        List<Shop> shops = shopReadDomainService.findByShopNameFilterByIds(
                request.getId(),request.getCooperationMode(), request.getName(), request.getType(), request.getTenantId(), request.getSize(), request.getIgnoreOperatorId());
        return Response.ok(GeneralConverter.batchConvert(shops, shopApiInfoConverter::domain2info));
    }

    @Override
    public Response<Boolean> setDefaultLogisticsUpdateVendorExtra(ShopSinglesUpdateByIdRequest request) {
        log.info("setDefaultLogisticsUpdateVendorExtra.requset:{}",request);
        Boolean bool = shopReadDomainService.setDefaultLogisticsUpdateVendorExtra(request.getId(),request.getLogisticsId(),request.getTenantId());
        return Response.ok(bool);
    }

//    @Override
//    public List<ShopInfo> selOpera(Long operatorId) {
//        List<Shop> shops = shopReadDomainService.selOpera(operatorId);
//        return GeneralConverter.batchConvert(shops, shopApiInfoConverter::domain2info);
//    }
//
    @Override
    public List<ShopInfo> findAllOperatorList(){
        return GeneralConverter.batchConvert(shopReadDomainService.findByType(2), shopApiInfoConverter::domain2info);
    }

    @Override
    public Response<Paging<SdCustomerDimensionInfo>> pageSdCustomerDimension(SdCustomerDimensionQueryRequest request) {
        try {
            request.checkParam();
            Paging<SdCustomerDimensionInfo> paging = shopReadDomainService.findByTypeAndCondition(request);
            return Response.ok(paging);
        } catch (Exception e) {
            log.error("SD客户维度配置分页查询失败, request: {}", request, e);
            return Response.fail("sd.customer.dimension.page.fail");
        }
    }

}

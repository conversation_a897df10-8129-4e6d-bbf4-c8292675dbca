package io.terminus.parana.item.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.net.URL;
import java.util.Base64;
@Slf4j
public class EaFileUtil {

	public static File getHttpFile(String url) throws Exception {
		String fileUrl = url;
		int paramstart = url.lastIndexOf("?");
		if(paramstart != -1) {
			fileUrl = url.substring(0, paramstart);
			url += "&x-oss-process=image/resize,w_800,h_600";
		}
		else {
			url += "?x-oss-process=image/resize,w_800,h_600";
		}


		String fileName = fileUrl;
		int fileSeperatorIdx = fileUrl.lastIndexOf("/");
		if(fileSeperatorIdx != -1) {
			fileName = fileUrl.substring(fileSeperatorIdx+1);
		}


		File localFile = new File(fileName);
		FileUtils.copyURLToFile(new URL(url), localFile);
		return localFile;
	}

	public static File downloadFile(String url) throws Exception {
		String fileName = url;
		int fileSeperatorIdx = url.lastIndexOf("/");
		if(fileSeperatorIdx != -1) {
			fileName = url.substring(fileSeperatorIdx+1);
		}


		File localFile = new File(fileName);
		FileUtils.copyURLToFile(new URL(url), localFile);
		return localFile;
	}

	/**
	 * 图片解析方法
	 * @param file
	 * @return
	 * @throws Exception
	 */
	public static String file2String(File file) throws Exception {
		String fileString = FileUtils.readFileToString(file);
		file.delete();
		return fileString;
	}

	public static String file2StringWithBase64(File file) throws Exception {
		byte[] fileBytes = FileUtils.readFileToByteArray(file);
		file.delete();
		return Base64.getEncoder().encodeToString(fileBytes);
	}

	public static void main(String[] args) throws Exception {
		File file = getHttpFile("http://eabbc-dev.oss-cn-beijing.aliyuncs.com/images/14d7a3b0-b1b0-4be8-bd3a-18cc163e8cc5.jpg");
		log.info(file.getAbsolutePath());
//		String fileString = file2String(file);
//		log.info(fileString);
	}

}

package io.terminus.parana.item.common.canal;

public class ExceptionUtil {

	public static String getExceptionStr(Throwable e) {
		String msg = "";
		msg += getThrowableDump(e, 0);
		return msg;
	}
	
	private static String getThrowableDump(Throwable e, int level) {
		if(e == null)
			return "";
		if(level > 10)
			return "";
		
		String msg = e.getClass().getSimpleName() + ":" + e.getMessage() + "\r\n";
		
		
		String xx = getExceptionDetailStr(e);
		msg += xx;
		
		StackTraceElement[] stackTrace = e.getStackTrace();
		
		for(StackTraceElement stackTraceElement : stackTrace) {
			//String str1 = stackTraceElement.getClassName().replaceAll("\\$" , "");
			//String str2 = stackTraceElement.getMethodName().replaceAll("\\$" , "");
			//if(str1 == null || str1.length() == 0) {
			//	continue;
			//}
			//msg += " at " + str1 + "." + str2 + ":" + stackTraceElement.getLineNumber() + "\r\n";
			msg += getStackTraceElement(stackTraceElement) + "\r\n";
		}
		
		String caused = getThrowableDump(e.getCause(), level + 1);
		if(caused != null && caused.length() > 0) {
			msg = msg + "\r\nCaused By : " + caused;
		}
		
		return msg;
	}
	
	private static String getStackTraceElement(StackTraceElement stackTraceElement) {
		String str1 = stackTraceElement.getClassName().replaceAll("\\$" , "");
		String str2 = stackTraceElement.getMethodName().replaceAll("\\$" , "");
		if(str1 == null || str1.length() == 0) {
			return stackTraceElement.getFileName() + ":" + stackTraceElement.getLineNumber();
		}
		
		String msg = " at " + str1 + "." + str2 + ":" + stackTraceElement.getLineNumber() + "(" + stackTraceElement.getFileName() + ")";
		
		return msg;
	}
	
	private static String getExceptionDetailStr(Throwable e) {
		
		if(e == null)
			return "";
		
		String msg = e.getMessage();
		/*
		if(e instanceof com.stony.core.exception.RunException) {
			com.stony.core.exception.RunException re = (com.stony.core.exception.RunException)e;
			String ps = re.getProperty();
			String[] arr = re.getValues();
			
			if(ps != null) {
				msg += ps + "\r\n";
			}
			if(arr != null) {
				for(String s : arr) {
					msg += s + "\r\n";
				}
			}
			
		}
		else if(e instanceof com.core.exception.RunException) {
			com.core.exception.RunException re = (com.core.exception.RunException)e;
			String ps = re.getProperty();
			String[] arr = re.getValues();
			
			if(ps != null) {
				msg += ps + "\r\n";
			}
			if(arr != null) {
				for(String s : arr) {
					msg += s + "\r\n";
				}
			}
			
		}
		else if(e instanceof com.stony.core.exception.ErrorException) {
			com.stony.core.exception.ErrorException re = (com.stony.core.exception.ErrorException)e;
			String ps = re.getProperty();
			String[] arr = re.getValues();
			
			if(ps != null) {
				msg += ps + "\r\n";
			}
			if(arr != null) {
				for(String s : arr) {
					msg += s + "\r\n";
				}
			}
		}
		*/
		return msg;
	}
}

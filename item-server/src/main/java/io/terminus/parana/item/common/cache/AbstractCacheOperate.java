package io.terminus.parana.item.common.cache;

import com.alicp.jetcache.CacheGetResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 基于接口{@link CacheOperate}的抽象实现类，实现了简单的缓存命名空间、获取、写入和读取操作。<br>
 * 在此基础上，应该实现缓存未命中时的数据源自动获取等其它功能。可参见{@link io.terminus.parana.item.common.cache.plugin.MultiCache}
 * 或{@link io.terminus.parana.item.common.cache.plugin.MultiCache}
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-15
 */
@Slf4j
public abstract class AbstractCacheOperate<TR> implements CacheOperate<TR> {

    protected final String namespace;

    protected boolean cacheNull = false;

    public AbstractCacheOperate(String namespace) {
        this.namespace = namespace;
    }

    /**
     * 从缓存中获取缓存数据
     *
     * @param key 唯一键key（命名空间下唯一即可）
     * @return 获得数据成功返回内容，否则返回null
     */
    protected CacheGetResult<TR> get(String key) {
        return getCacheStack().GET(key);
    }

    /**
     * 从缓存中批量获取内容
     *
     * @param keySet 唯一键key集合
     * @return 以key为索引的Map结果
     */
    protected Map<String, TR> batchGet(Set<String> keySet) {
        return getCacheStack().getAll(keySet);
    }

    /**
     * 向缓存中存入数据
     *
     * @param key     唯一键key
     * @param element 数据对象
     */
    protected void put(String key, TR element) {
        if (!cacheNull && element == null) {
            return;
        }

        getCacheStack().put(key, element);
    }

    /**
     * 向缓存中批量存入数据
     *
     * @param resultMap 以key为索引的数据Map
     */
    protected void put(Map<? extends String, ? extends TR> resultMap) {
        if (!cacheNull) {
            List<String> toRemoveKeyList = new LinkedList<>();

            for (Map.Entry<? extends String, ? extends TR> entry : resultMap.entrySet()) {
                if (entry.getValue() == null) {
                    toRemoveKeyList.add(entry.getKey());
                }
            }

            for (String key : toRemoveKeyList) {
                resultMap.remove(key);
            }
        }
        getCacheStack().putAll(resultMap);
    }

    /**
     * 释放缓存
     *
     * @param key 唯一键key
     */
    protected void release(String key) {
        if (StringUtils.isEmpty(key)) {
            if (log.isDebugEnabled()) {
                log.debug("input key is empty");
            }

            return;
        }

        getCacheStack().remove(key);
    }

    /**
     * 批量释放缓存
     *
     * @param keySet 唯一键集合
     */
    protected void release(Set<String> keySet) {
        if (CollectionUtils.isEmpty(keySet)) {
            if (log.isDebugEnabled()) {
                log.debug("input key set is empty");
            }

            return;
        }

        getCacheStack().removeAll(keySet);
    }

    @Override
    public String getNamespace() {
        return this.namespace;
    }

    /**
     * 设置是否缓存NULL对象
     *
     * @param cacheNull 是否缓存NULL对象
     */
    public void setCacheNull(boolean cacheNull) {
        this.cacheNull = cacheNull;
    }
}

package io.terminus.parana.item.shop.util;

import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.shop.common.ShopIdAssembleStrategy;
import io.terminus.parana.item.shop.model.Shop;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2018-06-19
 */
@Component
@RequiredArgsConstructor
public class ShopIdGenerateHelper {

    private final IdGenerator idGenerator;

    /**
     * 为Shop生成id
     *
     * @param shop
     * @return id
     * @see ShopIdAssembleStrategy
     */
    public Long generateId(Shop shop) {
        Long id = idGenerator.nextValue(Shop.class, (long) shop.getType(), (long) shop.getTenantId());
        shop.setId(id);
        return id;
    }
}

package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.io.IoUtil;
import com.google.common.base.Throwables;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.List;

/**
 * Excel表格上传OSS服务
 * <AUTHOR>
 * @date 2023/2/1
 */
@Slf4j
public abstract class AbstractExcelExportUploadService {

	@Autowired(required = false)
	private ThirdPartyRegistry registry;

	@Autowired(required = false)
	private ExcelExportHelper excelExportHelper;

	public String updateFile(String name, List list, Class<?> clazz) {
		String filePath = null;
		InputStream is = null;
		try {
			ByteArrayOutputStream outputStream = excelExportHelper.generateExcelNew(list, clazz);

			byte[] content = outputStream.toByteArray();
			is = new ByteArrayInputStream(content);
			filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
			if (is != null) {
				IoUtil.close(is);
			}
		} catch (Exception e) {
			log.error("updateFile, error {}", Throwables.getStackTraceAsString(e));
			if (is != null) {
				IoUtil.close(is);
			}
		} finally {
			if (is != null) {
				IoUtil.close(is);
			}
		}
		return filePath;
	}
}

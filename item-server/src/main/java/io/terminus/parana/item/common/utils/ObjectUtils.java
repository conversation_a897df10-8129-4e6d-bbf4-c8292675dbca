package io.terminus.parana.item.common.utils;

import org.springframework.util.CollectionUtils;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 面向对象的封装工具
 * <p>
 * 面向集合的操作，使用{@link AssembleDataUtils}
 * </p>
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-12-16
 * @see AssembleDataUtils
 */
public class ObjectUtils {

    /**
     * 获取一个非空的对象
     *
     * @param t        原对象，可能为空
     * @param supplier 默认值生成方法
     * @param <T>      类型
     * @return 对象
     */
    public static <T> T nonNullOrDefault(@Nullable T t, @Nonnull Supplier<T> supplier) {
        if (t != null) {
            return t;
        }

        Assert.nonNull(supplier, "supplier.is.null");
        T result = supplier.get();
        Assert.nonNull(result, "supplier.give.null.value");
        return result;
    }

    public static <TI, TR> TR nullOrTransfer(TI input, Function<TI, TR> transfer) {
        if (input == null) {
            return null;
        }

        Assert.nonNull(transfer, "transfer.method.is.null");
        return transfer.apply(input);
    }

    public static <TKey, TValue> Map<TKey, TValue> mapOf(TKey key, TValue value) {
        Map<TKey, TValue> map = new HashMap<>();
        map.put(key, value);
        return map;
    }

    public static <TKey, TValue> Map<TKey, TValue> mapOf(TKey k1, TValue v1, TKey k2, TValue v2) {
        Map<TKey, TValue> map = new HashMap<>();
        map.put(k1, v1);
        map.put(k2, v2);
        return map;
    }

    /**
     * 获取第一个非空的对象
     *
     * @param first    第一个对象
     * @param supplier 提供方法
     * @param <T>      对象类型
     * @return 对象
     */
    public static <T> T firstNonNull(T first, Supplier<T> supplier) {
        if (first != null) {
            return first;
        }

        Assert.nonNull(supplier, "supplier.should.not.null");

        return supplier.get();
    }

    /**
     * 如果map不存在指定key，添加值
     *
     * @param map      map对象
     * @param key      key对象
     * @param supplier value生成代理
     * @param <TKey>   key类型
     * @param <TValue> value类型
     */
    public static <TKey, TValue> void putIfAbsent(Map<TKey, TValue> map, TKey key, Supplier<TValue> supplier) {
        if (map == null || map.containsKey(key)) {
            return;
        }

        map.putIfAbsent(key, supplier.get());
    }

    /**
     * 从map中安全的获取value值
     *
     * @param map      map对象
     * @param key      键
     * @param <TKey>   key类型
     * @param <TValue> value类型
     * @return value
     */
    public static <TKey, TValue> TValue safeTake(Map<TKey, TValue> map, TKey key) {
        if (map == null) {
            return null;
        }

        return map.get(key);
    }

    /**
     * 安全的向map添加/更新数据
     *
     * @param map      map
     * @param key      键
     * @param value    值
     * @param <TKey>   键类型
     * @param <TValue> 值类型
     * @return map
     */
    public static <TKey, TValue> Map<TKey, TValue> safePut(@Nullable Map<TKey, TValue> map, TKey key, TValue value) {
        if (map == null) {
            return mapOf(key, value);
        }

        map.put(key, value);
        return map;
    }

    public static <TKey, TValue> Map<TKey, TValue> mergeMap(@Nullable Map<TKey, TValue> first,
                                                             @Nullable Map<TKey, TValue> other) {
        if (CollectionUtils.isEmpty(first)) {
            return other;
        }

        if (CollectionUtils.isEmpty(other)) {
            return first;
        }

        first.putAll(other);
        return first;
    }

    /**
     * 从map中获取value
     *
     * @param map      map
     * @param key      键
     * @param <TKey>   键类型
     * @param <TValue> 值类型
     * @return 值
     */
    public static <TKey, TValue> TValue safeGet(@Nullable Map<TKey, TValue> map, TKey key) {
        if (map == null) {
            return null;
        }

        return map.get(key);
    }

    /**
     * 判断两个集合是否存在交集。如果提供的集合中存在空，则返回false
     *
     * @param first 第一个集合
     * @param other 第二个集合
     * @param <T>   集合元素类型
     * @return 存在交集返回true，否则false
     */
    public static <T> boolean hasIntersection(Collection<T> first, Collection<T> other) {
        if (CollectionUtils.isEmpty(first) || CollectionUtils.isEmpty(other)) {
            return false;
        }

        Set<T> otherSet = (other instanceof Set) ? (Set<T>) other : new HashSet<>(other);

        for (T element : first) {
            if (otherSet.contains(element)) {
                return true;
            }
        }

        return false;
    }
}

package io.terminus.parana.item.open.service;

import java.util.List;
import java.util.Map;

import io.terminus.parana.item.open.dao.SdItemBindDao;
import io.terminus.parana.item.open.model.SdItemBindBO;
import org.springframework.stereotype.Component;


import io.terminus.common.model.Paging;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class SdItemBindReadService {

	private final SdItemBindDao sdItemBindDao;
	/**
	 * 查询
	 */
	public SdItemBindBO view(SdItemBindBO model) {
		return sdItemBindDao.queryOne(model);
	}
	/**
	 * 查询列表
	 */
	public List<SdItemBindBO> list(SdItemBindBO model) {
		return sdItemBindDao.listByModel(model);
	}
	/**
	 * 分页查询列表
	 */
	public Paging<SdItemBindBO> page(Map<String, Object> params, Integer offset, Integer limit) {
		return sdItemBindDao.page(params, offset, limit);
	}

	public List<SdItemBindBO> batchQueryBindInfo(List<Long> skuIds, List<Long> operatorIds, Long status) {
		return sdItemBindDao.batchQueryBindInfo(skuIds, operatorIds, status);
	}

	public List<SdItemBindBO> bySdCnoAndPnoBatchQueryBindInfo(List<Long> cnoList, List<Long> pnoList, Long operatorId, Long status) {
		return sdItemBindDao.bySdCnoAndPnoBatchQueryBindInfo(cnoList, pnoList, operatorId, status);
	}

	public List<SdItemBindBO> bySkuIdsBatchQueryBindInfo(List<Long> skuIds, Long operatorId, Long status) {
		return sdItemBindDao.bySkuIdsBatchQueryBindInfo(skuIds, operatorId, status);
	}

	public List<SdItemBindBO> byItemIdIdsBatchQueryBindInfo(List<Long> itemIds, Long operatorId, Long status) {
		return sdItemBindDao.byItemIdIdsBatchQueryBindInfo(itemIds, operatorId, status);
	}
}

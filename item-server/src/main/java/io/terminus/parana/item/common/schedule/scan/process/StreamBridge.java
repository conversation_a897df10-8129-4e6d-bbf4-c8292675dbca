package io.terminus.parana.item.common.schedule.scan.process;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-10-24
 */
public class StreamBridge<T> {

    private final ConcurrentLinkedQueue<T> pullBuffer;
    private Integer bufferCapacity;

    private boolean pushDone = false;
    private int bufferLength = 0;

    public StreamBridge(int bufferCapacity) {
        this.pullBuffer = new ConcurrentLinkedQueue<>();
        this.bufferCapacity = bufferCapacity;
    }

    /**
     * 向缓冲区写入数据<br>
     * <p>
     * 实际可能出现微超出{@link #bufferCapacity}的情况。没必要做二次控制了，麻烦还浪费锁时间
     * </p>
     *
     * @param elements 数据集合
     * @param size     数量
     */
    public void push(Collection<T> elements, int size) {

        // 如果已经标识了推送完成，丢弃数据
        if (pushDone) {
            return;
        }

        // 控制缓冲区大小，防止太大
        while (bufferLength + size > bufferCapacity) {
            sleepToWait();
        }

        synchronized (StreamBridge.class) {
            if (pushDone) {
                return;
            }

            pullBuffer.addAll(elements);
            bufferLength += size;
        }
    }

    /**
     * 从缓冲区拉取数据<br>
     *
     * @param expectSize 期望的数据大小。此值不能取的太小，防止内存生成对象频率过高
     * @return 数据包
     */
    public BridgeExchangeData<T> pull(int expectSize) {

        // 如果任务为完成，但是数据不满足最小大小设置，不返回内容，降低IO频率
        if (!pushDone && bufferLength < expectSize) {
            return BridgeExchangeData.empty();
        }

        // 关键数据变动，需要用锁控制住
        synchronized (StreamBridge.class) {
            int size = Math.min(bufferLength, expectSize);
            List<T> data = takeData(size);
            return BridgeExchangeData.make(pushDone && bufferLength == 0, data, size);
        }
    }

    /**
     * push线程完成
     */
    public void setPushDone() {
        this.pushDone = true;
    }

    public boolean isPushDone() {
        return pushDone;
    }

    /**
     * 从缓冲区取出指定数量的数据
     *
     * @param size 指定数量
     * @return 结果集合
     */
    private List<T> takeData(int size) {
        ArrayList<T> result = new ArrayList<>(size);

        for (int i = 0; i < size; i++) {
            T element = pullBuffer.poll();
            result.add(element);
        }

        bufferLength = bufferLength - size;

        return result;
    }

    private void sleepToWait() {
        try {
            Thread.sleep(20);
        } catch (InterruptedException e) {
            // ignore it.
        }
    }
}
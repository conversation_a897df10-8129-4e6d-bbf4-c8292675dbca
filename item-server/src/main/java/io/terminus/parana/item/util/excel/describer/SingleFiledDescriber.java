package io.terminus.parana.item.util.excel.describer;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.util.excel.consts.ColumnValueType;
import io.terminus.parana.item.web.excel.ValueParsePackage;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-03-03
 */
@Slf4j
class SingleFiledDescriber extends AbstractFieldDescriber {
    private final Field field;
    @Getter
    private final boolean required;
    private Method valueSetMethod;


    SingleFiledDescriber(Class model, Field field, boolean required) {
        super(model);
        this.field = field;
        this.required = required;
    }

    void setValue(Object instance, ValueParsePackage valueParsePackage) throws InvocationTargetException, IllegalAccessException {
        ColumnValueType columnValueType = resolveValueType(field.getType());
        Object value = getValue(valueParsePackage, columnValueType);
        getValueSetMethod().invoke(instance, value);
    }

    private Method getValueSetMethod() {
        if (valueSetMethod == null) {
            try {
                valueSetMethod = getValueSetMethod(field);
            } catch (NoSuchMethodException e) {
                log.error("no setter method found for field: {} but required", field);
                throw new ServiceException("excel.model.load.fail");
            }
        }

        return valueSetMethod;
    }

    @Override
    public void reset() {
        // do nothing.
    }
}

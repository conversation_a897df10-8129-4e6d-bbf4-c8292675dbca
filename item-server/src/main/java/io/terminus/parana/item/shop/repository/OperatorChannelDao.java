package io.terminus.parana.item.shop.repository;

import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.shop.model.OperatorChannel;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class OperatorChannelDao extends MyBatisDao<OperatorChannel> {
    /**
     * 查询渠道类型
     * @return
     */
    public List<Map> findChannelTypes(){
        return sqlSession.selectList(sqlId("findChannelTypes"));
    }

    /**
     * 查询渠道城市
     * @return
     */
    public List<Map> findCities(){
        return sqlSession.selectList(sqlId("findCities"));
    }

    public Long count(Map<String, Object> criteria){
        return this.sqlSession.selectOne(this.sqlId("count"), criteria);
    }

}

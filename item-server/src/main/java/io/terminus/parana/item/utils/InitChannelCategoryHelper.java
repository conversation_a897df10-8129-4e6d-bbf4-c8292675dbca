package io.terminus.parana.item.utils;

import com.google.common.collect.Lists;
import io.terminus.parana.item.category.manager.OperatorCategoryManager;
import io.terminus.parana.item.category.model.BackCategory;
import io.terminus.parana.item.category.model.OperatorCategory;
import io.terminus.parana.item.category.repository.BackCategoryDao;
import io.terminus.parana.item.category.repository.OperatorCategoryDao;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class InitChannelCategoryHelper {

    @Autowired
    private BackCategoryDao backCategoryDao;

    @Autowired
    private OperatorCategoryDao operatorCategoryDao;

    @Autowired
    private OperatorCategoryManager operatorCategoryManager;

    @Async
    public void asyncInitChannelCategory(List<Long> operatorIdList, String userName) {
        Map<String, Object> param = new HashMap<>();
        param.put("status", 1);
        param.put("tenantId", 1);
        List<BackCategory> backCategoryList = backCategoryDao.listByMap(param);
        if (CollectionUtils.isEmpty(backCategoryList)) {
            return;
        }

        List<List<Long>> partition = Lists.partition(operatorIdList, 2);
        List<List<OperatorCategory>> createList = new ArrayList<>();
        for (List<Long> longs : partition) {
            for (Long operatorId : longs) {
                log.info("initChannelCategory operatorId:{}", operatorId);
                int count = operatorCategoryDao.countByPidCheckOperatorId(0L, operatorId);
                if (count > 0) {
                    continue;
                }
                List<OperatorCategory> operatorCategoryCreateList = getOperatorCategories(userName, operatorId, backCategoryList);
                createList.add(operatorCategoryCreateList);
            }
            log.info("initChannelCategory ,size:{}, createList:{}", createList.size(), createList);
            operatorCategoryManager.initChannelCategory(backCategoryList, createList);
            createList = new ArrayList<>();
        }
    }

    private static @NotNull List<OperatorCategory> getOperatorCategories(String userName, Long operatorId, List<BackCategory> backCategoryList) {
        List<OperatorCategory> operatorCategoryCreateList = new ArrayList<>();
        for (BackCategory backCategory : backCategoryList) {
            OperatorCategory operatorCategory = new OperatorCategory();
            operatorCategory.setTenantId(1);
            //先设置为后台类目pid 后续更新为正确的值
            operatorCategory.setPid(backCategory.getPid());
            operatorCategory.setLogo(backCategory.getLogo());
            operatorCategory.setLevel(backCategory.getLevel());
            operatorCategory.setHasChildren(backCategory.getHasChildren());
            operatorCategory.setType(0);
            operatorCategory.setName(backCategory.getName());
            operatorCategory.setIndex(0);
            operatorCategory.setStatus(1);
            operatorCategory.setHasBind(Boolean.TRUE);
            operatorCategory.setUpdatedBy(userName);
            operatorCategory.setOperatorId(operatorId);
            operatorCategoryCreateList.add(operatorCategory);
        }
        return operatorCategoryCreateList;
    }
}

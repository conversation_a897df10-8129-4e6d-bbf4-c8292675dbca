package io.terminus.parana.item.open.repository;

import io.terminus.common.model.Paging;
import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.open.bo.OpenPagingCriteriaBO;
import io.terminus.parana.item.open.bo.OpenUpdateItemsStatusBO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-11-11
 */
@Repository
public class ItemOpenDao extends MyBatisDao<Item> {

    public List<Item> openPagingOfVernier(OpenPagingCriteriaBO bo) {
        return sqlSession.selectList(sqlId("openPagingOfVernier"), bo.toMap());
    }

    public Paging<Item> openPaging(OpenPagingCriteriaBO bo) {
        Map<String, Object> criteria = bo.toMap();

        long count = sqlSession.selectOne(sqlId("openPagingCount"), criteria);
        if (count == 0 || bo.getOffset() > count) {
            return Paging.empty();
        }

        List<Item> data = sqlSession.selectList(sqlId("openPaging"), criteria);
        return new Paging<>(count, data);
    }

    public boolean openUpdateStatus(OpenUpdateItemsStatusBO bo) {
        return getSqlSession().update(sqlId("openUpdateStatus"), bo) != 0;
    }
}

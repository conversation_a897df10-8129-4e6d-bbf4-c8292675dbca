package io.terminus.parana.item.shop.repository;

import java.util.List;
import java.util.Map;

import io.terminus.parana.item.shop.model.SupplierInfoLogModel;
import io.terminus.parana.item.shop.model.SupplierInfoModel;
import org.springframework.stereotype.Repository;


import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.common.model.Paging;

@Repository
public class SupplierInfoLogDao extends MyBatisDao<SupplierInfoLogModel> {

	/**
	 *	创建供应商详情日志表
	 */
	public Boolean createModel(SupplierInfoLogModel model) {
		return this.create(model);
	}
	/**
	 *	修改供应商详情日志表
	 */
	public Boolean updateModel(SupplierInfoLogModel model) {
		return this.update(model);
	}
	/**
	 *	删除供应商详情日志表
	 */
	public Boolean deleteById(SupplierInfoLogModel model) {
		return this.delete(model.getId());
	}
	/**
	 *	查看供应商详情日志表
	 */
	public SupplierInfoLogModel queryOne(SupplierInfoLogModel model){
		return this.sqlSession.selectOne(sqlId("queryOne"), model);
	}

	public SupplierInfoLogModel getViewByVendorId(Long vendorId){
		return this.sqlSession.selectOne(sqlId("getViewByVendorId"), vendorId);
	}

	/**
	 *	列表查询供应商详情日志表
	 */
	public List<SupplierInfoLogModel> listByModel(SupplierInfoLogModel model){
		return this.list(model);
	}
	/**
	 *	分页查询供应商详情日志表
	 */
	public Paging<SupplierInfoLogModel> page(Map<String, Object> params, Integer offset, Integer limit){
		return this.paging(offset, limit, params);
	}
}

package io.terminus.parana.item.util.excel.describer;

import com.google.common.base.Throwables;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.util.excel.consts.ColumnValueType;
import io.terminus.parana.item.web.excel.ValueParsePackage;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-03-03
 */
@Slf4j
class CollectionFiledDescriber extends AbstractFieldDescriber {
    private final Field field;
    private final ColumnValueType columnValueType;
    @Getter
    private final int columnStart;
    @Getter
    private final int columnEnd;
    private Map map;

    CollectionFiledDescriber(Class model, Field field, ColumnValueType columnValueType, int columnStart, int columnEnd) {
        super(model);
        this.field = field;
        this.columnValueType = columnValueType;
        this.columnStart = columnStart;
        this.columnEnd = columnEnd;
    }

    @SuppressWarnings("unchecked")
    void putElement(String key, ValueParsePackage valueParsePackage, Object instance) {
        if (map == null) {
            switch (columnValueType) {
                case INTEGER:
                    map = new LinkedHashMap<String, Integer>();
                    break;
                case LONG:
                    map = new LinkedHashMap<String, Long>();
                    break;
                case STRING:
                    map = new LinkedHashMap<String, String>();
                    break;
                case DOUBLE:
                    map = new LinkedHashMap<String, Date>();
                    break;
            }

            try {
                Method valueSetMethod = getValueSetMethod(field);
                valueSetMethod.setAccessible(true);
                valueSetMethod.invoke(instance, map);
            } catch (NoSuchMethodException e) {
                log.error("");
                throw new ServiceException("");
            } catch (IllegalAccessException e) {
                // ignore.
            } catch (InvocationTargetException e) {
                log.error(Throwables.getStackTraceAsString(e));
            }
        }

        map.put(key, getValue(valueParsePackage, columnValueType));
    }


    @Override
    public void reset() {
        map = null;
    }
}

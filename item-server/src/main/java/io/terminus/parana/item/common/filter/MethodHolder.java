package io.terminus.parana.item.common.filter;

import java.lang.reflect.Method;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-13
 */
public class MethodHolder {

    private final Method method;
    private final boolean present;

    private MethodHolder(Method method, boolean present) {
        this.method = method;
        this.present = present;
    }

    public Method getMethod() {
        return method;
    }

    public boolean isPresent() {
        return present;
    }

    static MethodHolder wrap(Method method) {
        if (method == null) {
            return nothing();
        }
        return new MethodHolder(method, true);
    }

    static MethodHolder nothing() {
        return new MethodHolder(null, false);
    }
}
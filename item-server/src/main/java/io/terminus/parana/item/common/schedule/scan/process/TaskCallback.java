package io.terminus.parana.item.common.schedule.scan.process;

import io.terminus.common.exception.ServiceException;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-10-23
 */
public interface TaskCallback<T> {

    /**
     * 任务开始
     *
     * @param context 上下文
     */
    default void onStart(T context) {
        // do nothing.
    }

    /**
     * 进度反馈
     *
     * @param context   上下文
     * @param processed 已处理梳理
     */
    default void onProgress(T context, long processed) {
        // do nothing.
    }

    /**
     * 任务完成
     *
     * @param context 上下文
     */
    default void onDone(T context) {
        // do nothing.
    }

    /**
     * 发生错误
     *
     * @param context   上下文
     * @param exception 错误
     */
    void onError(T context, ServiceException exception);
}

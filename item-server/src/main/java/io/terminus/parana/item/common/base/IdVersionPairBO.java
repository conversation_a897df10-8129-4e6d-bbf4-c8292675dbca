package io.terminus.parana.item.common.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 与对象{@link IdVersionPair}匹配的领域内对象，用于对象的版本信息强关联记录
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-03-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IdVersionPairBO {

    @ApiModelProperty("对象id")
    private Long id;

    @ApiModelProperty("当前版本号")
    private Integer version;
}

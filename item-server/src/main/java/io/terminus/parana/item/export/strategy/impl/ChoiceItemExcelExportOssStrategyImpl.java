package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.category.manager.BackCategoryService;
import io.terminus.parana.item.category.model.BackCategory;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibSkuReadService;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.export.dto.ChoiceSearchDistributorExcelTemplate;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.item.model.SkuAttribute;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.item.service.SkuReadDomainService;
import io.terminus.parana.item.search.docobject.ChoiceItemDO;
import io.terminus.parana.item.search.facade.ChoiceItemSearchFacade;
import io.terminus.parana.item.search.request.ChoiceItemSearchRequest;
import io.terminus.parana.user.api.facade.AddressReadFacade;
import io.terminus.parana.user.api.request.address.FindAddressByIdsRequest;
import io.terminus.parana.user.api.response.AddressInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/26 20:24
 */
@Service
@Slf4j
public class ChoiceItemExcelExportOssStrategyImpl implements ExcelExportStrategy {

    @Autowired
    private ChoiceItemSearchFacade choiceItemSearchFacade;

    @Autowired
    private ItemReadDomainService itemReadDomainService;

    @Autowired
    private SkuReadDomainService skuReadDomainService;

    @Autowired
    private ChoiceLotLibSkuReadService choiceLotLibSkuReadService;

    @Autowired
    private BackCategoryService backCategoryService;

    @Autowired
    private ExcelExportHelper excelExportHelper;

    @Autowired
    private ThirdPartyRegistry registry;

    @Resource
    private AddressReadFacade addressReadFacade;



    @Override
    public String getType() {
        return ExcelExportType.DISTRIBUTOR_ITEM_EXPORT.getReportType();
    }

    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        ChoiceItemSearchRequest request = JSON.parseObject(requestJson, ChoiceItemSearchRequest.class);
        log.info("distributor.item.export,choicePrimarySearchRequest:{}", request);
        List<ChoiceSearchDistributorExcelTemplate> choiceSearchDistributorExcelTemplates = searchExport(request);
        log.info("distributor.item.export,choiceSearchDistributorExcelTemplates:{} ：" , choiceSearchDistributorExcelTemplates.size());
        Long endTime = System.currentTimeMillis();
        log.info("distributor.item.export,time:{}", endTime - beginTime);
        String filePath = null;
        InputStream is = null;
        try {
            ByteArrayOutputStream outputStream = null;
            if (choiceSearchDistributorExcelTemplates.isEmpty()) {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(ChoiceSearchDistributorExcelTemplate.class);
            } else {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(choiceSearchDistributorExcelTemplates, ChoiceSearchDistributorExcelTemplate.class);
            }
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            log.info("distributor.item.export.filePath：{}", filePath);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("distributor.item..export, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("distributor.item..export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("distributor.item..export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }

    public List<ChoiceSearchDistributorExcelTemplate> searchExport(ChoiceItemSearchRequest request) {
        log.info("distributor.item.export,searchExport. param:{}", request);
        List<ChoiceSearchDistributorExcelTemplate> dataInfoListResp = new ArrayList<>();
        int pageNo = request.getPageNo();
        while(true){
            Response<Paging<ChoiceItemDO>> search = choiceItemSearchFacade.distributorSearch(request);
            Paging<ChoiceItemDO> itemPaging = Assert.take(search);
            if (itemPaging.isEmpty() || CollectionUtils.isEmpty(itemPaging.getData())) {
                break;
            }
            log.info("distributor.item.export,searchExport. search result total:{}", itemPaging.getData().size());
            List<ChoiceSearchDistributorExcelTemplate> choiceSearchDistributorExcelTemplates = excelDataList(itemPaging, request);
            if(!CollectionUtils.isEmpty(choiceSearchDistributorExcelTemplates)){
                dataInfoListResp.addAll(choiceSearchDistributorExcelTemplates);
            }
            log.info("distributor.item.export,searchExport. search dataInfoListResp total:{}", choiceSearchDistributorExcelTemplates.size());
            pageNo++;
            request.setPageNo(pageNo);
        }
        return dataInfoListResp;
    }

    public List<ChoiceSearchDistributorExcelTemplate> excelDataList(Paging<ChoiceItemDO>  pageSearchResult, ChoiceItemSearchRequest request){
        log.info("distributor.item.export,excelDataList. pageSearchResult total:{}", pageSearchResult.getData());
        if(CollectionUtils.isEmpty(pageSearchResult.getData())){
            return Lists.newArrayList();
        }
        int total = pageSearchResult.getData().size();
        Set<Long> choiceIds = new HashSet<>();
        Map<Long, ChoiceItemDO> choiceItemMap = new HashMap<>(total);
        List<Long> categoryIdList = new ArrayList<>();
        for (ChoiceItemDO choiceItemDO : pageSearchResult.getData()) {
            choiceIds.add(choiceItemDO.getChoiceLotLibId());
            choiceItemMap.put(choiceItemDO.getItemId(), choiceItemDO);
            categoryIdList.add(choiceItemDO.getCategoryId());
        }
        Set<Long> itemIdsIds = choiceItemMap.keySet();
        log.info("distributor.item.export,searchExport. itemIdsIds:{}, choiceIds:{},categoryIdList:{} ", itemIdsIds, choiceIds, categoryIdList);
        return getDisposeData(choiceIds, categoryIdList, itemIdsIds, choiceItemMap, request);
    }

    private List<ChoiceSearchDistributorExcelTemplate> getDisposeData(Set<Long> choiceIds, List<Long> categoryIdList, Set<Long> itemIdsIds,  Map<Long, ChoiceItemDO> choiceItemMap, ChoiceItemSearchRequest request) {
        List<ChoiceSearchDistributorExcelTemplate> dataInfoListResp = new ArrayList<>();
        List<Item> itemList = itemReadDomainService.findByIdSet(itemIdsIds, request.getTenantId(), null, null);
        Map<Long, Item> itemMap = itemList.stream().collect(Collectors.toMap(Item::getId, Function.identity()));
        // 选品库商品sku
        List<ChoiceLotLibSkuModel> choiceLotLibSkuModelList = choiceLotLibSkuReadService.listByWhere(request.getOperatorId(), null, choiceIds, null, itemIdsIds);

        // 商品sku信息
        List<Sku> skuList = skuReadDomainService.findByItemIdSet(itemIdsIds, request.getTenantId(), null, null);
        Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getId, Function.identity()));

        // 分类名称
        Map<Long, List<BackCategory>> categoryMap = backCategoryService.batchFindAncestorsOf(categoryIdList);
        Map<Long, ChoiceSearchDistributorExcelTemplate> map = new HashMap<>();
        for(ChoiceLotLibSkuModel choiceLotLibSkuModel : choiceLotLibSkuModelList){
            if (map.containsKey(choiceLotLibSkuModel.getItemId())){
                continue;
            }
            ChoiceSearchDistributorExcelTemplate template = new ChoiceSearchDistributorExcelTemplate();
            template.setItemId(choiceLotLibSkuModel.getItemId());
            //template.setSkuId(choiceLotLibSkuModel.getSkuId());
            ChoiceItemDO choiceItemDO = choiceItemDispose(choiceItemMap, template);
            log.info("commodity_library_export,searchExport. choiceItemDO:{} ",choiceItemDO);
            // 商品处理
            itemDispose(itemMap, template);
            // 规格属性处理
            skuDispose(skuMap, choiceLotLibSkuModel, template);

            // 商品分类处理
            categoryDispose(categoryMap, template, choiceItemDO);
            if ("选品库已过期".equals(template.getStatus())){
                template.setPreResellerGrossRate("");
            }
            map.put(template.getItemId(), template);
                dataInfoListResp.add(template);
        }
        return  dataInfoListResp;
    }

    private void categoryDispose(Map<Long, List<BackCategory>> categoryMap, ChoiceSearchDistributorExcelTemplate template, ChoiceItemDO choiceItemDO) {
        if(!categoryMap.containsKey(choiceItemDO.getCategoryId()) || null == choiceItemDO.getCategoryId()){
            return;
        }
        // 分类处理
        List<BackCategory> backCategories = categoryMap.get(choiceItemDO.getCategoryId());
        int categorySize = backCategories.size();
        switch (categorySize) {
            case 1:
                template.setOneLevelCategoryName(backCategories.get(0).getName());
                break;
            case 2:
                template.setOneLevelCategoryName(backCategories.get(0).getName());
                template.setTwoLevelCategoryName(backCategories.get(1).getName());
                break;
            case 3:
                template.setOneLevelCategoryName(backCategories.get(0).getName());
                template.setTwoLevelCategoryName(backCategories.get(1).getName());
                template.setThreeLevelCategoryName(backCategories.get(2).getName());
                break;
            default:
               break;
        }
    }

    private void skuDispose(Map<Long, Sku> skuMap, ChoiceLotLibSkuModel choiceLotLibSkuModel, ChoiceSearchDistributorExcelTemplate template) {
        Sku sku = skuMap.get(choiceLotLibSkuModel.getSkuId());
        if(sku == null){
            return;
        }
        template.setName(sku.getName());
        template.setWunit("0");
        if(null != sku.getGw()) {
            BigDecimal gw = new BigDecimal(sku.getGw()).multiply(new BigDecimal(0.001));
            template.setWunit(gw.toString());
        }
        template.setCbm("0");
        if(null != sku.getCbm()) {
            BigDecimal cbm = sku.getCbm().multiply(new BigDecimal(0.001));
            template.setCbm(cbm.toString());
        }

        List<SkuAttribute> attributes = sku.getAttributes();
        String keys = "";
        String vals = "";
        if(!CollectionUtils.isEmpty(attributes)){
            for (int i = 0; i < attributes.size(); i++) {
                SkuAttribute attribute = attributes.get(i);
                String attrVal = attribute.getAttrVal();
                String attrKey = attribute.getAttrKey();
                if(i == 0){
                    keys = attrKey;
                    vals = attrVal;
                }else {
                    vals = vals + ";" + attrVal;
                    keys = keys + ";" + attrKey;
                }
            }
        }
        template.setAttrKey(keys);
        template.setAttrVal(vals);

        if(CollectionUtil.isNotEmpty(sku.getExtraPrice()) && sku.getExtraPrice().containsKey("centralizedPurchasePrice")){
            template.setCentralizedPurchasePrice(sku.getExtraPrice().get("centralizedPurchasePrice")/100.0);
        }
    }

    private ChoiceItemDO choiceItemDispose(Map<Long, ChoiceItemDO> choiceItemMap ,ChoiceSearchDistributorExcelTemplate template) {
        if(!choiceItemMap.containsKey(template.getItemId())){
            return new ChoiceItemDO();
        }
        ChoiceItemDO choiceItemDO = choiceItemMap.get(template.getItemId());
        if(choiceItemDO.getMinOriginalPrice() != null && choiceItemDO.getMaxOriginalPrice() != null){
            if (Objects.equals(choiceItemDO.getMinOriginalPrice(), choiceItemDO.getMaxOriginalPrice())) {
                template.setOriginalPrice(String.valueOf(BigDecimal.valueOf(choiceItemDO.getMinOriginalPrice()).divide(new BigDecimal(100))));
            } else {
                if (choiceItemDO.getMinOriginalPrice()!=null){
                    template.setOriginalPrice(BigDecimal.valueOf(choiceItemDO.getMinOriginalPrice()).divide(new BigDecimal(100)) + "~" + BigDecimal.valueOf(choiceItemDO.getMaxOriginalPrice()).divide(new BigDecimal(100)));
                }
            }
        }
        if(choiceItemDO.getMinDistributorPrice() != null && choiceItemDO.getMaxDistributorPrice() != null){
            if (Objects.equals(choiceItemDO.getMinDistributorPrice(), choiceItemDO.getMaxDistributorPrice())) {
                if (null!=choiceItemDO.getMinDistributorPrice())
                {
                    template.setDistributorPrice(String.valueOf(BigDecimal.valueOf(choiceItemDO.getMinDistributorPrice()).divide(new BigDecimal(100))));
                }
            } else {
                template.setDistributorPrice(BigDecimal.valueOf(choiceItemDO.getMinDistributorPrice()).divide(new BigDecimal(100)) + "~" + BigDecimal.valueOf(choiceItemDO.getMaxDistributorPrice()).divide(new BigDecimal(100)));
            }
        }
        if (choiceItemDO.getMinPreResellerGrossRate() != null && choiceItemDO.getMaxPreResellerGrossRate() != null) {
            if (Objects.equals(choiceItemDO.getMinPreResellerGrossRate(), choiceItemDO.getMaxPreResellerGrossRate())) {
                if (null!=choiceItemDO.getMinPreResellerGrossRate())
                {
                    template.setPreResellerGrossRate(BigDecimal.valueOf(choiceItemDO.getMinPreResellerGrossRate()).divide(new BigDecimal(100)) + "%");
                }
            } else {
                template.setPreResellerGrossRate(BigDecimal.valueOf(choiceItemDO.getMinPreResellerGrossRate()).divide(new BigDecimal(100)) + "%~" + BigDecimal.valueOf(choiceItemDO.getMaxPreResellerGrossRate()).divide(new BigDecimal(100)) + "%");
            }
        }
        template.setChoiceName(choiceItemDO.getChoiceLotLibName());
        //template.setName(choiceItemDO.getName());
        template.setBrandName(choiceItemDO.getBrandName());

        template.setMainImage(choiceItemDO.getMainImage());
        if (choiceItemDO.getStatus() == 1) {
            template.setStatus("上架");
        } else if (choiceItemDO.getStatus() == -1) {
            template.setStatus("下架");
        } else if (choiceItemDO.getStatus() == -2) {
            template.setStatus("冻结");
        } else if (choiceItemDO.getStatus() == -5) {
            template.setStatus("审核中");
        } else if (choiceItemDO.getStatus() == -3) {
            template.setStatus("供应商删除");
        }else {
            template.setStatus("选品库已过期");
        }
        return choiceItemDO;
    }

    private void itemDispose(Map<Long, Item> itemMap, ChoiceSearchDistributorExcelTemplate template) {
        if(!itemMap.containsKey(template.getItemId())){
            return;
        }
        Item item = itemMap.get(template.getItemId());
        // 销售区域处理
        template.setSalesArea("");
        if (!StringUtils.isEmpty(item.getSalesArea())) {
            List<Long> salesAreaIds = Arrays.stream(item.getSalesArea().split(",")).map(Long::valueOf).collect(Collectors.toList());
            List<AddressInfo> addressByIds = Lists.newArrayList();
            ////////////////////////
            if(!CollectionUtils.isEmpty(salesAreaIds)){
                FindAddressByIdsRequest findAddressByIdsRequest = new FindAddressByIdsRequest();
                findAddressByIdsRequest.setIds(salesAreaIds);
                Response<List<AddressInfo>> addressInfos = addressReadFacade.findByIds(findAddressByIdsRequest);
                if (addressInfos.isSuccess() && !CollectionUtils.isEmpty(addressInfos.getResult())) {
                    addressByIds.addAll(addressInfos.getResult());
                }
            }
            ////////////////////////
            List<String> salesAreaNameList = addressByIds.stream().map(AddressInfo::getName).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(salesAreaNameList)) {
                template.setSalesArea(Joiner.on(",").join(salesAreaNameList));
            }
        }
        if (item.getType() == 1) {
            template.setItemType("普通商品");
        } else if (item.getType() == 6) {
            template.setItemType("电子卡券");
        }
    }

    public String converterAmountStr(Long amount){
        String val="";
        if(amount!=null){
            BigDecimal num=new BigDecimal(amount);
            BigDecimal rate=new BigDecimal(100);
            val=num.divide(rate).toString();
        }
        return val;
    }
}

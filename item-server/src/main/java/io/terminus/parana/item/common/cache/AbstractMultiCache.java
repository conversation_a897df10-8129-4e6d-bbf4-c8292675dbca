package io.terminus.parana.item.common.cache;

import cn.hutool.json.JSONUtil;
import com.alicp.jetcache.CacheGetResult;
import io.terminus.parana.item.common.cache.plugin.MultiCache;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * 接口{@link MultiCache}的抽象封装，有效实现数据获取和失效逻辑
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-15
 */
@Slf4j
public abstract class AbstractMultiCache<TI1, TI2, TR> extends AbstractCacheOperate<TR> implements MultiCache<TI1, TI2, TR> {

    public AbstractMultiCache(String namespace) {
        super(namespace);
    }

    @Override
    public TR get(TI1 input1, TI2 input2, Object... args) {
        // PART 1: 从缓存读取结果
        CacheGetResult<TR> cacheGetResult = get(generateKey(input1, input2));
        log.info("redis.log.key:{},cacheGetResult:{}", generateKey(input1, input2), JSONUtil.toJsonStr(cacheGetResult));
        if (cacheGetResult.isSuccess()) {
            return cacheGetResult.getValue();
        }

        // PART 2: 缓存未命中，走db
        TR databaseResult = databaseGet(input1, input2, args);
        put(generateKey(input1, input2), databaseResult);
        log.info("redis.log.put.db.key:{},cacheGetResult:{}", generateKey(input1, input2), JSONUtil.toJsonStr(cacheGetResult));
        return databaseResult;
    }

    @Override
    public void remove(TI1 input1, TI2 input2) {
        release(generateKey(input1, input2));
    }

    @Override
    public void remove(Set<TI1> inputSet,TI2 input2) {
        Set<String> keySet = AssembleDataUtils.set2set(inputSet, it -> generateKey(it, input2));
        super.release(keySet);
    }

    @Override
    public String generateKey(TI1 input1, TI2 input2) {
        return input1.toString() + "$" + input2.toString();
    }
}

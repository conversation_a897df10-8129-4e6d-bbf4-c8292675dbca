package io.terminus.parana.item.util.excel.in;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.parana.item.util.ExcelAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

;

/**
 * <AUTHOR>
 * 2018/7/4
 */
@Component
@Slf4j
public class ItemExcelImportHelper<T> {

    /**
     * 解析 excel 文件
     *
     * @param excel excel
     * @param clz   clz
     * @return result
     * @throws Exception exception
     */
    public ExcelParseResult<T> parseExcel(MultipartFile excel, Class<T> clz) throws Exception {
        Workbook workbook = ExcelUtil.getWorkbook(excel);
        Sheet sheet = workbook.getSheetAt(0);
        Map<String, Method> fieldName2SetMethod = mapFieldName2SetMethod(clz);
        LinkedHashMap<Integer, T> rowNumber2Result = Maps.newLinkedHashMap();
        List<Integer> errorRowNumbers = Lists.newArrayList();
        Iterator<Row> iterator = sheet.rowIterator();
        // 跳过第一行标题行
        if (iterator.hasNext()) {
            iterator.next();
        }
        while (iterator.hasNext()) {
            Row row = iterator.next();
            if (row != null) {
                Integer rowNumber = row.getRowNum() + 1;
                try {
                    T t = getBean(row, clz, fieldName2SetMethod);
                    rowNumber2Result.put(rowNumber, t);
                } catch (Exception e) {
                    log.error("row [{}] parse error,cause:{}", row, Throwables.getStackTraceAsString(e));
                    errorRowNumbers.add(rowNumber);
                }
            }
        }
        ExcelParseResult<T> excelParseResult = new ExcelParseResult<>();
        excelParseResult.setRowNumber2Result(rowNumber2Result);
        excelParseResult.setErrorRowNumbers(errorRowNumbers);
        return excelParseResult;
    }

    /**
     * 解析 excel 文件
     *
     * @param is is
     * @param fileType fileType
     * @param clz   clz
     * @return result
     * @throws Exception exception
     */
    public ExcelParseResult<T> parseExcel(InputStream is, String fileType, Class<T> clz) throws Exception {
        Workbook workbook = ExcelUtil.getWorkbook(is, fileType);
        Sheet sheet = workbook.getSheetAt(0);
        Map<String, Method> fieldName2SetMethod = mapFieldName2SetMethod(clz);
        LinkedHashMap<Integer, T> rowNumber2Result = Maps.newLinkedHashMap();
        List<Integer> errorRowNumbers = Lists.newArrayList();
        Iterator<Row> iterator = sheet.rowIterator();
        // 跳过第一行标题行
        if (iterator.hasNext()) {
            iterator.next();
        }
        while (iterator.hasNext()) {
            Row row = iterator.next();
            if (row != null) {
                Integer rowNumber = row.getRowNum() + 1;
                try {
                    T t = getBean(row, clz, fieldName2SetMethod);
                    rowNumber2Result.put(rowNumber, t);
                } catch (Exception e) {
                    log.error("row [{}] parse error,cause:{}", row, Throwables.getStackTraceAsString(e));
                    errorRowNumbers.add(rowNumber);
                }
            }
        }
        ExcelParseResult<T> excelParseResult = new ExcelParseResult<>();
        excelParseResult.setRowNumber2Result(rowNumber2Result);
        excelParseResult.setErrorRowNumbers(errorRowNumbers);
        return excelParseResult;
    }

    /**
     * 生成 filedName 到 set 方法的映射
     *
     * @param clz clz
     * @return result
     */
    private Map<String, Method> mapFieldName2SetMethod(Class<T> clz) throws NoSuchMethodException {
        Map<String, Method> fieldName2SetMethod = Maps.newHashMap();
        Field[] fields = clz.getDeclaredFields();
        for (Field field : fields) {
            ExcelAnnotation excelAnnotation = field.getAnnotation(ExcelAnnotation.class);
            if (excelAnnotation != null) {
                String setMethodName = "set" + StringUtils.capitalize(field.getName());
                Method method = clz.getDeclaredMethod(setMethodName, field.getType());
                fieldName2SetMethod.put(field.getName(), method);
            }
        }
        return fieldName2SetMethod;
    }

    /**
     * excel 行转化为 bean 实例
     *
     * @param row                 row
     * @param clz                 clz
     * @param fieldName2SetMethod fieldName2SetMethod
     * @return bean
     * @throws Exception exception
     */
    private T getBean(Row row, Class<T> clz, Map<String, Method> fieldName2SetMethod) throws Exception {
        T obj = clz.newInstance();
        for (Field field : clz.getDeclaredFields()) {
            ExcelAnnotation excelAnnotation = field.getAnnotation(ExcelAnnotation.class);
            if (excelAnnotation != null && row.getCell(excelAnnotation.columnIndex()) != null) {
                Object cellValue = ExcelUtil.getCellValue(row.getCell(excelAnnotation.columnIndex()), field.getType());
                if (fieldName2SetMethod.containsKey(field.getName())) {
                    Method method = fieldName2SetMethod.get(field.getName());
                    method.invoke(obj, cellValue);
                }
            }
        }
        return obj;
    }
}

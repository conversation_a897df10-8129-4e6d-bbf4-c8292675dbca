package io.terminus.parana.item.open.service;

import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.open.api.bean.response.OpenConfigurationInfo;
import io.terminus.parana.item.open.api.converter.OpenConfigurationConverter;
import io.terminus.parana.item.open.cache.CacheOpenConfigurationByAppKey;
import io.terminus.parana.item.open.cache.CacheOpenConfigurationByEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OpenConfigurationReadDomainService extends AbsServiceBase {

    @Autowired
    private OpenConfigurationConverter configurationConverter;
    @Autowired
    private CacheOpenConfigurationByEntity cacheOpenConfigurationByEntity;
    @Autowired
    private CacheOpenConfigurationByAppKey cacheOpenConfigurationByAppKey;

    public OpenConfigurationInfo queryByEntity(Long entityId, Integer entityType) {
        return configurationConverter.domain2info(cacheOpenConfigurationByEntity.get(entityId, entityType));
    }

    public OpenConfigurationInfo queryByAppKey(String appKey) {
        return configurationConverter.domain2info(cacheOpenConfigurationByAppKey.get(appKey));
    }

}

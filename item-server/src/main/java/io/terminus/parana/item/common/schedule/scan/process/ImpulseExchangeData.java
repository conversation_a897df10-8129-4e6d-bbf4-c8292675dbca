package io.terminus.parana.item.common.schedule.scan.process;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * 发号器交换数据定义
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-30
 */
@Getter
public class ImpulseExchangeData {

    private static final ImpulseExchangeData DONE = new ImpulseExchangeData(-1L, -1L, true);

    @ApiModelProperty("开始id")
    private final Long start;

    @ApiModelProperty("结束id")
    private final Long end;

    @ApiModelProperty("此次任务之后是否还有后续任务")
    private final Boolean done;

    private ImpulseExchangeData(Long start, Long end, Boolean done) {
        this.start = start;
        this.end = end;
        this.done = done;
    }

    public static ImpulseExchangeData done() {
        return DONE;
    }

    public static ImpulseExchangeData make(Long start, Long end, Boolean done) {
        return new ImpulseExchangeData(start, end, done);
    }
}

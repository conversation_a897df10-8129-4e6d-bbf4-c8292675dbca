package io.terminus.parana.item.export.manager;
import io.terminus.parana.item.web.excel.AbstractExcelFileProcessor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021.06.17
 */

@Component
public class ItemBatchShelfImportTranscriptProcessor extends AbstractExcelFileProcessor<ItemBatchShelfExcelImportBo> {


    public ItemBatchShelfImportTranscriptProcessor() {
        super(ItemBatchShelfExcelImportBo.class);
    }

    @Override
    public ItemBatchShelfExcelImportBo createNewObject() {
         return new ItemBatchShelfExcelImportBo();
    }

}

package io.terminus.parana.item.common.schedule.scan;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-29
 *
 * @param <P> 上下文类型
 */
public interface ScheduleTask<P> {

    /**
     * 开始任务
     */
    void start();

    /**
     * 停止任务
     */
    void stop();

    /**
     * 任务是否已经完成
     *
     * @return 任务是否已经完成
     */
    boolean done();

    /**
     * 获取任务的处理状况
     *
     * @return 任务处理状况
     */
    ScanTaskProgress getProgress();
}

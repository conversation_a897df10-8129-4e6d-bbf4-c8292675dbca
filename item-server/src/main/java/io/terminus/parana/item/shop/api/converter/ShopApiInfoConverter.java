package io.terminus.parana.item.shop.api.converter;

import cn.hutool.core.util.NumberUtil;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.parana.item.shop.api.bean.response.ShopInfo;
import io.terminus.parana.item.shop.model.Shop;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-02-27
 */
@Mapper(componentModel = "spring")
public interface ShopApiInfoConverter {


    @Mappings({
            @Mapping(target = "flowRatio", source = "extra", qualifiedByName = "extraflowRatio")
    })
    ShopInfo domain2info(Shop shop);


    default BigDecimal extraflowRatio(Map<String, String> extra) {
        if (extra == null) {
            // 返回0 保留两位小数
            return BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
        }
        String flowRatio = extra.getOrDefault("flowRatio", "0");
        if (!NumberUtil.isNumber(flowRatio)) {
            // 返回0 保留两位小数
            return BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
        }
        try {
            return new BigDecimal(flowRatio).setScale(4, RoundingMode.HALF_UP);
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP);
        }
    }

    List<ShopInfo> listdomain2info(List<Shop> shops);

}

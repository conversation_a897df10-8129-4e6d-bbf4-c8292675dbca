package io.terminus.parana.item.export.manager;
import io.terminus.parana.item.web.excel.AbstractExcelFileProcessor;
import org.springframework.stereotype.Component;

@Component
public class ItemCouponImportTranscriptProcessor extends AbstractExcelFileProcessor<ItemCouponExcelImportBo> {


    public ItemCouponImportTranscriptProcessor() {
        super(ItemCouponExcelImportBo.class);
    }

    @Override
    public ItemCouponExcelImportBo createNewObject() {
         return new ItemCouponExcelImportBo();
    }

}

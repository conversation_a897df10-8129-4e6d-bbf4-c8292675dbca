package io.terminus.parana.item.common.flow;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 状态流的默认实现
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2018-11-28
 */
public class DefaultStatusFlow implements StatusFlow, StatusFlowRegister {

    private Map<Class<?>, StatusFlow> statusFlowMap = new ConcurrentHashMap<>();

    @Override
    public void register(Class<?> clazz, StatusFlow statusFlow) {
        statusFlowMap.put(clazz, statusFlow);
    }

    @Override
    public boolean available(Class<?> clazz, Integer currentStatus, Integer nextStatus, Object... args) {
        StatusFlow flow;

        if ((flow = statusFlowMap.get(clazz)) != null) {
            return flow.available(clazz, currentStatus, nextStatus, args);
        }

        return true;
    }

    @Override
    public Integer nextStatus(Class<?> clazz, Integer currentStatus, Integer expectStatus, Object... args) {
        StatusFlow flow;

        if ((flow = statusFlowMap.get(clazz)) != null) {
            return flow.nextStatus(clazz, currentStatus, expectStatus, args);
        }

        return expectStatus;
    }

    private volatile static DefaultStatusFlow instance;

    public static DefaultStatusFlow singleton() {
        if (instance == null) {
            synchronized (DefaultStatusFlow.class) {
                if (instance == null) {
                    instance = new DefaultStatusFlow();
                }
            }
        }

        return instance;
    }
}

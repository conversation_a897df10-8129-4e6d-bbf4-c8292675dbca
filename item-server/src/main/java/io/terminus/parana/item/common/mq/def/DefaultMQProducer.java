package io.terminus.parana.item.common.mq.def;

import io.terminus.common.rocketmq.common.DelayTimeLevelEnum;
import io.terminus.common.rocketmq.common.TerminusMessage;
import io.terminus.common.rocketmq.common.TerminusSendResult;
import io.terminus.common.rocketmq.producer.TerminusMQProducer;
import io.terminus.parana.item.common.mq.MQProducer;
import io.terminus.parana.item.common.mq.Message;
import io.terminus.parana.item.common.mq.SendResult;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DefaultMQProducer implements MQProducer {

    private final TerminusMQProducer terminusMQProducer;

    @Override
    public SendResult send(Message message) {
        TerminusMessage terminusMessage = new TerminusMessage();
        terminusMessage.setTopic(message.getTopic());
        terminusMessage.setTags(message.getTags());
        terminusMessage.setBody(message.getBody());

        if (message.getDelayTimeLevel() != null) {
            terminusMessage.setDelayTimeLevel(DelayTimeLevelEnum.getEnumByLevel(message.getDelayTimeLevel().getLevel()));
        }

        TerminusSendResult send = terminusMQProducer.send(terminusMessage);

        SendResult sendResult = new SendResult();
        sendResult.setMessageId(send.getMessageId());
        return sendResult;
    }
}

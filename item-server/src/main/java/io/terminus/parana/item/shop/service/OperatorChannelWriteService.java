package io.terminus.parana.item.shop.service;

import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.shop.model.OperatorChannel;
import io.terminus.parana.item.shop.repository.OperatorChannelDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class OperatorChannelWriteService extends AbsServiceBase {

    private final OperatorChannelDao operatorChannelDao;

    public Boolean add(OperatorChannel operatorChannel) {
        return operatorChannelDao.create(operatorChannel);
    }

    public Boolean update(OperatorChannel operatorChannel) {
        return operatorChannelDao.update(operatorChannel);
    }
}

package io.terminus.parana.item.shop.api.facade;

import io.terminus.parana.item.shop.api.bean.request.SupplierInfoLogCreateRequest;
import io.terminus.parana.item.shop.api.bean.request.SupplierInfoLogDeleteRequest;
import io.terminus.parana.item.shop.api.bean.request.SupplierInfoLogUpdateRequest;
import io.terminus.parana.item.shop.api.converter.SupplierInfoLogApiConverter;
import io.terminus.parana.item.shop.model.SupplierInfoLogModel;
import io.terminus.parana.item.shop.service.SupplierInfoLogWriteService;
import org.springframework.stereotype.Service;


import io.terminus.common.model.Response;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class SupplierInfoLogWriteFacadeImpl implements SupplierInfoLogWriteFacade {

	private final SupplierInfoLogWriteService supplierInfoLogWriteService;
	private final SupplierInfoLogApiConverter supplierInfoLogApiConverter;

	@Override
	public Response<Boolean> create(SupplierInfoLogCreateRequest request) {
		SupplierInfoLogModel model = supplierInfoLogApiConverter.get(request);
		Boolean isSuccess = supplierInfoLogWriteService.create(model);
		if(isSuccess) {
			return Response.ok(isSuccess);
		}
		else {
			return Response.fail("创建供应商详情日志表失败。");
		}
	}

	@Override
	public Response<Boolean> update(SupplierInfoLogUpdateRequest request) {
		SupplierInfoLogModel model = supplierInfoLogApiConverter.get(request);
		Boolean isSuccess = supplierInfoLogWriteService.update(model);
		if(isSuccess) {
			return Response.ok(isSuccess);
		}
		else {
			return Response.fail("修改供应商详情日志表失败。");
		}
	}

	@Override
	public Response<Boolean> delete(SupplierInfoLogDeleteRequest request) {
		SupplierInfoLogModel model = supplierInfoLogApiConverter.get(request);
		Boolean isSuccess = supplierInfoLogWriteService.delete(model);
		if(isSuccess) {
			return Response.ok(isSuccess);
		}
		else {
			return Response.fail("删除供应商详情日志表失败。");
		}
	}

}

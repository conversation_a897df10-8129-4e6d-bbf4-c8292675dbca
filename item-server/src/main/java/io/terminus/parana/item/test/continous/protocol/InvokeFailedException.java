package io.terminus.parana.item.test.continous.protocol;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class InvokeFailedException extends Throwable {
    private static final long serialVersionUID = -9041579565355666945L;

    private String message;

    private StackTraceElement[] stackTrace;

    private AssertionError assertionError;

    @Override
    public synchronized Throwable getCause() {
        return super.getCause();
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    @Override
    public StackTraceElement[] getStackTrace() {
        return this.stackTrace;
    }
}

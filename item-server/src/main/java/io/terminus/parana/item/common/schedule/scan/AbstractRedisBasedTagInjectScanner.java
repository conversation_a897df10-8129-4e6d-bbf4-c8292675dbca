package io.terminus.parana.item.common.schedule.scan;

import io.terminus.parana.item.common.base.JsonSupport;
import io.terminus.parana.item.common.schedule.scan.impl.AbstractRangedScannerThread;
import io.terminus.parana.item.common.schedule.scan.process.RangeScanResultContainer;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Jedis;
import redis.clients.util.Pool;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 基于redis的标签插入逻辑
 *
 * @param <T> 数据流转模型
 * @param <P> 上下文类型
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-09-02
 */
public abstract class AbstractRedisBasedTagInjectScanner<T, P> extends AbstractRangedScannerThread<T> implements JsonSupport {

    private final Pool<Jedis> jedisPool;
    private final String redisKey;
    private final Integer database;
    private final P context;

    public AbstractRedisBasedTagInjectScanner(Pool<Jedis> jedisPool, String redisKey, @Nullable Integer database, P context) {
        this.jedisPool = jedisPool;
        this.redisKey = redisKey;
        this.database = database;
        this.context = context;
    }

    @Override
    public RangeScanResultContainer<T> process(long start, long end, RangeScanResultContainer<T> container) {
        List<String> jsons;

        try (Jedis jedis = jedisPool.getResource()) {
            if (database == null) {
                jsons = jedis.lrange(redisKey, start, end);
            } else {
                Long originDB = jedis.getDB();
                jedis.select(database);
                jsons = jedis.lrange(redisKey, start, end);
                jedis.select(originDB.intValue());
            }
        }

        // 没有元素
        if (CollectionUtils.isEmpty(jsons)) {
            container.setLastProcessed(0);
            container.setElements(null);
        } else {
            List<T> bindingList = buildTagModel(jsons, context);
            container.setLastProcessed(jsons.size());
            container.setElements(bindingList);
        }

        return container;
    }

    /**
     * 构建标签模型
     *
     * @param jsons   来自redis的模型数据
     * @param context 上下文环境
     * @return 简单标签模型对象
     */
    protected abstract List<T> buildTagModel(List<String> jsons, P context);
}

package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.api.utils.StringUtil;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.draco.api.facade.UcUserReadFacade;
import io.terminus.draco.api.request.FindUserByIdsRequest;
import io.terminus.draco.api.response.UserInfo;
import io.terminus.parana.item.card.api.bean.request.ParanaItemCardPageRequest;
import io.terminus.parana.item.card.model.ParanaItemCardModel;
import io.terminus.parana.item.card.service.ParanaItemCardReadService;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.export.dto.ItemCardExcelTemplate;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ItemCardExcelExportOssStrategyImpl implements ExcelExportStrategy {

    @Autowired
    private ExcelExportHelper excelExportHelper;

    @Autowired
    private ThirdPartyRegistry registry;

    @Autowired
    private ParanaItemCardReadService paranaItemCardReadService;

    @Autowired
    private ItemReadDomainService itemReadDomainService;

    @Autowired
    private UcUserReadFacade ucUserReadFacade;

    @Override
    public String getType() {
        return ExcelExportType.ITEM_CARD_EXPORT.getReportType();
    }


    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        ParanaItemCardPageRequest request = JSON.parseObject(requestJson, ParanaItemCardPageRequest.class);
        log.info("item.card.export,request:{}", request);
        //组装完的表格数据
        List<ItemCardExcelTemplate> excelDataList = excelDataList(request);
        log.info("组装数据结果集：" + excelDataList);
        log.info("item.card.export,导出结果条数：" + excelDataList.size());
        Long endTime = System.currentTimeMillis();
        log.info("item.card.export.data.build,time:{}", endTime - beginTime);

        String filePath = null;
        InputStream is = null;
        try {
            ByteArrayOutputStream outputStream = null;
            if (excelDataList.isEmpty()) {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(ItemCardExcelTemplate.class);
            } else {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(excelDataList, ItemCardExcelTemplate.class);
            }
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            log.info("filePath：{}", filePath);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("item.card.export, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("item.card.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("item.card.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }

    public List<ParanaItemCardModel> searchExport(ParanaItemCardPageRequest request) {
        List<ParanaItemCardModel> dataInfoListResp = new ArrayList<>();
        int pageNo = 1;
        request.setPageNo(pageNo);
        request.setPageSize(500);
        while (true) {
            Map<String, Object> params = JSONUtil.parseObj(request);
            PageInfo pageInfo = new PageInfo(request.getPageNo(), request.getPageSize());
            Paging<ParanaItemCardModel> paging = paranaItemCardReadService.page(params, pageInfo.getOffset(), pageInfo.getLimit());
            if (CollectionUtils.isEmpty(paging.getData())) {
                break;
            }
            dataInfoListResp.addAll(paging.getData());
            pageNo++;
            request.setPageNo(pageNo);
        }
        return dataInfoListResp;
    }

    public List<ItemCardExcelTemplate> excelDataList(ParanaItemCardPageRequest request) {
        List<ParanaItemCardModel> dataInfoList = searchExport(request);

        Set<Long> itemIds = dataInfoList.stream().map(ParanaItemCardModel::getItemId).collect(Collectors.toSet());
        List<Item> items = itemReadDomainService.findByIdSet(itemIds, request.getTenantId(), null, null);
        Map<Long, Item> itemMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(items)) {
            itemMap = items.stream().collect(Collectors.toMap(Item::getId, Function.identity(), (e1, e2) -> e1));
        }
        Set<Long> userIds = dataInfoList.stream().map(ParanaItemCardModel::getBuyerId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, UserInfo> userInfoMap = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(userIds)){
            FindUserByIdsRequest param = new FindUserByIdsRequest();
            param.setIds(userIds);
            param.setTenantId(request.getTenantId());
            List<UserInfo> infoList = Assert.take(ucUserReadFacade.findUserByIds(param));
            if(CollectionUtil.isNotEmpty(infoList)){
                userInfoMap = infoList.stream().collect(Collectors.toMap(UserInfo::getId, Function.identity(), (e1, e2) -> e1));
            }
        }
        List<ItemCardExcelTemplate> excelDataList = new ArrayList<>();
        for (ParanaItemCardModel info : dataInfoList) {
            ItemCardExcelTemplate data = new ItemCardExcelTemplate();
            data.setCardId(info.getCardId());
            //不为空 证明是渠道导出 不显示卡密
            if(StringUtil.isEmpty(request.getSourceType())){
                data.setPassword(info.getCardPassword());
            }
            data.setOutCouponsId(info.getOutCouponsId());
            data.setOutCouponsName(info.getCouponsName());
            data.setItemId(String.valueOf(info.getItemId()));
            if (itemMap.containsKey(info.getItemId())) {
                data.setItemName(itemMap.get(info.getItemId()).getName());
            }
            data.setInitPrice(new BigDecimal(info.getInitPrice()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString());
            data.setIsSale(info.getIsSale() == 0 ? "未售" : "已售");
            data.setOpenTime(DateUtil.format(info.getOpenTime(), "yyyy-MM-dd HH:mm:ss"));
            data.setSaleTime(DateUtil.format(info.getSaleTime(), "yyyy-MM-dd HH:mm:ss"));
            data.setOrderId(info.getOrderId() != null ? info.getOrderId() + "" : "");
            if (info.getOrderWay() != null) {
                switch (info.getOrderWay()){
                    case 1:
                        data.setOrderWay("开放平台");
                        break;
                    case 2:
                        data.setOrderWay("快速下单");
                        break;
                    case 4:
                        data.setOrderWay("小程序下单");
                        break;
                    case 5:
                        data.setOrderWay("手动绑定");
                        break;
                    default:
                        data.setOrderWay("");
                }
            }
            if(userInfoMap.containsKey(info.getBuyerId())){
                data.setBuyerUser(userInfoMap.get(info.getBuyerId()).getMobile());
            }
            data.setActiveUser(info.getActiveUser());
            if(info.getActiveTime() != null){
                data.setActiveTime(DateUtil.format(info.getActiveTime(), "yyyy-MM-dd HH:mm:ss"));
            }
            excelDataList.add(data);
        }
        return excelDataList;
    }

}
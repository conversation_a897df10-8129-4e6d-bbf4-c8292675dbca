package io.terminus.parana.item.brand.api.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandCreateRequest;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandDeleteRequest;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandExcelImportRequest;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandUpdateRequest;
import io.terminus.parana.item.brand.api.bean.response.OuterBrandInfo;
import io.terminus.parana.item.brand.api.converter.OuterBrandConverter;
import io.terminus.parana.item.brand.model.Brand;
import io.terminus.parana.item.brand.model.OuterBrand;
import io.terminus.parana.item.brand.model.OuterBrandBinding;
import io.terminus.parana.item.brand.repository.BrandDAO;
import io.terminus.parana.item.brand.repository.OuterBrandBindingDAO;
import io.terminus.parana.item.brand.repository.OuterBrandDAO;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.common.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OuterBrandManager {

    @Autowired
    private OuterBrandDAO outerBrandDAO;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private OuterBrandConverter outerBrandConverter;
    @Autowired
    private OuterBrandBindingDAO outerBrandBindingDAO;
    @Autowired
    private BrandDAO brandDAO;

    /* 编辑外部品牌 */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOuterBrand(OuterBrandUpdateRequest outerBrandUpdateRequest) {
        OuterBrand outerBrand = outerBrandDAO.findById(outerBrandUpdateRequest.getId(), outerBrandUpdateRequest.getTenantId(), outerBrandUpdateRequest.getOperatorId());
        if (null == outerBrand) {
            throw new ServiceException("外部品牌不存在!");
        }
        if (outerBrandUpdateRequest.getOutId() != null && !StringUtils.isEmpty(outerBrandUpdateRequest.getOutId())) {
            outerBrand.setOutId(outerBrandUpdateRequest.getOutId());
        }
        if (outerBrandUpdateRequest.getUpdatedBy() != null && StringUtils.isEmpty(outerBrandUpdateRequest.getUpdatedBy())) {
            throw new ServiceException("修改人信息为空！");
        }
        if (outerBrandUpdateRequest.getUpdatedByName() != null && StringUtils.isEmpty(outerBrandUpdateRequest.getUpdatedByName())) {
            throw new ServiceException("修改人名称为空！");
        }
        outerBrand.setUpdatedBy(outerBrandUpdateRequest.getUpdatedBy());
        outerBrand.setUpdatedByName(outerBrandUpdateRequest.getUpdatedByName());
        boolean isSuccess = outerBrandDAO.update(outerBrand);

        List<Long> brandIds = outerBrandUpdateRequest.getBrandId();
        if (null != brandIds && brandIds.size() > 0) {
            ArrayList<OuterBrandBinding> outerBrandBindingArrayList = new ArrayList<>();
            List<Brand> brands = brandDAO.findByIds(brandIds);
            List<OuterBrandBinding> outerBrandBindings = outerBrandBindingDAO.findByProjectIdAndBrandIds(outerBrandUpdateRequest.getOperatorId(), brandIds);
            if (CollectionUtil.isNotEmpty(outerBrandBindings)) {
                outerBrandBindings = outerBrandBindings.stream().filter(f -> !f.getOutBrandId().equals(outerBrand.getId())).collect(Collectors.toList());
            }
            if (CollectionUtil.isNotEmpty(outerBrandBindings)) {
                StringBuilder sdu = new StringBuilder();
                for (OuterBrandBinding binding : outerBrandBindings) {
                    sdu.append("内部品牌：").append(binding.getName()).append("已经被：").append(binding.getOuterName()).append("绑定。");
                }
                throw new ServiceException(sdu.toString());
            }

            outerBrandBindingDAO.deleteByOutId(Long.parseLong(outerBrandUpdateRequest.getOutId()), outerBrandUpdateRequest.getTenantId());

            for (Brand brand : brands) {
                OuterBrandBinding outerBrandBinding = new OuterBrandBinding();
                outerBrandBinding.setTenantId(outerBrandUpdateRequest.getTenantId());
                outerBrandBinding.setOperatorId(outerBrandUpdateRequest.getOperatorId());
                outerBrandBinding.setBrandId(brand.getId());
                outerBrandBinding.setOutBrandId(null == outerBrandUpdateRequest.getOutId() ? null : Long.parseLong(outerBrandUpdateRequest.getOutId()));
                outerBrandBinding.setName(brand.getName());
                outerBrandBinding.setOuterName(outerBrand.getBrandName());
                outerBrandBindingArrayList.add(outerBrandBinding);
            }

            boolean isOk = outerBrandBindingDAO.creates(outerBrandBindingArrayList) == brandIds.size();

            if (!isOk) {
                throw new ServiceException("修改外部品牌失败！");
            }
        }


        boolean Exist = outerBrandDAO.checkNameAndBrandCode(outerBrand.getTenantId(), outerBrand.getOperatorId(), outerBrand.getBrandCode(), outerBrand.getBrandName()).size() > 1;
        if (Exist) {
            throw new ServiceException("外部品牌编码重复！");
        }
        if (!isSuccess) {
            throw new ServiceException("外部品牌修改失败！");
        }
        return true;
    }

    //新增外部品牌
    @Transactional(rollbackFor = Exception.class)
    public Long create(OuterBrandCreateRequest outerBrandCreateRequest) {
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
       /* List<OuterBrand> exist = outerBrandDAO.checkNameAndBrandCode(outerBrandCreateRequest.getTenantId(),
                outerBrandCreateRequest.getOperatorId(), outerBrandCreateRequest.getBrandCode(), outerBrandCreateRequest.getBrandName());
        if (null != exist && exist.size() != 0) {
            throw new ServiceException("外部品牌编码重复！");
        }*/
        OuterBrand OuterBrand = outerBrandConverter.toModel(outerBrandCreateRequest);
        OuterBrand.setId(idGenerator.nextValue(OuterBrand.getClass()));
        try {
            OuterBrand.setCreatedAt(sd.parse(sd.format(new Date())));
            OuterBrand.setUpdatedAt(OuterBrand.getCreatedAt());
        } catch (ParseException e) {
            log.error(Throwables.getStackTraceAsString(e));
        }
        boolean isSuccess = outerBrandDAO.create(OuterBrand);
        List<Long> brandIds = outerBrandCreateRequest.getBrandId();
        ArrayList<OuterBrandBinding> outerBrandBindingArrayList = new ArrayList<>();
        if (null != brandIds && brandIds.size() > 0) {
            List<OuterBrandBinding> outerBrandBindings = outerBrandBindingDAO.findByProjectIdAndBrandIds(outerBrandCreateRequest.getOperatorId(), brandIds);
            if (CollectionUtil.isNotEmpty(outerBrandBindings)) {
                StringBuilder sdu = new StringBuilder();
                for (OuterBrandBinding binding : outerBrandBindings) {
                    sdu.append("内部品牌：").append(binding.getName()).append("已经被：").append(binding.getOuterName()).append("绑定。");
                }
                throw new ServiceException(sdu.toString());
            }
            List<Brand> brands = brandDAO.findByIds(brandIds);
            for (Brand brand : brands) {
                OuterBrandBinding outerBrandBinding = new OuterBrandBinding();
                outerBrandBinding.setTenantId(outerBrandCreateRequest.getTenantId());
                outerBrandBinding.setOperatorId(outerBrandCreateRequest.getOperatorId());
//                outerBrandBinding.setBrandCode(brand.getBrandCode());
                outerBrandBinding.setBrandId(brand.getId());
                outerBrandBinding.setOutBrandId(OuterBrand.getOutId() == null ? null : Long.parseLong(OuterBrand.getOutId()));
                outerBrandBinding.setName(brand.getName());
                outerBrandBinding.setOuterName(OuterBrand.getBrandName());
                outerBrandBindingArrayList.add(outerBrandBinding);
            }
            boolean isOk = outerBrandBindingDAO.creates(outerBrandBindingArrayList) == brandIds.size();
            if (!isOk) {
                throw new ServiceException("创建外部品牌绑定关系失败！");
            }
        }

        if (!isSuccess)
            throw new ServiceException("创建外部品牌失败！");

        return OuterBrand.getId();
    }

    /* 删除外部品牌 */
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(OuterBrandDeleteRequest outerBrandDeleteRequest) {

        boolean isSuccess = outerBrandDAO.deleteList(outerBrandDeleteRequest.getIds(),
                outerBrandDeleteRequest.getTenateId(),
                outerBrandDeleteRequest.getOperatorId());
        if (!isSuccess)
            throw new ServiceException("外部品牌删除失败！");

        return true;
    }

    /* 恢復外部品牌 */
    @Transactional(rollbackFor = Exception.class)
    public Boolean recoverOuterBrand(OuterBrandDeleteRequest outerBrandDeleteRequest) {

        boolean isSuccess = outerBrandDAO.recoverBrandList(outerBrandDeleteRequest);
        if (!isSuccess) {
            throw new ServiceException("Recover.Is.Fail");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean OuterBrandExcelImport(List<OuterBrandExcelImportRequest> list) {
        boolean createResult = false;
        List<String> brandNameList = Lists.newArrayList();
        for (OuterBrandExcelImportRequest req : list) {
            String brandNames = req.getBrandNames();
            if (StringUtils.isNotEmpty(brandNames)) {
                brandNameList.addAll(Arrays.asList(brandNames.split(",")));
            }
        }
        Map<String, List<Brand>> brandMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(brandNameList)) {
            List<Brand> brandList = brandDAO.findByNames(brandNameList);
            brandMap = brandList.stream().collect(Collectors.groupingBy(Brand::getName));
            List<OuterBrandBinding> outerBrandBindings = outerBrandBindingDAO.findByProjectIdAndBrandIds(list.get(0).getOperatorId(), brandList.stream().map(Brand::getId).collect(Collectors.toList()));
            if (CollectionUtil.isNotEmpty(outerBrandBindings)) {
                StringBuilder sdu = new StringBuilder();
                for (OuterBrandBinding binding : outerBrandBindings) {
                    sdu.append("内部品牌：").append(binding.getName()).append("已经被：").append(binding.getOuterName()).append("绑定。");
                }
                throw new ServiceException(sdu.toString());
            }
        }
        List<OuterBrandBinding> outerBrandBindingArrayList = new ArrayList<>();
        for (OuterBrandExcelImportRequest req : list) {
            OuterBrand oc = new OuterBrand();
            oc.setId(idGenerator.nextValue(OuterBrand.class));
            oc.setTenantId(req.getTenantId());
            oc.setOperatorId(req.getOperatorId());
            oc.setOutId(req.getOutId());
            oc.setBrandName(req.getOutBrandName());
            oc.setCreatedAt(req.getCreatedAt());
            oc.setCreatedBy(req.getCreatedBy());
            oc.setUpdatedAt(req.getUpdatedAt());
            oc.setStatus("RUN");
            oc.setCreatedByName(req.getCreatedByName());
            String brandNames = req.getBrandNames();
            if (StringUtils.isNotEmpty(brandNames)) {
                String[] names = brandNames.split(",");
                for (String name : names) {
                    Brand brand = new Brand();
                    if (CollectionUtil.isNotEmpty(brandMap.get(name))) {
                        brand = brandMap.get(name).get(0);
                    }
                    OuterBrandBinding outerBrandBinding = new OuterBrandBinding();
                    outerBrandBinding.setTenantId(req.getTenantId());
                    outerBrandBinding.setOperatorId(req.getOperatorId());
                    outerBrandBinding.setBrandId(brand.getId());
                    outerBrandBinding.setOutBrandId(oc.getOutId()== null ? null : Long.parseLong(oc.getOutId()));
                    outerBrandBinding.setName(brand.getName());
                    outerBrandBinding.setOuterName(oc.getBrandName());
                    outerBrandBindingArrayList.add(outerBrandBinding);
                }
            }
            createResult = outerBrandDAO.create(oc);
        }
        if (CollectionUtil.isNotEmpty(outerBrandBindingArrayList)) {
            boolean isOk = outerBrandBindingDAO.creates(outerBrandBindingArrayList) > 0;
            Assert.isTrue(isOk, "外部品牌绑定内部品牌失败！");
        }
        return createResult;
    }

    public List<OuterBrandInfo> checkName(Integer tenantId, Long operatorId, List<String> list) {
        List<OuterBrand> res = outerBrandDAO.checkName(tenantId, operatorId, list);
        List<OuterBrandInfo> result = new ArrayList<>();
        if (res != null && res.size() > 0) {
            for (OuterBrand oc : res) {
                OuterBrandInfo on = new OuterBrandInfo();
                on.setId(oc.getId());
                on.setBrandName(oc.getBrandName());
                result.add(on);
            }
        }
        return result;
    }


}

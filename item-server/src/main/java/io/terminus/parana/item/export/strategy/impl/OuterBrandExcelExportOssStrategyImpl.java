package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import io.terminus.parana.item.brand.api.bean.request.GetOuterBrandListRequest;
import io.terminus.parana.item.brand.api.bean.response.OuterBrandInfo;
import io.terminus.parana.item.brand.api.facade.OuterBrandReadFacade;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.export.dto.OuterBrandListExportDTO;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.shop.api.facade.ShopReadFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


@Service
@Slf4j
public class OuterBrandExcelExportOssStrategyImpl implements ExcelExportStrategy {

    @Autowired
    private ExcelExportHelper excelExportHelper;
    @Autowired
    private ThirdPartyRegistry registry;
    @Autowired
    private OuterBrandReadFacade outerBrandReadFacade;
    @Autowired
    private ShopReadFacade shopReadFacade;

    @Override
    public String getType() {
        return ExcelExportType.OUTER_BRAND_LIST_EXPORT.getReportType();
    }

    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        GetOuterBrandListRequest unitQueryRequest = JSON.parseObject(requestJson, GetOuterBrandListRequest.class);
        log.info("outer brand list.export,unitQueryRequest:{}", unitQueryRequest);
        log.info("outer brand list.export,unitQueryRequest:filename{}", name);
        List<OuterBrandListExportDTO> dtoList = exportData(unitQueryRequest);
        log.info("outer brand list.export,导出结果条数：" + dtoList.size());
        Long endTime = System.currentTimeMillis();
        log.info("outer brand list.export.data.build,time:{}", endTime - beginTime);

        String filePath = null;
        InputStream is = null;
        try {
            ByteArrayOutputStream outputStream = excelExportHelper.generateExcel(dtoList, OuterBrandListExportDTO.class);
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("outer brand list.export, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("outer brand list.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
            throw new RuntimeException(e.getMessage());
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("outer brand list.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }

    private List<OuterBrandListExportDTO> exportData(GetOuterBrandListRequest request) {
        List<OuterBrandInfo> data = outerBrandReadFacade.getList(request).getResult();

        List<OuterBrandListExportDTO> datas = new ArrayList<>();
        for (OuterBrandInfo oc : data) {
            OuterBrandListExportDTO info = new OuterBrandListExportDTO();
            //info.setId(oc.getId());
            info.setOutId(oc.getOutId());
            info.setBrandCode(oc.getBrandCode());
            info.setBrandName(oc.getBrandName());
            info.setBrandNames(oc.getBrandNames());
            if ("RUN".equals(oc.getStatus())) {
                info.setStatus("可用");
            } else if ("DIS".equals(oc.getStatus())) {
                info.setStatus("不可用");
            }
            if (oc.getUpdatedAt() != null) {
                info.setUpdatedTime(DateUtil.format(LocalDateTime.parse(oc.getUpdatedAt()), "yyyy-MM-dd HH:mm:ss"));
            }
            datas.add(info);
        }
        return datas;
    }
}
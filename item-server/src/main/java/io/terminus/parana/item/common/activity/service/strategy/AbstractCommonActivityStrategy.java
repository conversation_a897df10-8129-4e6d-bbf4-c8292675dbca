package io.terminus.parana.item.common.activity.service.strategy;

import io.terminus.parana.item.common.activity.api.bean.request.CommonActivityAddRequest;
import io.terminus.parana.item.common.activity.api.bean.request.CommonActivityUpdateRequest;

/**
 * 为了方便扩展和维护，不同常用活动类型由不同策略进行创建
 *
 * <AUTHOR>
 * @date 2021-07-10
 */
public interface AbstractCommonActivityStrategy {

    /**
     * 创建常用活动
     * @param request 入参对象
     * @return 创建结果
     */
    boolean create(CommonActivityAddRequest request);

    /**
     * 是否符合当前策略
     * @param type 常用活动类型
     * @return yes or no
     */
    boolean isSupport(String type);

    /**
     * 更新常用活动
     * @param request 入参对象
     * @return 更新结果
     */
    boolean update(CommonActivityUpdateRequest request);
}

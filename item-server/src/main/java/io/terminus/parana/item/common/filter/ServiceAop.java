package io.terminus.parana.item.common.filter;

import com.google.common.base.Throwables;
import io.terminus.api.request.AbstractRequest;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 这个是面向测试的Api自动执行逻辑，正常使用时，相关逻辑由{@link ServiceFilter}完成
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-07-27
 * @see ServiceFilter
 */
@Slf4j
@Aspect
public class ServiceAop {

    @Pointcut("execution(public io.terminus.common.model.Response io.terminus.parana.item..*Impl.*(..))")
    public void apiPointcut() {

    }

    @Around("apiPointcut()")
    public Object doApi(ProceedingJoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();

            if (args != null && args.length == 1 && args[0] instanceof AbstractRequest) {
                AbstractRequest request = (AbstractRequest) args[0];
                request.checkParam();
            }

            return joinPoint.proceed();
        } catch (ServiceException e) {
            return Response.fail(e.getMessage());
        } catch (Throwable e) {
            log.error("fail to invoke interface with: {}, cause: {}", joinPoint, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        }
    }
}

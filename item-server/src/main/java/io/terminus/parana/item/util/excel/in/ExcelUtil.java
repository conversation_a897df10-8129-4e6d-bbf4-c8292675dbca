package io.terminus.parana.item.util.excel.in;

import com.google.common.io.Files;
import io.terminus.parana.exception.RestException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.DateTimeFormatterBuilder;
import org.joda.time.format.DateTimeParser;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.util.Date;

/**
 * <AUTHOR>
 * 2018/5/6
 */
public class ExcelUtil {

    private static final String XLSX_2017 = "xlsx";
    private static final String XLS_2013 = "xls";
    private static final String EMPTY_STRING = "";
    private static final String COMMA = ",";

    /**
     * 获取 workbook
     *
     * @param excel excel
     * @return workbook
     * @throws IOException IOException
     */
    public static Workbook getWorkbook(MultipartFile excel) throws Exception {
        String fileExtension = Files.getFileExtension(excel.getOriginalFilename());
        if (fileExtension.equals(XLSX_2017)) {
            return new XSSFWorkbook(excel.getInputStream());
        }
        if (fileExtension.equals(XLS_2013)) {
            return new HSSFWorkbook(excel.getInputStream());
        }
        throw new RestException("请上传正确的 excel 格式文件！");
    }

    /**
     * 获取 workbook
     *
     * @param is excel
     * @param fileType excel
     * @return workbook
     * @throws IOException IOException
     */
    public static Workbook getWorkbook(InputStream is, String fileType) throws Exception {
        if (fileType.equals(XLSX_2017)) {
            return new XSSFWorkbook(is);
        }
        if (fileType.equals(XLS_2013)) {
            return new HSSFWorkbook(is);
        }
        throw new RestException("请上传正确的 excel 格式文件！");
    }

    /**
     * 将 excel 列值转为 bean 实例
     *
     * @param cell cell
     * @param clz  clz
     * @return obj
     */
    public static Object getCellValue(Cell cell, Class<?> clz) {
        if (cell == null) {
            return null;
        }
        cell.setCellType(CellType.STRING);
        String cellValue = cell.getStringCellValue();
        if (clz == String.class) {
            return cellValue;
        }
        if (clz == BigDecimal.class) {
            return new BigDecimal(cellValue);
        }
        if (clz == Double.class) {
            return Double.valueOf(cellValue);
        }
        if (clz == Float.class) {
            return Float.valueOf(cellValue);
        }
        if (clz == Integer.class) {
            return Integer.valueOf(cellValue);
        }
        if (clz == Long.class) {
            return Long.valueOf(cellValue);
        }
        if (clz == Short.class) {
            return Short.valueOf(cellValue);
        }
        if (clz == Byte.class) {
            return Byte.valueOf(cellValue);
        }
        if (clz == Boolean.class) {
            return Boolean.valueOf(cellValue);
        }
        if (clz == Date.class) {
            return parseWithTimeFirst(cellValue);
        }
        return null;
    }

    public static final DateTimeFormatter WITH_TIME = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeParser[] PARSERS = {
            DateTimeFormat.forPattern("yyyy-MM-dd").getParser(),
            DateTimeFormat.forPattern("yyyyMMdd").getParser()
    };

    private static final DateTimeFormatter DATE_TIME_FORMATTER =
            new DateTimeFormatterBuilder().append(null, PARSERS)
            .toFormatter();

    public static Date parseWithoutTime(String input) {
        return DATE_TIME_FORMATTER.parseDateTime(input).toDate();
    }

    public static Date parseWithTime(String input) {
        return WITH_TIME.parseDateTime(input).toDate();
    }

    public static Date parseWithTimeFirst(String input) {
        try {
            return parseWithTime(input);
        } catch (Exception e) {
            return parseWithoutTime(input);
        }
    }

    public static InputStream getFileInputStream(String fileUrl) throws IOException {
        // .openStream()这地方，总是随机出现下面的报错，多试几次又没有这个报错了。
        // 暂时还没有找到原因，如果再出现这个错误，尝试重试2次。
        for (int i = 0; ; i++) {
            try {
                return new URL(fileUrl).openStream();
            } catch (javax.net.ssl.SSLException ex) {
                if (i >= 2) {
                    throw ex;
                }
            }
        }
    }

}

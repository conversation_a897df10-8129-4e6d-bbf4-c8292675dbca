package io.terminus.parana.item.common.schedule.scan.impl;

import com.google.common.base.Throwables;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.schedule.scan.process.*;
import io.terminus.parana.item.common.utils.Assert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-29
 */
@Slf4j
public abstract class AbstractPersistentThread<T> extends AbstractTaskBurst implements Persistent<T> {

    private BridgePuller<T> puller;
    private static final Integer PULL_DATA_SIZE = 200;
    private TaskBurstCallback threadRunCallback;
    private final WorkKind WORK_KIND = WorkKind.PULLER;

    @Override
    public void run() {
        log.info("thread start of persistent by threadId: {}", getIdentityId());
        Assert.nonNull(puller, "bridge.pull.ability.is.necessary");

        BridgeExchangeData<T> bridgeExchangeData;

        do {
            bridgeExchangeData = puller.pull(PULL_DATA_SIZE);

            if (bridgeExchangeData.getSize() == 0) {
                try {
                    Thread.sleep(20);
                    continue;
                } catch (InterruptedException e) {
                    // ignore it.
                }
            }

            try {
                flush(bridgeExchangeData.getElements(), bridgeExchangeData.getSize());
            } catch (Exception e) {
                log.error("fail to flush data, cause: {}", Throwables.getStackTraceAsString(e));

                if (threadRunCallback != null) {
                    threadRunCallback.onError(getIdentityId(), WORK_KIND, new ServiceException("persistent.thread.error"));
                }
            }
        } while (!bridgeExchangeData.getDone());

        if (threadRunCallback != null) {
            threadRunCallback.onDone(getIdentityId(), WORK_KIND);
        }
        log.info("thread stop of persistent by threadId: {}", getIdentityId());
    }

    @Override
    public void registerBridgePuller(BridgePuller<T> puller) {
        this.puller = puller;
    }

    @Override
    public void registerCallback(TaskBurstCallback callback) {
        this.threadRunCallback = callback;
    }
}

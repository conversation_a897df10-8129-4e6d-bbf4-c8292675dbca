package io.terminus.parana.item.common.activity.api.converter;

import io.terminus.parana.item.common.activity.model.CommonActivityGoodsBinding;
import io.terminus.parana.item.common.spi.IdGenerator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class CommonActivityItemConverter {

    @Resource
    private IdGenerator idGenerator;

    /**
     * 封装常用活动所关联的商品信息
     * @param itemIds 商品ID
     * @param commonActivityManageId 常用活动ID
     * @param date 触发时间
     * @return 商品信息列表
     */
    public List<CommonActivityGoodsBinding> packItemBindInfo(List<String> itemIds, Long commonActivityManageId, Date date, Long operatorId) {
        List<CommonActivityGoodsBinding> resultList = new ArrayList<>(itemIds.size());
        for (int i = 0; i < itemIds.size();) {
            CommonActivityGoodsBinding commonActivityGoodsBinding = new CommonActivityGoodsBinding();
            commonActivityGoodsBinding.setId(idGenerator.nextValue(CommonActivityGoodsBinding.class));
            String[] s = itemIds.get(i).split(":");
            String itemId = s.length > 1 ? s[1] : s[0];
            commonActivityGoodsBinding.setGoodsId(Long.parseLong(itemId));
            commonActivityGoodsBinding.setCreatedAt(date);
            commonActivityGoodsBinding.setUpdatedAt(date);
            commonActivityGoodsBinding.setCommonRelationId(commonActivityManageId);
            commonActivityGoodsBinding.setOperatorId(operatorId);
            commonActivityGoodsBinding.setSort(i++);
            resultList.add(commonActivityGoodsBinding);
        }
        return resultList;
    }

}

package io.terminus.parana.item.export.manager;
import io.terminus.parana.item.web.excel.AbstractExcelFileProcessor;
import org.springframework.stereotype.Component;

@Component
public class ItemGroupImportTranscriptProcessor extends AbstractExcelFileProcessor<ItemGroupExcelImportBo> {


    public ItemGroupImportTranscriptProcessor() {
        super(ItemGroupExcelImportBo.class);
    }

    @Override
    public ItemGroupExcelImportBo createNewObject() {
         return new ItemGroupExcelImportBo();
    }

}

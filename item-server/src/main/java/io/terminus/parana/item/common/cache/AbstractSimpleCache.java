package io.terminus.parana.item.common.cache;

import com.alicp.jetcache.CacheGetResult;
import com.google.common.collect.Maps;
import io.terminus.parana.item.common.cache.plugin.SimpleCache;
import io.terminus.parana.item.common.response.MapResultPack;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.Map;
import java.util.Set;
import java.util.function.Function;

/**
 * 接口{@link SimpleCache}的抽象实现，对于缓存数据的获取和失效做了有效封装<br>
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-15
 */
@Slf4j
public abstract class AbstractSimpleCache<TI, TR> extends AbstractCacheOperate<TR> implements SimpleCache<TI, TR> {

    public AbstractSimpleCache(String namespace) {
        super(namespace);
    }

    @Override
    public String generateKey(TI input, Object... args) {
        return input.toString();
    }

    @Override
    public TR get(TI input, Object... args) {
        String key = generateKey(input, args);
        CacheGetResult<TR> cacheGetResult = get(key);

        // PART 1: 缓存命中，直接返回结果
        if (cacheGetResult.isSuccess()) {
            return cacheGetResult.getValue();
        }

        // PART 2: 缓存未命中，继续处理
        TR databaseResult = databaseGet(input, args);

        // 将结果放入缓存中
        put(key, databaseResult);
        return databaseResult;
    }

    @Override
    public MapResultPack<TI, TR> get(Set<TI> inputSet, Object... args) {
        // 输入为空集合，不需要查
        if (CollectionUtils.isEmpty(inputSet)) {
            return new MapResultPack<>();
        }

        // 返回结果
        MapResultPack<TI, TR> resultPack = new MapResultPack<>(inputSet.size());

        Map<String, TI> cacheKeyIndexedMap = AssembleDataUtils.collection2mapR(inputSet, it -> generateKey(it, args));
        Set<String> keySet = cacheKeyIndexedMap.keySet();

        // 缓存命中的命中集合
        Map<String, TR> cacheResult = batchGet(keySet);

        Set<String> missKeySet;

        // 修复如果redis未开启，此处将出现npe问题
        if (cacheResult != null) {
            // 添加已命中的结果
            for (Map.Entry<String, TR> entry : cacheResult.entrySet()) {
                resultPack.addQueryResult(cacheKeyIndexedMap.get(entry.getKey()), entry.getValue());
            }

            // 获取缓存未命中的input集合
            missKeySet = AssembleDataUtils.notInSet(cacheResult.keySet(), keySet);
        } else {
            log.warn("Could not get data from cache, maybe redis not running or config error!");
            missKeySet = keySet;
        }

        // PART 1: 都已命中，直接返回结果
        if (CollectionUtils.isEmpty(missKeySet)) {
            return resultPack;
        }

        // PART 2: 缓存未命中，走数据库
        Set<TI> missInputSet = AssembleDataUtils.set2set(missKeySet, cacheKeyIndexedMap::get);
        Map<TI, TR> databaseResult = databaseGet(missInputSet, args);

        // 将数据库结果添加缓存中（空集合必须显式变量，否则下面的putAll方法会抛错）
        Map<String, TR> transferredMap = CollectionUtils.isEmpty(databaseResult)
                ? Maps.newHashMapWithExpectedSize(inputSet.size())
                : AssembleDataUtils.mapKeyTransfer(databaseResult, it -> generateKey(it, args));

        if (cacheNull) {
            Set<String> stillMissKeySet = AssembleDataUtils.notInSet(transferredMap.keySet(), missKeySet);

            // 确保依然未命中缓存的对象为空
            if (!CollectionUtils.isEmpty(stillMissKeySet)) {
                Map<String, TR> map = AssembleDataUtils.set2map(stillMissKeySet, Function.identity(), it -> null);
                transferredMap.putAll(map);
            }
        }
        put(transferredMap);

        resultPack.addQueryResult(missInputSet, databaseResult);
        return resultPack;
    }

    @Override
    public void remove(TI input, Object... args) {
        String key = generateKey(input, args);
        super.release(key);
    }

    @Override
    public void remove(Set<TI> inputSet, Object... args) {
        Set<String> keySet = AssembleDataUtils.set2set(inputSet, it -> generateKey(it, args));
        super.release(keySet);
    }
}

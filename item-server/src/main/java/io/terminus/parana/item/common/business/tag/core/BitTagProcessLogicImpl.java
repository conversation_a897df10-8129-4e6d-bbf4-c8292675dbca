package io.terminus.parana.item.common.business.tag.core;

import io.terminus.parana.item.common.business.tag.*;
import io.terminus.parana.item.common.extension.ProcessResult;
import io.terminus.parana.item.common.extension.ProcessResultPack;
import io.terminus.parana.item.common.utils.GeneralTool;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;

import static io.terminus.parana.item.common.business.tag.BitTagDefinition.INTERNAL_MAX_PREEMPTION_SIZE;

/**
 * 在启动位标开关时，注册至上下文
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-12
 */
@Slf4j
public class BitTagProcessLogicImpl implements BitTagProcessLogic, InitializingBean, ApplicationContextAware {

    private static final long NO_SET_TAG_VALUE = 0X00L;

    /**
     * 内部位标处理的holder集合
     */
    private final Map<Class<?>, List<ProcessorHolder>> internalBitTagProcessorMap;

    /**
     * 用户定义位标处理的holder集合
     */
    private final Map<Class<?>, List<ProcessorHolder>> userBitTagProcessorMap;

    private final Map<Integer, ProcessorHolder> idIndexedHolderMap;

    private ApplicationContext applicationContext;

    @Value("${enable.bit.tag.process:true}")
    private Boolean enableProcess;

    public BitTagProcessLogicImpl() {
        internalBitTagProcessorMap = new HashMap<>();
        userBitTagProcessorMap = new HashMap<>();
        idIndexedHolderMap = new HashMap<>();
    }

    @Override
    public <Model> ProcessResultPack<Model> process(ExecuteType executeType, Model model) {
        if (!(model instanceof TagResolvable)) {
            if (log.isDebugEnabled()) {
                log.debug("模型未支持标签处理，跳过...");
            }

            return ProcessResultPack.doNothing(model);
        }

        return processCore(executeType, model);
    }

    @Override
    public boolean transactional(Map<Integer, Object> paramMap) {
        if (CollectionUtils.isEmpty(paramMap)) {
            return true;
        }

        for (Map.Entry<Integer, Object> entry : paramMap.entrySet()) {
            Integer uuid = entry.getKey();

            ProcessorHolder processorHolder = idIndexedHolderMap.get(uuid);
            if (processorHolder == null) {
                log.warn("no tag processor found, but found input uuid: {}", uuid);
                continue;
            }

            Object param = entry.getValue();
            boolean isOk = processorHolder.getBitTagProcessor().transactional(param);

            if (!isOk) {
                return false;
            }
        }

        return true;
    }

    /**
     * 擦除内部位标数据
     *
     * @param bitBuffer    位标buffer
     * @param internalSize 内部预占位数
     * @return 擦除后位标buffer
     */
    private long eraseInternalBitTag(long bitBuffer, int internalSize) {
        if (internalSize < 0) {
            throw new IllegalArgumentException("内部位标预占位数必须为自然数");
        }

        if (internalSize > INTERNAL_MAX_PREEMPTION_SIZE) {
            throw new IllegalArgumentException("内部位标预占位数过大");
        }

        bitBuffer = bitBuffer >> internalSize;

        return bitBuffer << internalSize;
    }

    private <Model> ProcessResultPack<Model> processResultPack(Collection<ProcessorHolder> holders,
                                                               ProcessResultPack<Model> resultPack,
                                                               ExecuteType executeType,
                                                               Model model,
                                                               Long tag) {
        if (resultPack == null) {
            resultPack = ProcessResultPack.doNothing(model);
        }

        if (CollectionUtils.isEmpty(holders)) {
            return resultPack;
        }

        for (ProcessorHolder processorHolder : holders) {
            if (processorHolder.match(tag, executeType)) {
                ProcessResult<Model> result = ((BitTagProcessor<Model>) processorHolder.getBitTagProcessor())
                        .process(executeType, resultPack.getResultModel());

                resultPack.setResultModel(result.getResultModel());
                resultPack.getParamMap().put(processorHolder.getUuid(), result.getParam());
            }
        }

        return resultPack;
    }

    private <Model> ProcessResultPack<Model> processCore(ExecuteType executeType, Model model) {
        Long bitTag = ((TagResolvable) model).getBitTag();

        // 没做位标签，或者没有生效的标签，不需要处理
        if (bitTag == null || bitTag == NO_SET_TAG_VALUE) {
            return ProcessResultPack.doNothing(model);
        }

        ProcessResultPack<Model> processResultPack = ProcessResultPack.doNothing(model);

        // 处理内部位标
        if (internalBitTagProcessorMap.containsKey(model.getClass())) {
            List<ProcessorHolder> processorHolderList = internalBitTagProcessorMap.get(model.getClass());
            processResultPack = processResultPack(processorHolderList, processResultPack, executeType, model, bitTag);
        }

        // 处理用户定义标签
        if (userBitTagProcessorMap.containsKey(model.getClass())) {
            List<ProcessorHolder> processorHolderList = userBitTagProcessorMap.get(model.getClass());
            processResultPack = processResultPack(processorHolderList, processResultPack, executeType, model, bitTag);
        }

        return processResultPack;
    }

    private void registerProcessor(ProcessorHolder processorHolder, Class<?> targetClass, boolean internal) {
        Map<Class<?>, List<ProcessorHolder>> operateMap = internal
                ? internalBitTagProcessorMap
                : userBitTagProcessorMap;

        if (operateMap.containsKey(targetClass)) {
            List<ProcessorHolder> processorHolderList = operateMap.get(targetClass);
            processorHolderList.add(processorHolder);
        } else {
            List<ProcessorHolder> processorHolderList = new LinkedList<>();
            processorHolderList.add(processorHolder);
            operateMap.put(targetClass, processorHolderList);
        }

        idIndexedHolderMap.put(processorHolder.getUuid(), processorHolder);
    }

    private void sortProcessors() {
        if (!CollectionUtils.isEmpty(internalBitTagProcessorMap)) {
            for (List<ProcessorHolder> processorHolders : internalBitTagProcessorMap.values()) {
                if (processorHolders.size() < 2) {
                    continue;
                }

                processorHolders.sort(Comparator.comparingInt(ProcessorHolder::getOrder));
            }
        }

        if (!CollectionUtils.isEmpty(userBitTagProcessorMap)) {
            for (List<ProcessorHolder> processorHolders : userBitTagProcessorMap.values()) {
                if (processorHolders.size() < 2) {
                    continue;
                }

                processorHolders.sort(Comparator.comparingInt(ProcessorHolder::getOrder));
            }
        }
    }

    @Override
    public void afterPropertiesSet() {
        if (!enableProcess) {
            log.warn("位标处理过程被关闭，请检查开关是否被关闭：enable.bit.tag.process");
            return;
        }

        Map<String, Object> beans = applicationContext.getBeansWithAnnotation(BitTagComponent.class);

        for (Map.Entry<String, Object> beanEntry : beans.entrySet()) {
            String beanName = beanEntry.getKey();
            Object beanInstance = beanEntry.getValue();

            if (!(beanInstance instanceof BitTagProcessor)) {
                log.error("fail to load bit tag processor with bean: {}, by type: {}",
                        beanName, beanInstance.getClass().getSimpleName());
                throw new RuntimeException("注解BitTagComponent作用的类对象必须实现BitTagProcessor接口");
            }

            ProcessorHolder processorHolder = new ProcessorHolder(GeneralTool.uuid());

            BitTagProcessor processor = (BitTagProcessor) beanInstance;
            processorHolder.setBitTagProcessor(processor);
            processorHolder.setConditions(processor.conditions());

            BitTagComponent annotation = beanInstance.getClass().getAnnotation(BitTagComponent.class);
            processorHolder.setExecuteTypes(annotation.executeTypes());
            processorHolder.setOrder(annotation.order());

            registerProcessor(processorHolder, getProcessorModelClass(beanInstance.getClass()), annotation.internal());
        }

        sortProcessors();
    }

    private Class<?> getProcessorModelClass(Class<?> beanClass) {
        Type[] genericInterfaces = beanClass.getGenericInterfaces();

        for (Type genericInterface : genericInterfaces) {
            if (genericInterface instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) genericInterface;

                if (BitTagProcessor.class.getName().equals(parameterizedType.getRawType().getTypeName())) {
                    Type modelType = parameterizedType.getActualTypeArguments()[0];

                    if (modelType instanceof Class) {
                        return (Class<?>) modelType;
                    }
                }
            }
        }

        throw new RuntimeException("非法的位标处理方式！");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Getter
    private static class ProcessorHolder {

        public ProcessorHolder(Integer uuid) {
            this.uuid = uuid;
        }

        private final Integer uuid;

        private ExecuteType[] executeTypes;

        private Set<? extends BitTagDefinition> conditions;

        @Setter
        private Integer order;

        private BitTagProcessor bitTagProcessor;

        public void setExecuteTypes(ExecuteType[] executeTypes) {
            if (executeTypes == null || executeTypes.length == 0) {
                throw new IllegalArgumentException("执行类型不可为空");
            }

            this.executeTypes = executeTypes;
        }

        public void setConditions(Set<? extends BitTagDefinition> conditions) {
            if (CollectionUtils.isEmpty(conditions)) {
                throw new IllegalArgumentException("位标执行条件不可为空");
            }

            this.conditions = conditions;
        }

        public void setBitTagProcessor(BitTagProcessor bitTagProcessor) {
            if (bitTagProcessor == null) {
                throw new IllegalArgumentException("位标处理方法不可为空");
            }

            this.bitTagProcessor = bitTagProcessor;
        }

        public boolean match(Long tag, ExecuteType executeType) {
            boolean hasExecuteType = false;

            for (ExecuteType type : executeTypes) {
                if (executeType == type) {
                    hasExecuteType = true;
                    break;
                }
            }

            if (!hasExecuteType) {
                return false;
            }

            for (BitTagDefinition condition : conditions) {
                int bitOffset = condition.getBitOffset();

                // 检测对应位标是否满足条件
                if (BitTagReadAbility.isBitFalse(tag, bitOffset)) {
                    return false;
                }
            }

            return true;
        }
    }
}

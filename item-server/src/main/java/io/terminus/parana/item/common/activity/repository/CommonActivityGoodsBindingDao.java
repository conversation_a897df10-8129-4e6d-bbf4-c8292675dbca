package io.terminus.parana.item.common.activity.repository;

import io.terminus.parana.item.common.activity.model.CommonActivityGoodsBinding;
import io.terminus.parana.item.common.base.AbstractMybatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class CommonActivityGoodsBindingDao extends AbstractMybatisDao<CommonActivityGoodsBinding> {

    /**
     * 根据常用活动ID查询所关联的商品IDs
     * @param commonRelationId 常用活动ID
     * @param operatorId 区域运营ID
     * @return 所关联的商品IDs
     */
    public List<Long> findItemIdsByCommonRelationId(Long commonRelationId, Long operatorId) {
        Map<String, Object> map = new HashMap<>(8);
        map.put("commonRelationId", commonRelationId);
        map.put("operatorId", operatorId);
        return sqlSession.selectList(sqlId("findItemIdsByCommonRelationId"), map);
    }

    /**
     * 根据常用活动ID删除所关联的商品IDs
     * @param commonRelationId 常用活动ID
     * @return 删除结果
     */
    public int deleteByCommonRelationId(Long commonRelationId) {
        return sqlSession.delete(sqlId("deleteByCommonRelationId"), commonRelationId);
    }
}

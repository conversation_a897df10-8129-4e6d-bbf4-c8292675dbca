package io.terminus.parana.item.common.schedule.scan.impl;

import io.terminus.parana.item.common.schedule.scan.process.ThreadProtocol;
import io.terminus.parana.item.common.utils.Assert;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-10-24
 */
public abstract class AbstractTaskBurst implements ThreadProtocol {

    private Long identityId;

    @Override
    public synchronized void setIdentityId(long identityId) {
        Assert.isNull(this.identityId, "identity.is.set");
        this.identityId = identityId;
    }

    @Override
    public long getIdentityId() {
        return identityId;
    }
}

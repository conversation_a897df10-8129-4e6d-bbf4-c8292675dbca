package io.terminus.parana.item.shop.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.terminus.api.utils.StringUtil;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.common.filter.RequestContext;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.industry.model.Industry;
import io.terminus.parana.item.industry.repository.IndustryDao;
import io.terminus.parana.item.partnership.api.bean.request.PartnershipVendorListPagingRequest;
import io.terminus.parana.item.partnership.api.bean.response.VendorWithPartnerShipInfo;
import io.terminus.parana.item.shop.api.bean.request.SdCustomerDimensionQueryRequest;
import io.terminus.parana.item.shop.api.bean.response.SdCustomerDimensionInfo;
import io.terminus.parana.item.shop.api.enums.SdSettlementCustomerDimensionEnum;
import io.terminus.parana.item.shop.app.ShopApp;
import io.terminus.parana.item.shop.enums.ShopBusinessType;
import io.terminus.parana.item.shop.enums.ShopType;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.model.ShopAuthCategory;
import io.terminus.parana.item.shop.repository.ShopAuthCategoryDao;
import io.terminus.parana.item.shop.repository.ShopDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;



/**
 * Shop读服务
 *
 * <AUTHOR>
 * @since 2018-08-15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ShopReadDomainService extends AbsServiceBase {

    private final ShopDao shopDao;
    private final ShopApp shopApp;
    private final ShopAuthCategoryDao shopAuthCategoryDao;
    private final IndustryDao industryDao;

    public Paging<Shop> paging(Map<String, Object> params) {
        try {
            Integer pageNo = (Integer) params.get("pageNo");
            Integer pageSize = (Integer) params.get("pageSize");
            PageInfo page = new PageInfo(pageNo, pageSize);
            Paging<Shop> paging = shopDao.paging(page.getOffset(), page.getLimit(), params);

            return paging;
        } catch (Exception e) {
            log.error("paging shop failed, params={}, cause:{}",
                    params, Throwables.getStackTraceAsString(e));
            throw new ServiceException("paging.shop.failed");
        }
    }

    public Paging<Shop> pagingForConfig(Map<String, Object> params) {
        try {
            Integer pageNo = (Integer) params.get("pageNo");
            Integer pageSize = (Integer) params.get("pageSize");
            PageInfo page = new PageInfo(pageNo, pageSize);
            Paging<Shop> paging = shopDao.pagingForConfig(page.getOffset(), page.getLimit(), params);
            return paging;
        } catch (Exception e) {
            log.error("pagingForConfig shop failed, params={}, cause:{}",
                    params, Throwables.getStackTraceAsString(e));
            throw new ServiceException("pagingForConfig.shop.failed");
        }
    }

    public List<Shop> list(Map<String, Object> params) {
        try {
            return shopDao.list(params);
        } catch (Exception e) {
            log.error("list shop failed, params={}, cause:{}",
                    params, Throwables.getStackTraceAsString(e));
            throw new ServiceException("list.shop.failed");
        }
    }

    public Shop findById(Long id, Integer tenantId,String shopNames) {
        try {
            Shop shop = shopDao.findById(id, tenantId,shopNames);
            if (shop == null) {
                log.debug("no shop(id={}) found", id);
            }
            return shop;
        } catch (Exception e) {
            log.error("failed to find shop by id={}, cause:{}", id, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.shop.failed");
        }
    }

    public List<Shop> findByIds(List<Long> ids) {
        try {
            List<Shop> shops = shopDao.findByIds(ids, RequestContext.getTenantId());
            if (CollectionUtils.isEmpty(shops)) {
                log.debug("no shop(ids={}) found", ids);
            }
            return shops;
        } catch (Exception e) {
            log.error("find shops by ids={} failed, cause:{}", ids, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.shops.failed");
        }
    }

    public Shop findByOuterId(String outerId, Integer tenantId) {
        try {
            Shop shop = shopDao.findByOuterId(outerId, tenantId);
            if (shop == null) {
                log.debug("no shop(outerId={}) found", outerId);
            }
            return shop;
        } catch (Exception e) {
            log.error("failed to find shop by outerId={}, cause:{}", outerId, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.shop.failed");
        }
    }

    public Shop findByUserId(Long userId, Integer tenantId) {
        try {
            Shop shop = shopDao.findByUserId(userId, tenantId);
            if (shop == null) {
                log.debug("no shop(userId:{}) found", userId);
            }
            return shop;
        } catch (Exception e) {
            log.error("failed to find shop by userId:{}, cause:{}", userId, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.shop.failed");
        }
    }

    public Shop findByName(String name, Integer type, Integer tenantId) {
        try {
            return shopDao.findByName(name.trim(), type, tenantId);
        } catch (Exception e) {
            log.error("failed to find shop by name:{}, cause:{}", name, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.shop.failed");
        }
    }

    public List<Shop> findByFuzzName(List<Long> ids,String name, Integer type, Integer tenantId, Integer limit) {
        try {
            List<Shop> shops = shopDao.listByNamePart(ids,name.trim(), type, tenantId, limit);
            if (CollectionUtils.isEmpty(shops)) {
                log.debug("no shop(name:{} type:{}) found", name, type);
            }
            return shops;
        } catch (Exception e) {
            log.error("failed to find shop by name={}, cause:{}", name, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.shop.failed");
        }
    }

    /**
     * 查询存在合作关系的 供应商/区域运营列表
     *
     * @param name
     * @param type
     * @param tenantId
     * @param limit
     * @return
     */
    public List<Shop> findByFuzzNameFilterByIds(Long id, Integer cooperationMode, String name, Integer type, Integer tenantId, Integer limit, Long ignoreId) {


        try {
            Set<Long> idSet = Sets.newHashSet();
            if (type == ShopType.AREA_OPERATOR.getValue()) {

                idSet = shopApp.queryOperatorPartnerShipById(id, cooperationMode);

            } else if (type == ShopType.VENDOR.getValue()) {

                idSet = shopApp.queryVendorPartnerShipById(id);
            }

            if (CollectionUtils.isEmpty(idSet)) {
                log.debug("partner.ship.not.found, id:{}", id);
                return Lists.newArrayList();
            }
            List<Shop> shops = shopDao.listByNamePartFilterByIds(idSet, name.trim(), type, tenantId, limit, ignoreId);
            if (CollectionUtils.isEmpty(shops)) {
                log.debug("no shop(name:{} type:{}) found", name, type);
            }
            return shops;
        } catch (Exception e) {
            log.error("failed to find shop by name={}, cause:{}", name, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.shop.failed");
        }
    }

    /**
     * 查询存在合作关系的 供应商/区域运营列表
     *
     * @param name
     * @param type
     * @param tenantId
     * @param limit
     * @return
     */
    public List<Shop> findByFuzzNameAllFilterByIds(List<Long> result,Long id, Integer cooperationMode, String name, Integer type, Integer tenantId, Integer limit, Long ignoreId) {


        try {
            Set<Long> idSet = Sets.newHashSet();
            if (type == ShopType.AREA_OPERATOR.getValue()) {
                for (Long aLong : result) {
                    Set<Long> longs = shopApp.queryOperatorPartnerShipById(aLong, cooperationMode);
                    idSet.addAll(longs);
                }

            } else if (type == ShopType.VENDOR.getValue()) {
                for (Long aLong : result) {
                    Set<Long> longs = shopApp.queryVendorPartnerShipById(aLong);
                    idSet.addAll(longs);
                }
            }

            if (CollectionUtils.isEmpty(idSet)) {
                log.debug("partner.ship.not.found, id:{}", id);
                return Lists.newArrayList();
            }
            List<Shop> shops = shopDao.listByNamePartFilterByIds(idSet, name.trim(), type, tenantId, limit, ignoreId);
            if (CollectionUtils.isEmpty(shops)) {
                log.debug("no shop(name:{} type:{}) found", name, type);
            }
            return shops;
        } catch (Exception e) {
            log.error("failed to find shop by name={}, cause:{}", name, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.shop.failed");
        }
    }

    public List<Shop> findByShopNameFilterByIds(Long id, Integer cooperationMode, String name, Integer type, Integer tenantId, Integer limit, Long ignoreId) {


        try {
            Set<Long> idSet = Sets.newHashSet();
            if (type == ShopType.AREA_OPERATOR.getValue()) {

                idSet = shopApp.queryOperatorPartnerShipById(id, cooperationMode);

            } else if (type == ShopType.VENDOR.getValue()) {

                idSet = shopApp.queryVendorPartnerShipById(id);
            }

            if (CollectionUtils.isEmpty(idSet)) {
                log.debug("partner.ship.not.found, id:{}", id);
                return Lists.newArrayList();
            }
            List<Shop> shops = shopDao.listByShopNamePartFilterByIds(idSet, name.trim(), type, tenantId, limit, ignoreId);
            if (CollectionUtils.isEmpty(shops)) {
                log.debug("no shop(name:{} type:{}) found", name, type);
            }
            return shops;
        } catch (Exception e) {
            log.error("failed to find shop by name={}, cause:{}", name, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.shop.failed");
        }
    }

    public Boolean checkNameAvailable(Long id, String name, Integer type, Integer tenantId) {
        try {
            Shop shop = shopDao.findByNameWithId(id, name.trim(), type, tenantId);
            return shop == null;
        } catch (Exception e) {
            log.error("fail to check exist by name:{},cause:{}",
                    name, Throwables.getStackTraceAsString(e));
            throw new ServiceException("check.shop.exist.failed");
        }
    }

    public List<Long> queryAuthCategoryId(Long shopId) {
        return AssembleDataUtils.list2list(shopAuthCategoryDao.listByShopId(shopId), ShopAuthCategory::getCategoryId);
    }

    public Shop queryByIndustryIdAndBusinessType(Long industryId, Integer businessType) {
        return shopDao.queryByIndustryIdAndBusinessType(industryId, businessType);
    }

    public List<Shop> findTreeShopList(Integer tenantId) {
        try {
            Integer type=2;//区域运营
            Short status=1;//正常
            return shopDao.treeShopList(tenantId,type,status);
        } catch (Exception e) {
            log.error("find tree shops tenantId={} failed, cause:{}", tenantId, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.tree.shops.failed");
        }
    }

    public List<Shop> getExistCityList(Integer tenantId,Long id,Long pid,String cityId,Integer type) {
        try {
            return shopDao.getExistCityList( tenantId, id, pid, cityId, type);
        } catch (Exception e) {
            log.error("getExistCityList  shops tenantId={} failed, cause:{}", tenantId, Throwables.getStackTraceAsString(e));
            throw new ServiceException("getExistCityList.shops.failed");
        }
    }

    public List<Shop> queryOperatorIdByDomainAndAddressV2(String url,Map<String,String> addressMap){

        List<Shop> shops = shopDao.listByUrl(url);

        if (shops == null || shops.size() == 0) {
            return Lists.newArrayList();
        }

        if (shops != null && shops.size() == 1) {
            return shops;
        }

        List<Shop> results = Lists.newArrayList();

        String provinceId = null;
        String cityId = null;
        String regionId = null;
        if(addressMap != null) {
            provinceId = addressMap.get("provinceId");
            cityId = addressMap.get("cityId");
            regionId = addressMap.get("regionId");
            if(provinceId == null || "".equals(provinceId)) {
                log.error("provinceId.not.found, provinceId:{} ", provinceId);
                throw new ServiceException("provinceId.not.found");
            }
            if(cityId == null || "".equals(cityId)) {
                log.error("cityId.not.found, cityId:{} ", cityId);
                throw new ServiceException("cityId.not.found");
            }
                /*if(regionId==null||"".equals(regionId)){
                    log.error("regionId.not.found, regionId:{} ", regionId);
                    throw new ServiceException("regionId.not.found");
                }*/
        }

        for (Shop shop : shops) {
            Map<String, String> extraMap = shop.getExtra();
            String salesArea = extraMap.get("salesArea");
            if (StringUtils.isNotBlank(salesArea)) {
                List<String> salesAreaList = Arrays.asList(salesArea.split(","));
                if (salesAreaList.contains(provinceId)
                        || salesAreaList.contains(cityId)
                        || salesAreaList.contains(regionId)) {
                    results.add(shop);
                }
            }
        }

        return results;

    }

    public Long queryOperatorIdByDomainAndAddress(String url,Map<String,String> addressMap){
        List<Shop> shops = shopDao.listByUrl(url);

        if (shops == null || shops.size() == 0) {
            return null;
        }

        if (shops != null && shops.size() == 1) {
            return shops.get(0).getId();
        }

        List<Shop> results = Lists.newArrayList();

        String provinceId=null;
        String cityId=null;
        String regionId=null;
        if(addressMap!=null){
            provinceId=addressMap.get("provinceId");
            cityId=addressMap.get("cityId");
            regionId=addressMap.get("regionId");
            if(provinceId==null||"".equals(provinceId)){
                log.error("provinceId.not.found, provinceId:{} ", provinceId);
                throw new ServiceException("provinceId.not.found");
            }
            if(cityId==null||"".equals(cityId)){
                log.error("cityId.not.found, cityId:{} ", cityId);
                throw new ServiceException("cityId.not.found");
            }
                /*if(regionId==null||"".equals(regionId)){
                    log.error("regionId.not.found, regionId:{} ", regionId);
                    throw new ServiceException("regionId.not.found");
                }*/
        }

        if (shops.get(0).getIndustryId().intValue() == 1) {
            List<Shop> shop = shopDao.listByUrlAndAddress(url,provinceId,cityId,regionId);

            if (null != shop && shop.size() == 1) {
                return shop.get(0).getId();
            } else {
                return null;
            }
        } else if (shops.get(0).getIndustryId().intValue() == 2
                || shops.get(0).getIndustryId().intValue() == 12) {
            for (Shop shop : shops) {
                if (ShopBusinessType.NATIONWIDE_AGENCY.getValue() == shop.getBusinessType()) {
                    continue;
                }
                Map<String, String> extraMap = shop.getExtra();
                String salesArea = extraMap.get("salesArea");
                if (StringUtils.isNotBlank(salesArea)) {
                    List<String> salesAreaList = Arrays.asList(salesArea.split(","));
                    if (salesAreaList.contains(provinceId)
                            || salesAreaList.contains(cityId)
                            || salesAreaList.contains(regionId)) {
                        results.add(shop);
                        if (results.size() > 1) {
                            break;
                        }
                    }
                }
            }

            if (results.size() == 1) {
                return results.get(0).getId();
            } else {
                return null;
            }
        }else {
            return null;
        }

    }

    public Shop queryShopInfoByUrl(String url){
        try {
            Shop shop = shopDao.queryByUrl(url);
            return shop;
        } catch (Exception e) {
            log.error("failed to queryShopInfoByUrl shop , cause:{}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("queryShopInfoByUrl.shop.failed");
        }
    }

    public Shop queryShopInfoByKey(String key){
        try {
            Shop shop = shopDao.queryByKey(key);
            return shop;
        } catch (Exception e) {
            log.error("failed to queryShopInfoByUrl shop , cause:{}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("queryShopInfoByUrl.shop.failed");
        }
    }

    public List<Shop> listByUrl(String url){
        try {
            List<Shop> shops = shopDao.listByUrl(url);
            return shops;
        } catch (Exception e) {
            log.error("failed to listByUrl shop , cause:{}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("listByUrl.shop.failed");
        }
    }

    public Shop queryByIndustryId(Long industryId){
        try {
            Shop shop = shopDao.queryByIndustryId(industryId);
            return shop;
        } catch (Exception e) {
            log.error("failed to queryByIndustryId shop , cause:{}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("queryByIndustryId.shop.failed");
        }
    }

    public List<Shop> listByIndustryId(Long industryId){
        try {
            List<Shop> shops = shopDao.listByIndustryId(industryId);
            return shops;
        } catch (Exception e) {
            log.error("failed to queryByIndustryId shop , cause:{}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("queryByIndustryId.shop.failed");
        }
    }

    public List<Shop> listByAppId(String appId){
        try {
            List<Shop> shops = shopDao.listByAppId(appId);
            return shops;
        } catch (Exception e) {
            log.error("failed to listByAppId shop , cause:{}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("listByAppId.shop.failed");
        }
    }

    public List<Shop> listByAppIndustryUrl(String appId, Long industryId, String url) {
        try {
            return shopDao.listByAppIndustryUrl(appId, industryId, url);
        } catch (Exception e) {
            log.error("failed to listByAppIndustryUrl shop , cause:{}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("listByAppIndustryUrl.shop.failed");
        }
    }

    /**
     *
     * @param isBrand 是否是品牌供应
     * @param isDeliery 是否提供配送业务
     * @param isZq 是否政企
     * @return
     */
    public List<Shop> listByZqAndIsBrandAndIsDeliery(String isBrand,String isDeliery,String isZq,Integer tenantId) {

        Map<String, Object> params = Maps.newHashMap();
        params.put("tenantId", tenantId);
        /*if (StringUtil.isEmpty(isBrand)){
            isBrand = "N";
        }if (StringUtil.isEmpty(isDeliery)){
            isDeliery = "N";
        }if (StringUtil.isEmpty(isZq)){
            isZq = "N";
        }*/
        params.put("isBrand", isBrand);
        params.put("isDeliery", isDeliery);
        params.put("isZq", isZq);

        try {
            return shopDao.listByZqAndIsBrandAndIsDeliery(params);
        } catch (Exception e) {
            log.error("listByZqAndIsBrandAndIsDeliery shop failed, params={}, cause:{}",
                    params, Throwables.getStackTraceAsString(e));
            throw new ServiceException("list.shop.failed");
        }
    }

    /**
     * 根据id查询供应商入驻状态
     * @param id
     * @return
     */
    public Boolean queryProviderStatus(Long id){
        try {
            return shopDao.queryProviderStatus(id) > 0;
        }catch (Exception e){
            log.error("queryProviderStatus shop failed,cause:{}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("queryProviderStatus.shop.failed");
        }
    }

    public List<VendorWithPartnerShipInfo> getList(PartnershipVendorListPagingRequest request) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("name", request.getName());
        params.put("status", request.getStatus());
        params.put("type", request.getType());
        params.put("idSet",request.getIdSet());
        params.put("operatorId", request.getOperatorId());
        params.put("regionalOperationName",request.getRegionalOperationName());
        if (request.getPid() != null) {
            params.put("pidStr", "," + request.getPid() + ",");
        }
        List<VendorWithPartnerShipInfo> list = shopDao.getList(params);
        return list;
    }

    public Boolean setDefaultLogisticsUpdateVendorExtra(Long id, Long logisticsId, Integer tenantId) {
        Shop shop = this.findById(id, tenantId, null);
        String extraJson = shop.getExtraJson();
        Map<String, Object> stringObjectMap = JSON.parseObject(extraJson, new TypeReference<Map<String, Object>>() {
        });
        if(ObjectUtils.isEmpty(stringObjectMap)){
            stringObjectMap = new HashMap<>();
        }
        stringObjectMap.put("defaultLogisticsId",logisticsId);
        try {
            shop.setExtraJson(JSON.toJSONString(stringObjectMap));
        } catch (Exception e) {
            log.error("setDefaultLogisticsUpdateVendorExtra",e);
        }
        return shopDao.update(shop);
    }

    public List<Shop> findByType(Integer type){
        return shopDao.findByType(type);
    }

    public List<Shop> selOpera(Long operatorId){
        Map<String, Object> params = Maps.newHashMap();
        params.put("operatorId",operatorId);
        params.put("isOp","N");
        List<Shop> shops = shopDao.selOpera(params);
        return shops;
    }

    public List<Long> findAllOperatorId() {
        return shopDao.findAllOperatorId();
    }

    /**
     * 根据类型和条件查询SD客户维度配置信息
     *
     * @param request 查询请求
     * @return 分页结果
     */
    public Paging<SdCustomerDimensionInfo> findByTypeAndCondition(SdCustomerDimensionQueryRequest request) {
        try {
            // 构建查询参数
            Map<String, Object> params = Maps.newHashMap();
            params.put("type", ShopType.AREA_OPERATOR.getValue()); // 只查询运营商类型
            params.put("shopNames", request.getShopNames());
            params.put("name", request.getName());
            params.put("sdSettlementCustomerDimension", request.getSdSettlementCustomerDimension());
            params.put("pageNo", request.getPageNo());
            params.put("pageSize", request.getPageSize());

            // 调用DAO进行分页查询
            Paging<Shop> shopPaging = shopDao.findByTypeAndCondition(params);

            // 转换为响应对象
            List<SdCustomerDimensionInfo> resultList = Lists.newArrayList();
            for (Shop shop : shopPaging.getData()) {
                SdCustomerDimensionInfo info = new SdCustomerDimensionInfo();
                info.setId(shop.getId());
                info.setShopNames(shop.getShopNames());
                info.setName(shop.getName());

                // 从extra字段获取SD结算客户维度配置
                Map<String, String> extra = shop.getExtra();
                if (extra != null && extra.containsKey("sdSettlementCustomerDimension")) {
                    String dimensionStr = extra.get("sdSettlementCustomerDimension");
                    try {
                        info.setSdSettlementCustomerDimension(Integer.valueOf(dimensionStr));
                        info.setSdSettlementCustomerDimensionText(
                            SdSettlementCustomerDimensionEnum.getDescByValue(Integer.valueOf(dimensionStr))
                        );
                    } catch (NumberFormatException e) {
                        // 如果解析失败，设置为null
                        info.setSdSettlementCustomerDimension(null);
                        info.setSdSettlementCustomerDimensionText("");
                    }
                }

                info.setStatus(shop.getStatus());
                info.setCreatedAt(shop.getCreatedAt());
                info.setUpdatedAt(shop.getUpdatedAt());
                info.setUpdatedBy(shop.getUpdatedBy());

                resultList.add(info);
            }

            return new Paging<>(shopPaging.getTotal(), resultList, request.getPageSize());
        } catch (Exception e) {
            log.error("findByTypeAndCondition failed, request: {}, cause: {}", request, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.sd.customer.dimension.failed");
        }
    }
}

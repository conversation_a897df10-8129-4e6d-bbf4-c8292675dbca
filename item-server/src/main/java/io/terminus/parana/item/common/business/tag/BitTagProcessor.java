package io.terminus.parana.item.common.business.tag;

import io.terminus.parana.item.common.extension.ProcessResult;

import java.util.Set;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-12
 */
public interface BitTagProcessor<Model> {

    /**
     * 获取位标的成立条件
     *
     * @return 位标定义集合
     * @see BitTagDefinition
     */
    Set<? extends BitTagDefinition> conditions();

    /**
     * 处理位标
     *
     * @param executeType 执行类型
     * @param model       模型实例
     * @return 模型实例
     * @see ExecuteType
     */
    ProcessResult<Model> process(ExecuteType executeType, Model model);

    /**
     * 需要在事务中执行的任务
     * <p>
     * 此方法在{@link ExecuteType}为{@link ExecuteType#CREATE}、{@link ExecuteType#UPDATE}与{@link ExecuteType#DELETE}时
     * 调用，默认情况下可以不被实现。<br>
     * 入参对象为{@link #process(ExecuteType, Object)}方法的{@link ProcessResult#getParam()}对
     * 象，系统内实现数据的暂存和传递。方法实现侧需要自行完成数据类型的转换。
     * <br>
     * <br>
     * <h2>特别注意：事务方法中禁止做数据写无关的其它操作，避免性能问题！</h2>
     * </p>
     *
     * @param param 附加参数，此对象使用时，需要自行解析参数类型
     * @return 事务方法执行结果
     * @see ProcessResult
     */
    default boolean transactional(Object param) {
        return true;
    }
}

package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.terminus.parana.item.category.manager.BackCategoryService;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.export.dto.ParanaZqSkuListExportDTO;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.item.api.bean.request.sku.ParanaZqSkuQueryRequest;
import io.terminus.parana.item.item.api.converter.ParanaZqSkuApiConverter;
import io.terminus.parana.item.item.model.ParanaZqSkuModel;
import io.terminus.parana.item.item.service.ParanaZqSkuReadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ParanaZqSkuExcelExportOssStrategyImpl implements ExcelExportStrategy {

    @Autowired
    private ExcelExportHelper excelExportHelper;
    @Autowired
    private ThirdPartyRegistry registry;
    @Autowired
    private ParanaZqSkuReadService paranaZqSkuReadService;
    @Autowired
    private ParanaZqSkuApiConverter paranaZqSkuApiConverter;
    @Autowired
    private BackCategoryService backCategoryService;
    @Override
    public String getType() {
        return ExcelExportType.PARANA_ZQ_SKU_LIST_EXPORT.getReportType();
    }

    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        ParanaZqSkuQueryRequest paranaZqSkuQueryRequest = JSON.parseObject(requestJson, ParanaZqSkuQueryRequest.class);
        log.info("parana zq sku list.export,paranaZqSkuQueryRequest:{}", paranaZqSkuQueryRequest);
        log.info("parana zq sku list.export,paranaZqSkuQueryRequest:filename{}", name);
        List<ParanaZqSkuListExportDTO> dtoList = exportData(paranaZqSkuQueryRequest);
        log.info("parana zq sku list.export,导出结果条数：" + dtoList.size());
        Long endTime = System.currentTimeMillis();
        log.info("parana zq sku list.export.data.build,time:{}", endTime - beginTime);

        String filePath = null;
        InputStream is = null;
        try {
            ByteArrayOutputStream outputStream = excelExportHelper.generateExcel(dtoList, ParanaZqSkuListExportDTO.class);
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("parana zq sku list.export, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("parana zq sku list.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
            throw new RuntimeException(e.getMessage());
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("parana zq sku list.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }

    private List<ParanaZqSkuListExportDTO> exportData(ParanaZqSkuQueryRequest request) {
        List<ParanaZqSkuModel> data = paranaZqSkuReadService.list(paranaZqSkuApiConverter.get(request));
        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }
        //类目查询
        Set<Long> categoryIds = data.stream().map(ParanaZqSkuModel::getCategoryId).collect(Collectors.toSet());
        Map<Long, String> backCategoryMap = backCategoryService.findByIds(categoryIds);
        List<ParanaZqSkuListExportDTO> datas = new ArrayList<>();
        for (ParanaZqSkuModel oc : data) {
            ParanaZqSkuListExportDTO info = new ParanaZqSkuListExportDTO();
            info.setPptSkuId(oc.getPptSkuId());
            info.setPptItemId(oc.getPptItemId());
            info.setZqSkuId(oc.getZqSkuId());
            info.setName(oc.getName());
            info.setMainImage(oc.getMainImage());
            if (backCategoryMap.containsKey(oc.getCategoryId())) {
                info.setCategoryName(backCategoryMap.get(oc.getCategoryId()) + "(" + oc.getCategoryId() + ")");
            }
            info.setBrandName(oc.getBrandName());
            info.setCreatedAt(DateUtil.format(oc.getCreatedAt(), "yyyy-MM-dd"));
            info.setUpdatedAt(DateUtil.format(oc.getUpdatedAt(), "yyyy-MM-dd"));
            if (!ObjectUtils.isEmpty(oc.getZqShelfStatus())) {
                if (-1 == oc.getZqShelfStatus()) {
                    info.setZqShelfStatus("下架");
                }else if (1 == oc.getZqShelfStatus()) {
                    info.setZqShelfStatus("上架");
                }
            }
            if (!ObjectUtils.isEmpty(oc.getPptStatus())) {
                if (-1 == oc.getPptStatus()) {
                    info.setPptStatus("下架");
                }else if (1 == oc.getPptStatus()) {
                    info.setPptStatus("上架");
                }
            }
            if (!ObjectUtils.isEmpty(oc.getZqPushStatus())) {
                if (0 == oc.getZqPushStatus()) {
                    info.setZqPushStatus("待推送");
                }else if (1 == oc.getZqPushStatus()) {
                    info.setZqPushStatus("已入池");
                }else if (2 == oc.getZqPushStatus()) {
                    info.setZqPushStatus("待审核");
                }else if (3 == oc.getZqPushStatus()) {
                    info.setZqPushStatus("被驳回");
                }else if (4 == oc.getZqPushStatus()) {
                    info.setZqPushStatus("修改待推送");
                }else if (5 == oc.getZqPushStatus()) {
                    info.setZqPushStatus("修改审核");
                }else if (6 == oc.getZqPushStatus()) {
                    info.setZqPushStatus("修改被驳回");
                }
            }
            datas.add(info);
        }
        return datas;
    }
}
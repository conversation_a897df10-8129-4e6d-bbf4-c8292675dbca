/*
 * Copyright (c) 2018. 杭州端点网络科技有限公司.  All rights reserved.
 */

package io.terminus.parana.item.shop.repository;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import io.terminus.common.model.Paging;
import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.item.canal.ESMQInjection;
import io.terminus.parana.item.partnership.api.bean.response.VendorWithPartnerShipInfo;
import io.terminus.parana.item.shop.model.Shop;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-28
 */
@Repository
public class ShopDao extends MyBatisDao<Shop> {

    public Boolean create(Shop shop) {
        boolean isOk = false;
        isOk = this.sqlSession.insert(this.sqlId("create"), shop) == 1;
        if (isOk) {
            //配置埋点
            ESMQInjection.entryShopConfigInit(shop.getId());
        }
        return isOk;
    }

    public Boolean update(Shop shop) {
        boolean isOk = false;
        isOk = this.sqlSession.update(this.sqlId("update"), shop) == 1;
        if (isOk) {
            //配置埋点
            ESMQInjection.entryShopConfigInit(shop.getId());
        }
        return isOk;
    }

    /**
     * 根据店铺id查询对应的店铺
     *
     * @param id
     * @param tenantId
     * @return
     */
    public Shop findById(Long id, Integer tenantId,String shopNames) {
        if(StringUtils.isNotBlank(shopNames)){
            return getSqlSession().selectOne(sqlId("findById"),
                    ImmutableMap.of("id", id, "tenantId", tenantId,"shopNames" ,shopNames));
        }else {
            return getSqlSession().selectOne(sqlId("findById"),
                    ImmutableMap.of("id", id, "tenantId", tenantId));
        }
    }

    /**
     * 根据id列表查询对应的店铺
     *
     * @param ids
     * @param tenantId
     * @return
     */
    public List<Shop> findByIds(List<Long> ids, Integer tenantId) {
        return getSqlSession().selectList(this.sqlId("findByIds"),
                ImmutableMap.of("ids", ids, "tenantId", tenantId));
    }

    /**
     * 根据用户id查找对应的店铺
     *
     * @param userId 用户id
     * @return 对应的店铺
     */
    public Shop findByUserId(Long userId, Integer tenantId) {
        return getSqlSession().selectOne(sqlId("findByUserId"),
                ImmutableMap.of("userId", userId, "tenantId", tenantId));
    }

    /**
     * 根据id删除店铺
     *
     * @param id
     * @param tenantId
     * @return
     */
    public Boolean delete(Long id, Integer tenantId) {
        return getSqlSession().delete(this.sqlId("delete"),
                ImmutableMap.of("id", id, "tenantId", tenantId)) == 1;
    }

    /**
     * 根据店铺名称查找对应的店铺
     *
     * @param name
     * @param type
     * @param tenantId
     * @return
     */
    public Shop findByName(String name, Integer type, Integer tenantId) {
        return getSqlSession().selectOne(sqlId("findByName"),
                ImmutableMap.of("name", name, "type", type, "tenantId", tenantId));
    }

    /**
     * 根据店铺名称查找对应的店铺
     *
     * @param id
     * @param name
     * @param type
     * @param tenantId
     * @return
     */
    public Shop findByNameWithId(Long id, String name, Integer type, Integer tenantId) {
        return getSqlSession().selectOne(sqlId("findByNameWithId"), id == null
                ? ImmutableMap.of("name", name, "type", type, "tenantId", tenantId)
                : ImmutableMap.of("id", id, "name", name, "type", type, "tenantId", tenantId));
    }

    /**
     * 根据店铺名称模糊查询店铺
     *
     * @param shopNamePart 店铺名称的关键部分
     * @return 店铺列表
     */
    public List<Shop> listByNamePart(List<Long> ids,String shopNamePart, Integer type, Integer tenantId, Integer limit) {
        Map<String,Object> param = Maps.newHashMap();
        param.put("ids",ids);
        param.put("namePart",shopNamePart);
        param.put("type",type);
        param.put("tenantId",tenantId);
        param.put("limit",limit);
        return getSqlSession().selectList(sqlId("listByNamePart"),param);
    }

    /**
     * 根据店铺名称模糊查询店铺
     * @Param  ids   存在合作关系的供应商id/区域运营id
     * @param shopNamePart 店铺名称的关键部分
     * @return 店铺列表
     */
    public List<Shop> listByNamePartFilterByIds(Set<Long> ids, String shopNamePart, Integer type, Integer tenantId, Integer limit, Long ignoreId) {
        Map<String, Object> paramMap = new HashMap<>(6);
        paramMap.put("ids", ids);
        paramMap.put("namePart", shopNamePart);
        paramMap.put("type", type);
        paramMap.put("tenantId", tenantId);
        paramMap.put("limit", limit);
        paramMap.put("ignoreId", ignoreId);
        return getSqlSession().selectList(sqlId("listByNamePart"), paramMap);
    }

    /**
     * 根据店铺名称模糊查询店铺
     * @Param  ids   存在合作关系的供应商id/区域运营id
     * @param shopNamePart 店铺名称的关键部分
     * @return 店铺列表
     */
    public List<Shop> listByShopNamePartFilterByIds(Set<Long> ids, String shopNamePart, Integer type, Integer tenantId, Integer limit, Long ignoreId) {
        Map<String, Object> paramMap = new HashMap<>(6);
        paramMap.put("ids", ids);
        paramMap.put("namePart", shopNamePart);
        paramMap.put("type", type);
        paramMap.put("tenantId", tenantId);
        paramMap.put("limit", limit);
        paramMap.put("ignoreId", ignoreId);
        return getSqlSession().selectList(sqlId("listByShopNamePart"), paramMap);
    }

    /**
     * 根据外部店铺id查找对应的店铺
     *
     * @param outerId 店铺名称
     * @return 对应的店铺列表
     */
    public Shop findByOuterId(String outerId, Integer tenantId) {
        return getSqlSession().selectOne(sqlId("findByOuterId"),
                ImmutableMap.of("outerId", outerId, "tenantId", tenantId));
    }

    /**
     * 更新店铺状态
     *
     * @param shopId 店铺id
     * @param status 对应的状态
     */
    public Boolean updateStatus(Long shopId, Integer status, Integer tenantId, String updatedBy) {
        boolean isOk = getSqlSession().update(sqlId("updateStatus"), ImmutableMap.of(
                "id", shopId,
                "status", status,
                "tenantId", tenantId,
                "updatedBy", updatedBy)) == 1;
        if (isOk) {
            //配置埋点
            ESMQInjection.entryShopConfigInit(shopId);
        }
        return isOk;
    }

    /**
     * 根据店铺类型获取店铺id列表
     *
     * @param type
     * @return
     */
    public List<Shop> findByType(Integer type) {
        return getSqlSession().selectList(sqlId("findByType"), type);
    }

    /**
     * 查询店铺总数
     *
     * @param tenantId
     * @param type
     * @param status
     * @return
     */
    public Long getShopCount(Integer tenantId, Integer type, Integer status) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("tenantId", tenantId);
        params.put("type", type);
        params.put("status", status);
        return getSqlSession().selectOne(sqlId("getShopCount"), params);
    }

    public Shop queryByIndustryIdAndBusinessType(Long industryId, Integer businessType) {
        return getSqlSession().selectOne(sqlId("queryByIndustryIdAndBusinessType"),
                ImmutableMap.of("industryId", industryId, "businessType", businessType));
    }

    /**
     * 获取同一城市的区域数量
     * @param tenantId
     * @param id
     * @param pid
     * @param cityId
     * @param type
     * @return
     */
    public Integer getExistCityCount(Integer tenantId,Long id,Long pid,String cityId,Integer type,Long industryId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("tenantId", tenantId);
        params.put("id", id);
        params.put("pid", pid);
        params.put("cityId", cityId);
        params.put("type", type);
        params.put("industryId", industryId);
        Integer count= getSqlSession().selectOne(sqlId("getExistCityCount"), params);
        if(count==null)count=0;
        return count;
    }

    /**
     * 查询区域信息
     * @param tenantId
     * @param id
     * @param status
     * @return
     */
    public Shop findShopById(Integer tenantId,Long id, Short status) {
        return getSqlSession().selectOne(sqlId("findShopById"),
                ImmutableMap.of("tenantId", tenantId,"id", id, "status",status));
    }

    /**
     * 查询所有区域信息
     * @param tenantId
     * @param type
     * @param status
     * @return
     */
    public List<Shop> treeShopList(Integer tenantId,Integer type,Short status) {
        return getSqlSession().selectList(sqlId("treeShopList"),
                ImmutableMap.of("tenantId", tenantId,"status",status,"type",type));
    }

    /**
     * 区域子节点数量加一
     * @param tenantId
     * @param id
     * @param updatedBy
     * @return
     */
    public Boolean updateAddLeafCount(Integer tenantId,Long id, String updatedBy) {
        return getSqlSession().update(sqlId("updateAddLeafCount"), ImmutableMap.of(
                "tenantId", tenantId,
                "id", id,
                "updatedBy", updatedBy)) == 1;
    }

    /**
     * 区域子节点数量减一
     * @param tenantId
     * @param id
     * @param updatedBy
     * @return
     */
    public Boolean updateSubLeafCount(Integer tenantId,Long id, String updatedBy) {
        return getSqlSession().update(sqlId("updateSubLeafCount"), ImmutableMap.of(
                "tenantId", tenantId,
                "id", id,
                "updatedBy", updatedBy)) == 1;
    }

    /**
     * 获取同一城市的区域信息
     * @param tenantId
     * @param id
     * @param pid
     * @param cityId
     * @param type
     * @return
     */
    public List<Shop> getExistCityList(Integer tenantId,Long id,Long pid,String cityId,Integer type) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("tenantId", tenantId);
        params.put("id", id);
        params.put("pid", pid);
        params.put("cityId", cityId);
        params.put("type", type);
        return getSqlSession().selectList(sqlId("getExistCityList"), params);
    }

    /**
     * 获取区域ID根据域名和地址
     * @param industryId
     * @param provinceId
     * @param cityId
     * @param regionId
     * @return
     */
    public Shop queryByIndustryIdAndAddress(Long industryId, String provinceId,String cityId,String regionId) {
        return getSqlSession().selectOne(sqlId("queryByIndustryIdAndAddress"),
                ImmutableMap.of("industryId", industryId, "provinceId", provinceId,
                        "cityId", cityId,"regionId", regionId));
    }

    /**
     * 获取区域ID根据域名和地址
     * @param url
     * @param provinceId
     * @param cityId
     * @param regionId
     * @return
     */
    public Shop queryByUrlAndAddress(String url, String provinceId,String cityId,String regionId) {
        return getSqlSession().selectOne(sqlId("queryByUrlAndAddress"),
                ImmutableMap.of("url", url, "provinceId", provinceId,
                        "cityId", cityId,"regionId", regionId));
    }

    public List<Shop> listByUrlAndAddress(String url, String provinceId,String cityId,String regionId) {
        return getSqlSession().selectList(sqlId("listByUrlAndAddress"),
                ImmutableMap.of("url", url, "provinceId", provinceId,
                        "cityId", cityId,"regionId", regionId));
    }

    /**
     * 获取区域ID根据域名
     * @param url
     * @return
     */
    public Shop queryByUrl(String url) {
        return getSqlSession().selectOne(sqlId("queryByUrl"),
                ImmutableMap.of("url", url));
    }

    /**
     * 获取区域ID根据key
     * @param key
     * @return
     */
    public Shop queryByKey(String key) {
        return getSqlSession().selectOne(sqlId("queryByKey"),
                ImmutableMap.of("key", key));
    }

    /**
     * 获取区域ID根据域名
     * @param url
     * @return
     */
    public List<Shop> listByUrl(String url) {
        return getSqlSession().selectList(sqlId("listByUrl"),
                ImmutableMap.of("url", url));
    }

    /**
     * 获取国代区域ID根据行业
     * @param industryId
     * @return
     */
    public Shop queryByIndustryId(Long industryId) {
        return getSqlSession().selectOne(sqlId("queryByIndustryId"),
                ImmutableMap.of("industryId", industryId));
    }

    /**
     * 获取国代区域ID根据行业
     * @param industryId
     * @return
     */
    public List<Shop> listByIndustryId(Long industryId) {
        return getSqlSession().selectList(sqlId("listByIndustryId"),
                ImmutableMap.of("industryId", industryId));
    }

    /**
     * 根据APPID取区域运营
     * @param appId
     * @return
     */
    public List<Shop> listByAppId(String appId) {
        return getSqlSession().selectList(sqlId("listByAppId"),
                ImmutableMap.of("appId", appId));
    }

    /**
     * 根据APPID、行业、域名取区域运营
     * @param appId
     * @return
     */
    public List<Shop> listByAppIndustryUrl(String appId, Long industryId, String url) {
        return getSqlSession().selectList(sqlId("listByAppIndustryUrl"),
                ImmutableMap.of("appId", appId, "industryId", industryId, "url", url));
    }

    public List<Shop> listByZqAndIsBrandAndIsDeliery(Map<String, Object> params){
        return getSqlSession().selectList(sqlId("listByZqAndIsBrandAndIsDeliery"),params);
    }

    /**
     * 修改政企供应商属性信息
     * @param id
     * @param vendorType
     * @param isBrand
     * @param isDeliery
     * @param updatedBy
     * @param tenantId
     * @return
     */
    public Boolean updateZqAttr(Long id, Integer vendorType,
                                String isBrand,String isDeliery,
                                String updatedBy,Integer tenantId) {

        Map<String,Object> paramas = new HashMap<>();
        paramas.put("id",id);
        paramas.put("vendorType",vendorType);
        paramas.put("isBrand",isBrand);
        paramas.put("isDeliery",isDeliery);
        paramas.put("updatedBy",updatedBy);
        paramas.put("tenantId",tenantId);

        return getSqlSession().update(sqlId("updateZqAttr"), paramas) == 1;
    }

    public List<Shop> findChildsByPid(Long pid,Integer tenantId) {
        return getSqlSession().selectList(sqlId("findChildsByPid"),
                ImmutableMap.of("tenantId", tenantId,"pid", pid));
    }

    /**
     * 根据id查询供应商入驻状态
     * @param id
     * @return
     */
    public Integer queryProviderStatus(Long id){
        return getSqlSession().selectOne(sqlId("findShopCountById"),id);
    }

    public List<VendorWithPartnerShipInfo> getList(Map<String, Object> params) {
        return getSqlSession().selectList(sqlId("getList"),params);
    }

    public Boolean setDefaultLogisticsUpdateVendorExtra(Map<String, Object> mp) {
        return getSqlSession().update(sqlId("setDefaultLogisticsUpdateVendorExtra"),mp)>0;
    }


    public List<Shop> selOpera(Map<String, Object> map) {
        return getSqlSession().selectList(sqlId("selOpera"),map);
    }

    public Boolean upByOpera(Map<String, Object> map) {
        boolean isOk = getSqlSession().update(sqlId("upByOpera"),map) > 0;
        if (isOk) {
            //配置埋点
            ESMQInjection.entryShopConfigInit(Long.valueOf(map.get("id").toString()));
        }
        return isOk;
    }
    /**
     * 查询所有运营商
     */
    public List<Shop> querySeller(Map<String, Object> map) {
        return getSqlSession().selectList(sqlId("querySeller"),map);
    }

    public List<Long> findAllOperatorId() {
        return getSqlSession().selectList(sqlId("findAllOperatorId"));
    }

    public Paging<Shop> pagingForConfig(Integer offset, Integer limit, Map<String, Object> criteria) {
        Long total = this.sqlSession.selectOne(this.sqlId("count"), criteria);
        if (total <= 0L) {
            return new Paging(0L, Collections.emptyList());
        } else {
            criteria.put("offset", offset);
            criteria.put("limit", limit);
            List<Shop> datas = this.sqlSession.selectList(this.sqlId("pagingForConfig"), criteria);
            return new Paging(total, datas);
        }
    }

    /**
     * 根据类型和条件查询店铺信息
     *
     * @param params 查询参数
     * @return 分页结果
     */
    public Paging<Shop> findByTypeAndCondition(Map<String, Object> params) {
        Integer pageNo = (Integer) params.get("pageNo");
        Integer pageSize = (Integer) params.get("pageSize");
        Integer offset = (pageNo - 1) * pageSize;

        Long total = this.sqlSession.selectOne(this.sqlId("countByTypeAndCondition"), params);
        if (total <= 0L) {
            return new Paging(0L, Collections.emptyList());
        } else {
            params.put("offset", offset);
            params.put("limit", pageSize);
            List<Shop> datas = this.sqlSession.selectList(this.sqlId("findByTypeAndCondition"), params);
            return new Paging(total, datas);
        }
    }
}

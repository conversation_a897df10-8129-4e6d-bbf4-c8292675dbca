/*
 * Copyright (c) 2018. 杭州端点网络科技有限公司.  All rights reserved.
 */

package io.terminus.parana.item.shop.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.parana.common.utils.ImageUrlHandler;
import io.terminus.parana.item.common.base.BaseModel;
import lombok.Data;

import java.util.Collections;
import java.util.Date;
import java.util.Map;

/**
 * 店铺
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-27SkuManager
 */
@Data
public class Shop extends BaseModel {

    private static final long serialVersionUID = 2240906413277397381L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 外部店铺id
     */
    private String outerId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 店铺全称
     */
    private String shopNames;

    /**
     * 状态 1:正常, -1:关闭, -2:冻结
     */
    private Integer status;

    /**
     * 店铺类型,各应用自己定义
     */
    private Integer type;

    /**
     * 电话
     */
    private String phone;

    /**
     * 店铺主图
     */
    private String imageUrl;

    /**
     * 店铺地址
     */
    private String address;

    /**
     * 店铺地址
     */
    private String email;

    /**
     * 放店铺扩展信息, json存储, 存数据库
     */
    private String extraJson;

    /**
     * 放店铺扩展信息,不存存数据库
     */
    private Map<String, String> extra;

    /**
     * 店铺本身的tag信息, 由运营操作
     */
    private Long tags;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 最后更新人
     */
    private String updatedBy;

    /**
     * 行业id
     */
    private Long industryId;

    /**
     * 纳税人识别号
     */
    private String tin;

    /**
     * 父ID
     */
    private Long pid;

    /**
     * 编码
     */
    private String code;

    /**
     * 叶子节点数量
     */
    private Integer leafCount;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     *
     */
    private String pcUrl;

    /**
     *
     */
    private String wapUrl;

    /**
     * 特殊区域运营商类型
     */
    private Integer specialOperatorType;

    /**
     * 特殊区域运营商编码
     */
    private String specialOperatorKey;

    private String qrcode;

    private String pidStr;

    /***
     * appId
     */
    private String appId;

    /***
     * appName
     */
    private String appName;

    /**
     * 经营区域
     */
    private String operatingArea;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 配送范围
     */
    private String deliveryArea;

    /**
     * 配送费用
     */
    private String shippingFee;

    /**
     * 退货规则
     */
    private String returnRule;

    /**
     * 纳税人类型
     */
    private String taxpayerType;

    /**
     * 纳税类型税码
     */
    private String taxpayerTax;

    /**
     * 图片宽
     */
    private Long width;

    /**
     * 图片高
     */
    private Long height;
    /**
     * 默认Y  注：Y_品牌供应商，N_非品牌供应商
     */
    private String isBrand;
    /**
     * 默认N  注：Y_配送，N_不配送
     */
    private String isDeliery;
    /**
     * 默认N  注：Y_政企，N_非政企
     */
    private String isZq;

    /**
     * 默认Y 注：Y_平台,N_运营商
     */
    private String isOp;

    /**
     * 供应商类型 1-厂家 2-代理商
     */
    private Integer vendorType;

    /**
     * sd价格绑定类型 1：取sd价格 取sd促销 2：取sd价格 取电商促销 3：内部价格(不走sd库存)
     */
    private Integer sdPriceType;

    /**
     * sd库存绑定类型 1：取sd库存 2：不取sd库存
     */
    private Integer sdInventoryType;

    private String regionalOperationName;

    private Long operatorId;

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getImageUrl() {
        return ImageUrlHandler.simplify(this.imageUrl);
    }

    @JsonProperty("imageUrl")
    public String getImageUrl_() {
        return this.imageUrl;
    }

    @JsonSetter
    public void setImageUrl(String imageUrl) {
        this.imageUrl = ImageUrlHandler.complete(imageUrl);
    }

    public void setExtraJson(String extraJson) throws Exception {
        this.extraJson = extraJson;
        this.extra = json2object(extraJson, MAP_OF_STRING, Collections::emptyMap, "");
    }

    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        this.extraJson = object2json(extra, "");
    }
}

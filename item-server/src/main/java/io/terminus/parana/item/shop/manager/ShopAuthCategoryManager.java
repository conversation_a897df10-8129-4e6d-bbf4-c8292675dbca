package io.terminus.parana.item.shop.manager;

import io.terminus.parana.item.shop.model.ShopAuthCategory;
import io.terminus.parana.item.shop.repository.ShopAuthCategoryDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-05-06
 */
@Component
@RequiredArgsConstructor
public class ShopAuthCategoryManager {

    private final ShopAuthCategoryDao shopAuthCategoryDao;

    @Transactional(rollbackFor = Exception.class)
    public void flushShopAuthCategory(Long shopId, List<ShopAuthCategory> authCategoryList) {
        shopAuthCategoryDao.deleteByShopId(shopId);

        if (!CollectionUtils.isEmpty(authCategoryList)) {
            shopAuthCategoryDao.creates(authCategoryList);
        }
    }
}

package io.terminus.parana.item.export.manager;

import io.terminus.parana.item.util.ExcelAnnotation;
import io.terminus.parana.item.util.excel.annotation.ExcelModel;
import io.terminus.parana.item.util.excel.annotation.ExcelSingleField;
import io.terminus.parana.item.web.excel.AbstractExcelImportModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@ExcelModel
@EqualsAndHashCode(callSuper = true)
public class ItemBatchUpdateExcelImportBo extends AbstractExcelImportModel implements Serializable {

    private static final long serialVersionUID = 6995376571949953459L;


    @ExcelSingleField(columnPosition = 0,columnName = "商品ID",required = false)
    @ExcelAnnotation(columnIndex = 0, columnName = "商品ID", isLock = false)
    private String itemId;

    @ExcelSingleField(columnPosition = 1,columnName = "商品名称",required = false)
    @ExcelAnnotation(columnIndex = 1, columnName = "商品名称", isLock = false)
    private String itemName;

    @ExcelSingleField(columnPosition = 2,columnName = "广告语",required = false)
    @ExcelAnnotation(columnIndex = 2, columnName = "广告语", isLock = false)
    private String advertise;

    @ExcelSingleField(columnPosition = 3,columnName = "开票名称",required = false)
    @ExcelAnnotation(columnIndex = 3, columnName = "开票名称", isLock = false)
    private String universalName;

    @ExcelSingleField(columnPosition = 4,columnName = "关键词",required = false)
    @ExcelAnnotation(columnIndex = 4,columnName = "关键词",isLock = false)
    private String keyword;

    @ExcelSingleField(columnPosition = 5,columnName = "税收分类编码",required = false)
    @ExcelAnnotation(columnIndex = 5,columnName = "税收分类编码",isLock = false)
    private String taxCode;

    @ExcelSingleField(columnPosition = 6,columnName = "税率",required = false)
    @ExcelAnnotation(columnIndex = 6,columnName = "税率",isLock = false)
    private String vatrate;

    @ExcelSingleField(columnPosition = 7,columnName = "品牌",required = false)
    @ExcelAnnotation(columnIndex = 7,columnName = "品牌",isLock = false)
    private String brandName;

    @ExcelSingleField(columnPosition = 8,columnName = "包装单位",required = false)
    @ExcelAnnotation(columnIndex = 8,columnName = "包装单位",isLock = false)
    private String unit;

    @ExcelSingleField(columnPosition = 9,columnName = "比价链接",required = false)
    @ExcelAnnotation(columnIndex = 9,columnName = "比价链接",isLock = false)
    private String zqbjurl1;

    @ExcelSingleField(columnPosition = 10,columnName = "快递运费模板",required = false)
    @ExcelAnnotation(columnIndex = 10,columnName = "快递运费模板",isLock = false)
    private String deliveryFeeTempName;

    @ExcelSingleField(columnPosition = 11,columnName = "是否可退货",required = false)
    @ExcelAnnotation(columnIndex = 11,columnName = "是否可退货",isLock = false)
    private String supportReturn;

    @ExcelSingleField(columnPosition = 12,columnName = "规格ID",required = false)
    @ExcelAnnotation(columnIndex = 12,columnName = "规格ID",isLock = false)
    private String skuId;

    @ExcelSingleField(columnPosition = 13,columnName = "国际编码",required = false)
    @ExcelAnnotation(columnIndex = 13,columnName = "国际编码",isLock = false)
    private String skuCode;

    @ExcelSingleField(columnPosition = 14,columnName = "规格条码",required = false)
    @ExcelAnnotation(columnIndex = 14,columnName = "规格条码",isLock = false)
    private String barcode;

    @ExcelSingleField(columnPosition = 15,columnName = "商家编码",required = false)
    @ExcelAnnotation(columnIndex = 15,columnName = "商家编码",isLock = false)
    private String custSkuCode;

    @ExcelSingleField(columnPosition = 16,columnName = "起售数量",required = false)
    @ExcelAnnotation(columnIndex = 16,columnName = "起售数量",isLock = false)
    private String minQuantity;

    @ExcelSingleField(columnPosition = 17,columnName = "是否启用",required = false)
    @ExcelAnnotation(columnIndex = 17,columnName = "是否启用",isLock = false)
    private String status;

    @ExcelSingleField(columnPosition = 18,columnName = "导入结果",required = false)
    @ExcelAnnotation(columnIndex = 18,columnName = "导入结果",isLock = false)
    private String results;

    @ExcelSingleField(columnPosition = 19,columnName = "异常信息",required = false)
    @ExcelAnnotation(columnIndex = 19,columnName = "异常信息",isLock = false)
    private String reason;


}

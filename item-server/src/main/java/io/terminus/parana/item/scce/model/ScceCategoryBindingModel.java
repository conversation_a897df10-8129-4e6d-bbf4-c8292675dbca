package io.terminus.parana.item.scce.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class ScceCategoryBindingModel implements Serializable{

	/**
	 * 
	 */
	private Integer id;
	/**
	 * 
	 */
	private Long vendorId;
	/**
	 * 
	 */
	private Long categoryId;
	/**
	 * 
	 */
	private Long categoryId2;
	/**
	 * 
	 */
	private Long categoryId3;
	/**
	 * 
	 */
	private Long scceCategoryId;
	/**
	 * 
	 */
	private Long scceCategoryId2;
	/**
	 * 
	 */
	private Long scceCategoryId3;
	/**
	 * 创建人
	 */
	private Long createdBy;
	/**
	 * 创建时间
	 */
	private java.util.Date createdAt;
	/**
	 * 修改人
	 */
	private Long updatedBy;
	/**
	 * 修改时间
	 */
	private java.util.Date updatedAt;
}

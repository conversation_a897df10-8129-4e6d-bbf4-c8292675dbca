package io.terminus.parana.item.brand.api.service;

import io.terminus.parana.item.brand.api.bean.request.OuterBrandCreateRequest;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandDeleteRequest;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandExcelImportRequest;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandUpdateRequest;
import io.terminus.parana.item.brand.api.manager.OuterBrandManager;
import io.terminus.parana.item.brand.model.OuterBrand;
import io.terminus.parana.item.brand.repository.OuterBrandDAO;
import io.terminus.parana.item.category.util.Constant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version :2021-01-12 14:49:31
 */
@Service
public class OuterBrandWriteService {

    @Autowired
    private OuterBrandManager outerBrandManager;

    @Autowired
    private OuterBrandDAO outerBrandDAO;

    public Long create(OuterBrandCreateRequest paranaOuterBrand) {

        return outerBrandManager.create(paranaOuterBrand);
    }

    public Boolean create(OuterBrand outerBrand) {
        return outerBrandDAO.create(outerBrand);
    }


    public Boolean delete(OuterBrandDeleteRequest paranaOuterBrand) {

        return outerBrandManager.delete(paranaOuterBrand);
    }


    public Boolean updateOuterBrand(OuterBrandUpdateRequest outerBrandUpdateRequest) {
        outerBrandUpdateRequest.setOperatorId(Constant.ZQ_DEFAULT_OPERATOR_ID);
        return outerBrandManager.updateOuterBrand(outerBrandUpdateRequest);
    }


    public Boolean OuterBrandExcelImport(List<OuterBrandExcelImportRequest> request) {

        return outerBrandManager.OuterBrandExcelImport(request);
    }


    public Boolean recoverOuterBrand(OuterBrandDeleteRequest outerBrandDeleteRequest) {

        return outerBrandManager.recoverOuterBrand(outerBrandDeleteRequest);

    }

    public Boolean update(OuterBrand outerBrand) {
        return outerBrandDAO.update(outerBrand);
    }

    public Boolean creates(List<OuterBrand> createList) {
        return outerBrandDAO.creates(createList) > 0;
    }
}
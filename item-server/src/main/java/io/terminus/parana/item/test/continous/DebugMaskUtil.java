package io.terminus.parana.item.test.continous;

import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-21
 */
@Slf4j
public class DebugMaskUtil {

    private static final String MASK_PATH = "/tmp/test.debug";
    private static Integer PORT;

    public static void maskDebugMode(Integer port) {
        if (debugMode()) {
            return;
        }

        try {
            File file = new File(MASK_PATH);

            if (!file.exists()) {
                FileWriter fw = new FileWriter(file);
                fw.write(String.valueOf(port));
                fw.flush();
                fw.close();
                PORT = port;
            }
        } catch (IOException e) {
            log.error(Throwables.getStackTraceAsString(e));
        }

        log.info("Success to write debug mask with expose port: {}", port);
    }

    public static boolean debugMode() {
        return new File(MASK_PATH).exists();
    }

    public static Integer getPort() {
        if (PORT == null && debugMode()) {

            try (FileReader fr = new FileReader(MASK_PATH)) {
                char[] buffer = new char[32];

                int size = fr.read(buffer);
                if (size < 1) {
                    return null;
                }

                String value = new String(buffer, 0, size);

                PORT = StringUtils.isEmpty(value) ? null : Integer.parseInt(value);
            } catch (Exception e) {
                log.error("Fail to resolve mask file");
            }
        }

        return PORT;
    }

    public static void removeDebugMode() {
        if (!debugMode()) {
            return;
        }

        File file = new File(MASK_PATH);

        if (file.exists()) {
            file.delete();
        }
    }
}

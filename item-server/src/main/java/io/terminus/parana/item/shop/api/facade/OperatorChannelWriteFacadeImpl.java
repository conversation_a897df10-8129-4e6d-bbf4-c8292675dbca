package io.terminus.parana.item.shop.api.facade;

import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.shop.api.bean.request.OperatorChannelAddRequest;
import io.terminus.parana.item.shop.api.bean.request.OperatorChannelUpdateRequest;
import io.terminus.parana.item.shop.api.converter.OperatorChannelApiConverter;
import io.terminus.parana.item.shop.model.OperatorChannel;
import io.terminus.parana.item.shop.service.OperatorChannelReadService;
import io.terminus.parana.item.shop.service.OperatorChannelWriteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
@RequiredArgsConstructor
public class OperatorChannelWriteFacadeImpl implements OperatorChannelWriteFacade {

    private final OperatorChannelWriteService operatorChannelWriteService;

    private final OperatorChannelReadService operatorChannelReadService;

    private final OperatorChannelApiConverter operatorChannelApiConverter;

    private final IdGenerator idGenerator;

    @Override
    public Response<Boolean> addOperatorChannel(OperatorChannelAddRequest operatorChannelAddRequest) {
        //一个区域运营只能进行一次渠道配置
        OperatorChannel query = new OperatorChannel();
        query.setOperatorId(operatorChannelAddRequest.getOperatorId());
        Long count = operatorChannelReadService.count(query);
        if (count > 0){
            throw new ServiceException("operator.channel.already.exists");
        }
        OperatorChannel operatorChannel = operatorChannelApiConverter.request2domain(operatorChannelAddRequest);
        operatorChannel.setId(idGenerator.nextValue(OperatorChannel.class));
        operatorChannel.setCreatedAt(new Date());
        operatorChannel.setUpdatedAt(new Date());
        return Response.ok(operatorChannelWriteService.add(operatorChannel));
    }

    @Override
    public Response<Boolean> updateOperatorChannel(OperatorChannelUpdateRequest operatorChannelUpdateRequest) {
        OperatorChannel operatorChannel = operatorChannelApiConverter.request2domain(operatorChannelUpdateRequest);
        operatorChannel.setUpdatedAt(new Date());
        return Response.ok(operatorChannelWriteService.update(operatorChannel));
    }

}

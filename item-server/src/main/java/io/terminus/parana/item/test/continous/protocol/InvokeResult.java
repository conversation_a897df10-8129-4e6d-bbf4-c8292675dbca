package io.terminus.parana.item.test.continous.protocol;

import lombok.Data;

import java.io.Serializable;

/**
 * 单元测试代理请求结果
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-21
 */
@Data
public class InvokeResult implements Serializable {
    private static final long serialVersionUID = -7853802301798326557L;

    /**
     * 执行是否成功
     */
    private Boolean success;

    /**
     * 发生的错误堆栈(转下吧，不然序列化比较尴尬)
     */
    private InvokeFailedException exception;
}

package io.terminus.parana.item.shop.service;

import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.api.utils.StringUtil;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.search.request.SupplierContractRequest;
import io.terminus.parana.item.shop.api.bean.request.UpdateUserIdRequest;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.model.SupplierInfoLogModel;
import io.terminus.parana.item.shop.model.SupplierInfoModel;
import io.terminus.parana.item.shop.repository.SupplierInfoDao;
import io.terminus.parana.item.shop.repository.SupplierInfoLogDao;
import io.terminus.parana.item.shop.util.ShopIdGenerateHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
@Slf4j
@Component
@RequiredArgsConstructor
public class SupplierInfoWriteService {

	private final SupplierInfoDao supplierInfoDao;
	private final ShopIdGenerateHelper shopIdGenerateHelper;
	private final ShopWriteDomainService shopWriteDomainService;
	private final SupplierInfoLogDao supplierInfoLogDao;
	@Autowired
	private IdGenerator idGenerator;
//	private final OperationJournalDao operationJournalDao;
	/**
	 * 创建供应商信息表
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean create(SupplierInfoModel model, Shop shop) {
		if (StringUtil.isEmpty(shop.getIsBrand())){
			shop.setIsBrand("Y");
		}
		if (StringUtil.isEmpty(shop.getIsDeliery())){
			shop.setIsDeliery("N");
		}
		Long shopId = shopWriteDomainService.create(shop);
		model.setId(shopId);
		return supplierInfoDao.createModel(model);
	}
	/**
	 * 修改供应商信息表
	 */
	public Boolean update(SupplierInfoModel model) {
		return supplierInfoDao.updateModel(model);
	}
	/**
	 * 删除供应商信息表
	 */
	public Boolean delete(SupplierInfoModel model) {
		return supplierInfoDao.deleteById(model);
	}

	public void updateUserId(UpdateUserIdRequest request) {
		Map<String, Object> criteria = Maps.newHashMap();
		criteria.put("id",request.getId());
		criteria.put("userId",request.getUserId());
		supplierInfoDao.updateUserId(criteria);
	}

//	@Transactional(rollbackFor = Throwable.class)
//	public void updateCompanyInfoAndSaveJournal(SupplierInfoModel model, OperationJournal operationJournal) {
//		if(update(model)){ operationJournalDao.create(operationJournal); }
//	}

	public Boolean contract(SupplierContractRequest request){
		Map<String, Object> criteria = new HashMap<>();
		criteria.put("id",request.getId());
		criteria.put("contract",request.getContract());
		return supplierInfoDao.contract(criteria);
	}

	public Map<String,Object> createVendor(SupplierInfoModel model, Shop shop) {
		if (StringUtil.isEmpty(shop.getIsBrand())){
			shop.setIsBrand("Y");
		}
		if (StringUtil.isEmpty(shop.getIsDeliery())){
			shop.setIsDeliery("N");
		}
		Long shopId = shopWriteDomainService.createVendor(shop);
		model.setId(shopId);
		log.info("SupplierInfoModel:{}",model);
		Map<String,Object> map = Maps.newHashMap();
		map.put("flag",supplierInfoDao.createModel(model));
		map.put("shopId",shopId);

		//添加消息记录
		try {
			SupplierInfoLogModel supplierInfoLogModel = new SupplierInfoLogModel();
			BeanUtils.copyProperties(model,supplierInfoLogModel);
			supplierInfoLogModel.setVendorId(model.getId());
			supplierInfoLogModel.setId(idGenerator.nextValue(SupplierInfoLogModel.class,supplierInfoLogModel.getVendorId()));
			supplierInfoLogDao.createModel(supplierInfoLogModel);
		} catch (BeansException e) {
			log.error("记录创建失败::{}", Throwables.getStackTraceAsString(e));
		}
		return map;
	}

	public Boolean createOperator(SupplierInfoModel model, Shop shop) {
		model.setId(shop.getId());
		model.setUserId(0L);
		model.setAuditStatus(2);
		model.setStateLocked(0);
		model.setOperatorId(0L);
		log.info("SupplierInfoModel:{}", model);
		return supplierInfoDao.createModel(model);
	}


	public Boolean createSup(SupplierInfoModel model) {
		log.info("SupplierInfoModel:{}",model);
		return supplierInfoDao.createModel(model);
	}

	public Boolean updateVendorDetailAuditTime(Long id){
		return supplierInfoDao.updateVendorDetailAuditTime(id);
	}
}

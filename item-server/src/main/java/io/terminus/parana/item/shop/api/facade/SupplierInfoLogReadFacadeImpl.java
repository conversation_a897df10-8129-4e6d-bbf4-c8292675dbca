package io.terminus.parana.item.shop.api.facade;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import io.terminus.parana.item.shop.api.bean.request.SupplierInfoLogPageRequest;
import io.terminus.parana.item.shop.api.bean.request.SupplierInfoLogQueryRequest;
import io.terminus.parana.item.shop.api.bean.response.SupplierInfoLogInfoResponse;
import io.terminus.parana.item.shop.api.converter.SupplierInfoLogApiConverter;
import io.terminus.parana.item.shop.model.SupplierInfoLogModel;
import io.terminus.parana.item.shop.service.SupplierInfoLogReadService;
import org.springframework.stereotype.Service;


import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.RequiredArgsConstructor;
import cn.hutool.json.JSONUtil;

@Service
@RequiredArgsConstructor
public class SupplierInfoLogReadFacadeImpl implements SupplierInfoLogReadFacade {

	private final SupplierInfoLogReadService supplierInfoLogReadService;
	private final SupplierInfoLogApiConverter supplierInfoLogApiConverter;

	@Override
	public Response<SupplierInfoLogInfoResponse> view(SupplierInfoLogQueryRequest request) {
		SupplierInfoLogModel model = supplierInfoLogReadService.view(supplierInfoLogApiConverter.get(request));
		if(model == null){
			model = new SupplierInfoLogModel();
		}
		return Response.ok(supplierInfoLogApiConverter.model2InfoResponse(model));
	}

	@Override
	public Response<List<SupplierInfoLogInfoResponse>> list(SupplierInfoLogQueryRequest request) {
		List<SupplierInfoLogModel> modelList = supplierInfoLogReadService.list(supplierInfoLogApiConverter.get(request));
		if(modelList == null){
			modelList = new ArrayList<SupplierInfoLogModel>();
		}
		return Response.ok(supplierInfoLogApiConverter.modelList2InfoResponseList(modelList));
	}

	@Override
	public Response<Paging<SupplierInfoLogInfoResponse>> page(SupplierInfoLogPageRequest request) {
		Map<String, Object> params = JSONUtil.parseObj(request);
		PageInfo pageInfo = new PageInfo(request.getPageNo(), request.getPageSize());
		Paging<SupplierInfoLogModel> modelPage = supplierInfoLogReadService.page(params, pageInfo.getOffset(), pageInfo.getLimit());
		return Response.ok(supplierInfoLogApiConverter.modePage2InfoPage(modelPage));
	}

	@Override
	public Response<SupplierInfoLogInfoResponse> getViewByVendorId(SupplierInfoLogQueryRequest request) {
		SupplierInfoLogModel model = supplierInfoLogReadService.getViewByVendorId(request.getId());
		if(model == null){
			model = new SupplierInfoLogModel();
		}
		return Response.ok(supplierInfoLogApiConverter.model2InfoResponse(model));
	}

}

package io.terminus.parana.item.common.schedule.scan.process;

import io.terminus.common.exception.ServiceException;

/**
 * 线程运行回调
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-09-02
 */
public interface TaskBurstCallback {

    /**
     * 任务分片开始
     *
     * @param identityId 标识id
     * @param workKind   工作类型
     */
    void onStart(long identityId, WorkKind workKind);

    /**
     * 线程执行完成通知
     *
     * @param identityId 标识id
     * @param workKind   工作类型
     */
    void onDone(long identityId, WorkKind workKind);

    /**
     * 线程发生错误通知
     *
     * @param identityId 标识id
     * @param workKind   工作类型
     * @param exception  错误信息
     */
    void onError(long identityId, WorkKind workKind, ServiceException exception);
}

package io.terminus.parana.item.common.cache;

import com.alicp.jetcache.Cache;

/**
 * 缓存的基础定义接口。
 * <p>
 * 缓存的基础定义，定义了缓存的获取，放入以及删除操作
 * </p>
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-15
 */
public interface CacheOperate<TR> {

    /**
     * 获取{@link Cache}实例
     * <p>
     * 缓存实例的获取，参见jetcache中相关内容。包括两种创建方式：<br>
     * 1. 基于注解形式的自动创建，参见{@link com.alicp.jetcache.anno.CreateCache}<br>
     * 2. 基于代码的手动创建
     * </p>
     *
     * @return {@link Cache}实例
     * @see Cache
     */
    Cache<String, TR> getCacheStack();

    /**
     * 获得缓存对象的命名空间
     * <p>
     * 命名空间用来区分模型对象
     * </p>
     *
     * @return 命名空间
     */
    String getNamespace();
}

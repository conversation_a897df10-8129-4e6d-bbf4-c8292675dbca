package io.terminus.parana.item.common.cache.plugin;

import java.util.Collection;
import java.util.List;

/**
 * 基于{@link MultiCache}接口的扩展，增加通过第一个参数批量获取数据的能力。
 * <p>
 * 注意：当TR为集合时，将导致数据结构异常，例：List<List<Long>>。此时建议使用{@link SimpleCache}接口，
 * 通过缓存更多数据，在获得结果后代码过滤处理。
 * </p>
 * <p>
 * 接口依赖{@link #get(Object, Object...)}方法，提供基于第一个参数<strong>TI1</strong>的批量查询和缓存能力。数据源
 * 需要保持与该方法的统一形式缓存。在解析<strong>TI2</strong>值时，依赖用户提供的解析方法{@link #ti2Parser(Object)}。
 * </p>
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-23
 */
public interface BatchedMultiCache<TI1, TI2, TR> extends MultiCache<TI1, TI2, TR> {

    /**
     * 通过第一个参数(<b>类型为TI1</b>)获取结果集合
     * <p>
     * <strong>注意：此方法不支持空结果缓存！不支持空结果缓存！不支持空结果缓存！</strong>
     * <br>
     * <br>
     * <p>
     * 在设计上，此接口具有较高的复杂度。当调用方法{@link #get(Object, Object, Object...)}时，通过{@link IndexCache}将完成从
     * <strong>TI1</strong>到<strong>TI2</strong>的映射缓存。映射缓存的主要目的在于，调用{@link #remove(Object)}方法时，可以
     * 自动的寻找缓存中的<strong>keys集合</strong>，并完成相关缓存的自动清除操作。但是当调用本方法时，映射缓存将变的不安全，原因在
     * 于它无法感知数据库中<strong>TI1</strong>下的其它映射关系。因此，采用数据库读取开关的方式，当数据库发生过读取后，认为缓存可靠
     * （前提是映射缓存的超时时间是大于当前对象缓存的，默认情况下映射缓存的超时时间为1小时，而对象的缓存时间一般为15分钟），如果未采用
     * 默认时间，则需要自行保证映射缓存的超时时间是明显长于对象缓存时间的。
     * </p>
     * </p>
     *
     * @param input 第一个参数
     * @param args  附加参数
     * @return 集合结果
     */
    Collection<TR> get(TI1 input, Object... args);

    /**
     * 从数据库中获取结果集合
     *
     * @param input 第一个参数
     * @param args  附加参数
     * @return List类型的结果集合
     */
    List<TR> databaseGet(TI1 input, Object... args);

    /**
     * 从TR结果中解析获得TI2值
     *
     * @param tr TR结果
     * @return TI2的值
     */
    TI2 ti2Parser(TR tr);
}

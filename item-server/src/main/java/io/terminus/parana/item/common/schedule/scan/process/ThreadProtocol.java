package io.terminus.parana.item.common.schedule.scan.process;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-09-02
 */
public interface ThreadProtocol {

    /**
     * 注册线程运行回调
     *
     * @param callback 回调
     */
    void registerCallback(TaskBurstCallback callback);

    /**
     * 获取标识id
     *
     * @return 标识id
     */
    long getIdentityId();

    /**
     * 设置标识id
     *
     * @param identityId 标识id
     */
    void setIdentityId(long identityId);
}

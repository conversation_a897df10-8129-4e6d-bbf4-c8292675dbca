package io.terminus.parana.item.common.canal;

public class MQSendRecord {
	private String topic;
	private String tag;
	private MQPayloadBase payload;

	public MQSendRecord() {
		
	}
	
	public MQSendRecord(String topic, String tag, MQPayloadBase payload) {
		this.topic = topic;
		this.tag = tag;
		this.payload = payload;
	}

	public String getTopic() {
		return topic;
	}

	public void setTopic(String topic) {
		this.topic = topic;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public MQPayloadBase getPayload() {
		return payload;
	}

	public void setPayload(MQPayloadBase payload) {
		this.payload = payload;
	}

}

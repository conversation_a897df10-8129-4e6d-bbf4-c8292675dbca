package io.terminus.parana.item.shop.extension;

import io.terminus.parana.item.common.extension.ExtensionResult;
import io.terminus.parana.item.shop.model.Shop;

/**
 * 店铺写扩展
 *
 * <AUTHOR>
 */
public interface ShopDomainWriteExtension {

    /**
     * 店铺创建扩展（非事务）
     *
     * @param shop
     * @return
     */
    default ExtensionResult beforeCreate(Shop shop) {
        return ExtensionResult.internal();
    }

    default ExtensionResult afterCreate(Shop shop) {
        return ExtensionResult.internal();
    }

    /**
     * 店铺更新扩展（非事务）
     *
     * @param shop
     * @return
     */
    default ExtensionResult beforeUpdate(Shop shop) {
        return ExtensionResult.internal();
    }

    default ExtensionResult afterUpdate(Shop shop) {
        return ExtensionResult.internal();
    }

}

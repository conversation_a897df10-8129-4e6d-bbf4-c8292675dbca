package io.terminus.parana.item.common.schedule.scan;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-09-02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScanTaskProgress {

    private static final ScanTaskProgress DONE = new ScanTaskProgress(0L, false, false);

    private Long processed;

    private Boolean done;

    private Boolean error;

    public static ScanTaskProgress done() {
        return DONE;
    }
}

package io.terminus.parana.item.common.activity.manager;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.activity.model.CommonActivityChannelBinding;
import io.terminus.parana.item.common.activity.model.CommonActivityGoodsBinding;
import io.terminus.parana.item.common.activity.model.CommonActivityManage;
import io.terminus.parana.item.common.activity.repository.CommonActivityChannelBindingDao;
import io.terminus.parana.item.common.activity.repository.CommonActivityGoodsBindingDao;
import io.terminus.parana.item.common.activity.repository.CommonActivityManageDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
@RequiredArgsConstructor
public class CommonActivityManager {

    private final CommonActivityManageDao commonActivityManageDao;
    private final CommonActivityGoodsBindingDao commonActivityGoodsBindingDao;
    private final CommonActivityChannelBindingDao commonActivityChannelBindingDao;

    @Transactional(rollbackFor = Exception.class)
    public boolean createCommonActivityAndItem(CommonActivityManage commonActivityManage,
                                               List<CommonActivityGoodsBinding> commonActivityGoodsBindings,
                                               List<CommonActivityChannelBinding> commonActivityChannelBindings) {
        if (!commonActivityManageDao.create(commonActivityManage)) {
            throw new ServiceException("commonActivityManage create fail");
        }
        if (!CollectionUtils.isEmpty(commonActivityGoodsBindings) &&
                commonActivityGoodsBindingDao.creates(commonActivityGoodsBindings) < 1) {
            throw new ServiceException("commonActivityGoodsBindings creates fail");
        }
        if (!CollectionUtils.isEmpty(commonActivityChannelBindings) &&
                commonActivityChannelBindingDao.creates(commonActivityChannelBindings) < 1) {
            throw new ServiceException("commonActivityChannelBindings creates fail");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateCommonActivityAndItem(CommonActivityManage commonActivityManage,
                                               List<CommonActivityGoodsBinding> commonActivityGoodsBindings,
                                               List<CommonActivityChannelBinding> commonActivityChannelBindings) {
        if (commonActivityManage == null) {
            throw new ServiceException("commonActivityManage is null update fail");
        }
        if (!commonActivityManageDao.update(commonActivityManage)) {
            throw new ServiceException("commonActivityManage update fail");
        }
        if (!CollectionUtils.isEmpty(commonActivityGoodsBindings)) {
            commonActivityGoodsBindingDao.deleteByCommonRelationId(commonActivityManage.getId());
            if (commonActivityGoodsBindingDao.creates(commonActivityGoodsBindings) < 1) {
                throw new ServiceException("commonActivityGoodsBindings update fail");
            }
        }
        if (!CollectionUtils.isEmpty(commonActivityChannelBindings)) {
            commonActivityChannelBindingDao.deleteByCommonRelationId(commonActivityManage.getId());
            if (commonActivityChannelBindingDao.creates(commonActivityChannelBindings) < 1) {
                throw new ServiceException("commonActivityChannelBindings update fail");
            }
        }
        return true;
    }
}

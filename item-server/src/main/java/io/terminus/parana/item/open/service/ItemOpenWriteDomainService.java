package io.terminus.parana.item.open.service;

import io.terminus.parana.item.common.base.IdVersionPairBO;
import io.terminus.parana.item.common.extension.AbstractNormalExtensionDeal;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.item.cache.CacheItemById;
import io.terminus.parana.item.item.cache.CacheSkuById;
import io.terminus.parana.item.item.cache.CacheSkuIdByItemId;
import io.terminus.parana.item.item.extension.ItemUpdateStatusExtension;
import io.terminus.parana.item.open.bo.OpenUpdateItemsStatusBO;
import io.terminus.parana.item.open.manage.ItemOpenManager;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ItemOpenWriteDomainService extends AbstractNormalExtensionDeal {
    private final ItemOpenManager itemOpenManager;
    private final CacheItemById cacheItemById;
    private final CacheSkuIdByItemId cacheSkuIdByItemId;
    private final CacheSkuById cacheSkuById;

    @Autowired(required = false)
    private ItemUpdateStatusExtension itemUpdateStatusExtension;

    public Boolean updateItemStatus(OpenUpdateItemsStatusBO bo) {
        //判断是否为上架，如果是上架，校验是否设置售价
        if (itemUpdateStatusExtension != null) {
            List<IdVersionPairBO> ivp = AssembleDataUtils.set2list(bo.getIdSet(), it -> new IdVersionPairBO(it, null));
            invokeExtension(itemUpdateStatusExtension::checkItem, ivp, bo.getStatus());
        }
        //更新状态
        itemOpenManager.updateItemStatus(bo);
        //移除缓存
        cacheItemById.remove(bo.getIdSet());
        List<List<Long>> result = cacheSkuIdByItemId.get(bo.getIdSet(), bo.getTenantId()).getDirectResult();
        Set<Long> skuIdSet = AssembleDataUtils.expandToSet(result);
        cacheSkuById.remove(skuIdSet);
        return Boolean.TRUE;
    }
}

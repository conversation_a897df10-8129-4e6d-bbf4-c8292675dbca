package io.terminus.parana.item.common.schedule.scan.process;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-29
 */
public interface RangedScanner<T> extends ThreadProtocol, Runnable {

    /**
     * 按照区间处理数据
     *
     * @param start     起始id
     * @param end       结束id
     * @param container 数据装载容器（降低内存对象开销）
     * @return 处理后的结果集合
     */
    RangeScanResultContainer<T> process(long start, long end, RangeScanResultContainer<T> container);

    /**
     * 注册交换区数据推送能力
     *
     * @param pusher 推送能力
     */
    void registerBridgePusher(BridgePusher<T> pusher);

    /**
     * 注册发号器能力
     *
     * @param impulse 发号器能力
     */
    void registerImpulse(Impulse impulse);
}

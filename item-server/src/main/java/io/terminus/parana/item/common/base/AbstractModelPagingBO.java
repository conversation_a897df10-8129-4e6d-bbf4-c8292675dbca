package io.terminus.parana.item.common.base;

import com.google.common.base.MoreObjects;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.parana.item.common.enums.SortType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-14
 */
@Data
public abstract class AbstractModelPagingBO implements Serializable, BusinessObjectMappable {
    private static final long serialVersionUID = 6115058739758126795L;

    @ApiModelProperty("行数约束")
    private Integer limit;

    @ApiModelProperty("记录行偏移")
    private Integer offset;

    @ApiModelProperty("排序字段")
    private String orderBy = "id";

    @ApiModelProperty("排序类型")
    private SortType sortType = SortType.DESC;

    private Date startAt;

    private Date endAt;

    public Integer getLimit() {
        return MoreObjects.firstNonNull(limit, 20);
    }

    public Integer getOffset() {
        return MoreObjects.firstNonNull(offset, 0);
    }

    public String getSortType() {
        return sortType == null ? null : sortType.name();
    }
}

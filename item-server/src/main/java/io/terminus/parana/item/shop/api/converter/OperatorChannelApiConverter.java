package io.terminus.parana.item.shop.api.converter;

import io.terminus.parana.item.shop.api.bean.request.OperatorChannelAddRequest;
import io.terminus.parana.item.shop.api.bean.request.OperatorChannelUpdateRequest;
import io.terminus.parana.item.shop.api.bean.response.OperatorChannelInfo;
import io.terminus.parana.item.shop.model.OperatorChannel;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface OperatorChannelApiConverter {

    OperatorChannel request2domain(OperatorChannelAddRequest operatorChannelAddRequest);

    OperatorChannel request2domain(OperatorChannelUpdateRequest operatorChannelUpdateRequest);

    OperatorChannelInfo domain2info(OperatorChannel operatorChannel);

}

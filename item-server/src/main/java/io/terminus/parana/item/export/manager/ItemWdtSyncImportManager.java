package io.terminus.parana.item.export.manager;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.terminus.common.model.Paging;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.item.service.ItemWriteDomainService;
import io.terminus.parana.item.item.service.SkuReadDomainService;
import io.terminus.parana.item.partnership.service.VendorPartnershipReadDomainService;
import io.terminus.parana.item.plugin.third.api.misc.api.ExcelReportWriteApi;
import io.terminus.parana.item.search.docobject.AreaItemDO;
import io.terminus.parana.item.search.facade.ItemSearchFacade;
import io.terminus.parana.item.search.request.AreaItemSearchRequest;
import io.terminus.parana.item.third.param.ThirdUploadReportCreateRequest;
import io.terminus.parana.item.third.param.ThirdUploadReportDetailRequst;
import io.terminus.parana.item.wdt.model.WdtItemEventLog;
import io.terminus.parana.item.wdt.service.WdtItemEventLogWriteDomainService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ItemWdtSyncImportManager {

    @Autowired
    private ExcelReportWriteApi excelReportWriteApi;

    @Autowired
    private VendorPartnershipReadDomainService vendorPartnershipReadDomainService;

    @Autowired
    private ItemSearchFacade itemSearchFacade;
    @Autowired
    private SkuReadDomainService skuReadDomainService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private WdtItemEventLogWriteDomainService wdtItemEventLogWriteDomainService;

    @SneakyThrows
    public void execute() {
        Long startTime = null;
        List<ThirdUploadReportCreateRequest> result = null;

        log.info("[ITEM JOB:旺店通商品处理]=====启动初始化开始=====");
        startTime = System.currentTimeMillis();
        //删除超过一周的数据
        excelReportWriteApi.deleteWeekAgo("item-upload");
        int pageNo = 1;
        int pageSize = 200;
        ThirdUploadReportCreateRequest uploadRequest = new ThirdUploadReportCreateRequest();
        uploadRequest.setReportType("item_wdt_sync");
        uploadRequest.setUploadStauts(0);
        uploadRequest.setCenter("item-upload");
        uploadRequest.setPageNo(pageNo);
        uploadRequest.setPageSize(pageSize);
        result = excelReportWriteApi.pageRead(uploadRequest);
        log.info("ItemWdtSyncImportManager excelReportWriteApi result::{}", result.size());
        if (CollectionUtils.isNotEmpty(result)) {
            for (ThirdUploadReportCreateRequest reportInfoResponse : result) {
                try {
                    // 更新导入记录状态
                    updateReport(reportInfoResponse.getId(), 1,0); // 处理中


                    Map<String, Object> extendMap = JSON.parseObject(reportInfoResponse.getExtendJson(), Map.class);
                    Long vendorId = MapUtil.getLong(extendMap, "vendorId");
                    //查询供应运营关系
                    List<Long> vendorShips = vendorPartnershipReadDomainService.queryOperatorByVendor(vendorId);
                    //限制商品显示
                    List<String> relationshipTypes = Lists.newArrayList();
                    for (Long vendorShip : vendorShips) {
                        relationshipTypes.add(vendorShip + "-" + "0");
                    }
                    relationshipTypes.add("1-1");
                    AreaItemSearchRequest request = new AreaItemSearchRequest();
                    request.setStatus("1_-1");
                    request.setPageSize(100);
                    request.setPageNo(1);
                    Set<Long>  itemIds = new HashSet<>();
                    int i = 1;
                    while(true){
                        request.setRelationshipType(String.join("_", relationshipTypes));
                        request.setTenantId(RequestContext.getTenantId());
                        request.setShopId(vendorId.toString());
                        if (StringUtils.isNotBlank(request.getStatus())) {
                            request.setStatus(request.getStatus());
                        } else {
                            request.setStatus("-1_1_-2_-5");
                        }
                        Paging<AreaItemDO> paging =  Assert.take(itemSearchFacade.search(request));
                        if(paging == null || paging.getData() == null || paging.getData().size()<=0){
                            break;
                        }
                        List<AreaItemDO> areaItemDOList =  paging.getData();
                        log.info("ItemWdtSyncImportManager areaItemDOList {}",areaItemDOList);
                        // 查询品牌通商品SKU信息
                        Set<Long> skuIdSet = areaItemDOList.stream().map(AreaItemDO::getItemId).collect(Collectors.toSet());
                        List<Sku> skuList = skuReadDomainService.findByItemIdSet(skuIdSet, vendorId, RequestContext.getTenantId(), null, null);
                        log.info("ItemWdtSyncImportManager skuList {}",skuList);
                        Map<Long, List<Sku>> skuGroupBy = skuList.stream().collect(Collectors.groupingBy(Sku::getItemId));
                        Set<Long> itemGroupby =  skuGroupBy.keySet();
                        for(Long itemId:itemGroupby){
                            if(checkCusCode(skuGroupBy.get(itemId))){
                                itemIds.add(itemId);
                            }
                        }
                        request.setPageNo(++i);
                    }

                    log.info("ItemWdtSyncImportManager areaItemDOList {}",itemIds);


                    List<Long> itemIdList =  new ArrayList<>(itemIds);
                    int listSize = itemIdList.size();
                    int toIndex = 10;
                    for(int j = 0; j < listSize; j += 10) {

                        if (j + 10 > listSize) {
                            toIndex = listSize - j;
                        }
                        List<Long> newList = itemIdList.subList(j, j + toIndex);
                        List<WdtItemEventLog> eventLogs = new ArrayList<>();
                        for(Long itemId:newList){
                            WdtItemEventLog eventLog = new WdtItemEventLog();
                            eventLog.setId(idGenerator.nextValue(WdtItemEventLog.class, eventLog.getVendorId()));
                            eventLog.setTenantId(request.getTenantId());
                            eventLog.setTimestamp(new Date().getTime());
                            eventLog.setItemId(itemId);
                            eventLog.setVendorId(vendorId);
                            eventLog.setStatus("0");
                            eventLogs.add(eventLog);
                        }
                        wdtItemEventLogWriteDomainService.creates(eventLogs);
                    }

                    updateReport(reportInfoResponse.getId(), 2,itemIdList.size()); // 处理成功

                } catch (Exception e) {
                    ThirdUploadReportDetailRequst thirdUploadReportDetailRequst = new ThirdUploadReportDetailRequst();
                    thirdUploadReportDetailRequst.setCol(0);
                    thirdUploadReportDetailRequst.setRow(0);
                    thirdUploadReportDetailRequst.setUploadReportId(reportInfoResponse.getId());
                    thirdUploadReportDetailRequst.setReason("导入失败系统错误");
                    thirdUploadReportDetailRequst.setSuggest("导入失败系统错误");
                    thirdUploadReportDetailRequst.setCreateAt(new Date());
                    thirdUploadReportDetailRequst.setUpdatedAt(new Date());
                    excelReportWriteApi.detailCreate(thirdUploadReportDetailRequst);
                    updateReport(reportInfoResponse.getId(),-1,0);
                    log.error("ItemWdtSyncImportManager error:{}", Throwables.getStackTraceAsString(e));
                }
            }

        }

        log.info("[ITEM JOB:旺店通商品处理]=====处理完成，文件个数{}===== 耗时：{}ms", result.size(), System.currentTimeMillis() - startTime);
    }


    private Boolean checkCusCode(List<Sku> skuList1){
        Boolean flag = Boolean.TRUE;
        for(Sku sku:skuList1){
            if(StringUtils.isEmpty(sku.getCustSkuCode())){
                flag = Boolean.FALSE;
                break;
            }
        }
        return flag;
    }

    /**
     * 更新导入记录状态
     *
     * @param id
     * @param status
     */
    private void updateReport(Long id, int status,int dataNum) {
        // 更新导入记录状态
        ThirdUploadReportCreateRequest uploadReportUpdateRequest = new ThirdUploadReportCreateRequest();
        uploadReportUpdateRequest.setUploadStauts(status);
        uploadReportUpdateRequest.setId(id);
        uploadReportUpdateRequest.setDataNum(dataNum);
        uploadReportUpdateRequest.setUpdatedAt(new Date());
        excelReportWriteApi.update(uploadReportUpdateRequest);
    }
}

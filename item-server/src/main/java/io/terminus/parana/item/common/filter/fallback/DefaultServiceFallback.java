package io.terminus.parana.item.common.filter.fallback;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.AsyncRpcResult;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-07-27
 */
@Slf4j
public class DefaultServiceFallback implements ServiceFallback {
    @Override
    public Result handle(Invoker<?> invoker, Invocation invocation, BlockException ex) {
//        if (ex == null) {
//            return new RpcResult(Response.fail("Wrong state for MicroService annotation"));
//        }

//        // Execute fallback for degrading if configured.
//        if (ex instanceof DegradeException) {
//            Method method = prepareFallbackMethod(pjp, annotation.fallback());
//            if (method != null) {
//                try {
//                    return method.invoke(pjp.getTarget(), args);
//                } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
//                    log.error("fail to execute fallback methodName, cause: {}", Throwables.getStackTraceAsString(e));
//                    return new RpcResult(Response.fail("降级方法执行错误"));
//                }
//            }
//        }
//
//        // Execute block handler if configured.
//        if (!StringUtils.isEmpty(annotation.blockHandler())) {
//            log.warn("限流触发");
//        }

        return AsyncRpcResult.newDefaultAsyncResult(Response.fail("本次请求已被流控"), invocation);
    }
}

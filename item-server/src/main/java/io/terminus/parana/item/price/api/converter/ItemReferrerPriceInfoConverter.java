package io.terminus.parana.item.price.api.converter;

import io.terminus.parana.item.price.api.bean.response.ItemReferrerPriceInfo;
import io.terminus.parana.item.price.model.ItemReferrerPrice;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface ItemReferrerPriceInfoConverter {

    ItemReferrerPriceInfo domain2info(ItemReferrerPrice domain);


    List<ItemReferrerPriceInfo> domain2infoList(List<ItemReferrerPrice> domain);
}

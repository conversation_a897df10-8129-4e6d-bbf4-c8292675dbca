package io.terminus.parana.item.brand.api.service;

import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.brand.api.bean.request.GetOuterBrandListRequest;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandFindByIdRequest;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandFuzzyRequest;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandPagingRequest;
import io.terminus.parana.item.brand.api.bean.response.OuterBrandInfo;
import io.terminus.parana.item.brand.api.converter.OuterBrandConverter;
import io.terminus.parana.item.brand.model.OuterBrand;
import io.terminus.parana.item.brand.repository.OuterBrandDAO;
import io.terminus.parana.item.category.util.Constant;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class OuterBrandReadService {

    @Autowired
    private OuterBrandDAO outerBrandDAO;

    @Autowired
    private OuterBrandConverter outerBrandConverter;

    public List<OuterBrandInfo> fuzzyByNameQuery(OuterBrandFuzzyRequest outerBrandFuzzyRequest) {
        outerBrandFuzzyRequest.setOperatorId(Constant.ZQ_DEFAULT_OPERATOR_ID);
        List<OuterBrand> outerBrandList = outerBrandDAO.fuzzyByNameQuery(outerBrandFuzzyRequest.getBrandName(),
                outerBrandFuzzyRequest.getTenantId(),
                outerBrandFuzzyRequest.getOperatorId(),
                outerBrandFuzzyRequest.getStatus());
        List<OuterBrandInfo> outerBrandInfoList = new ArrayList<>();
        for (int i = 0; i < outerBrandList.size(); i++) {
            OuterBrand outerBrand = outerBrandList.get(i);
            OuterBrandInfo outerBrandInfo = outerBrandConverter.toInfo(outerBrand);
            outerBrandInfoList.add(outerBrandInfo);
        }
        return outerBrandInfoList;
    }

    /* 根据名称模糊搜索 */
    public Paging<OuterBrand> pagingQuery(OuterBrandPagingRequest outerBrandPagingRequest, PageInfo pageInfo) {

        Map<String, Object> map = new HashedMap();
        if (StringUtils.isNotBlank(outerBrandPagingRequest.getBrandName())) {
            map.put("brandName", outerBrandPagingRequest.getBrandName());
        }
        if(StringUtils.isNotBlank(outerBrandPagingRequest.getOutId())){
            map.put("outId",outerBrandPagingRequest.getOutId());
        }
        if(StringUtils.isNotBlank(outerBrandPagingRequest.getStatus())){
            map.put("status",outerBrandPagingRequest.getStatus());
        }
        map.put("tenantId", outerBrandPagingRequest.getTenantId());
        map.put("operatorId", outerBrandPagingRequest.getOperatorId());
        Paging<OuterBrand> pagingRes = outerBrandDAO.paging(pageInfo.getOffset(), pageInfo.getLimit(), map);

        return pagingRes;
    }

    public List<OuterBrand> getListByIds(List<Long> ids){

        List<OuterBrand> resultData = outerBrandDAO.findByIds(ids);
        return resultData;
    }

    public OuterBrandInfo findById(OuterBrandFindByIdRequest outerBrandFindByIdRequest) {
        OuterBrand outerBrand = outerBrandDAO.findById(outerBrandFindByIdRequest.getId(), outerBrandFindByIdRequest.getTenantId(), outerBrandFindByIdRequest.getOperatorId());
        if (outerBrand == null || outerBrand.getId() == null) {
            throw new ServiceException("'ID'.CANNOT.FIND");
        }
        return outerBrandConverter.toInfo(outerBrand);
    }

    public List<OuterBrandInfo> getList(Integer tenantId, Long operatorId, GetOuterBrandListRequest request){
        List<OuterBrand> res = outerBrandDAO.getList(tenantId,operatorId,request);
        List<OuterBrandInfo> result = new ArrayList<>();
        if (res!=null&&res.size()>0) {
            for (OuterBrand oc : res) {
                OuterBrandInfo on = new OuterBrandInfo();
                on.setId(oc.getId());
                on.setOutId(oc.getOutId());
                on.setBrandName(oc.getBrandName());
                on.setBrandCode(oc.getBrandCode());
                result.add(on);
            }
        }
        return result;
    }

    public List<OuterBrandInfo> findByName(List<OuterBrandFuzzyRequest> requests) {
        List<OuterBrand> baseData = outerBrandDAO.findByOutName(requests);
        List<OuterBrandInfo> result = new ArrayList<>();
        if (baseData!=null&&baseData.size()>0) {
            for (OuterBrand oc : baseData) {
                OuterBrandInfo on = new OuterBrandInfo();
                on.setId(oc.getId());
                on.setOutId(oc.getOutId());
                on.setBrandName(oc.getBrandName());
                on.setBrandCode(oc.getBrandCode());
                result.add(on);
            }
        }
        return result;
    }

    public List<OuterBrand> listByMap(Map<String, Object> map) {
        return outerBrandDAO.list(map);
    }
}

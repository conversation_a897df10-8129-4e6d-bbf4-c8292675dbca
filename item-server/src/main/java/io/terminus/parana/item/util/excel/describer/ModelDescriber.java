package io.terminus.parana.item.util.excel.describer;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.util.excel.annotation.ExcelCollectionField;
import io.terminus.parana.item.util.excel.annotation.ExcelModel;
import io.terminus.parana.item.util.excel.annotation.ExcelSingleField;
import io.terminus.parana.item.util.excel.consts.AnnotationAgreement;
import io.terminus.parana.item.web.excel.ValueParsePackage;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Nullable;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-03-03
 */
@Slf4j
public class ModelDescriber<T> implements Describer {

    private final Class<T> model;

    @Getter
    private int skipRow;
    @Getter
    private int titleRow;

    @Getter
    private Map<String, SingleFiledDescriber> columnMapOfName;
    @Getter
    private Map<Integer, SingleFiledDescriber> columnMapOfPosition;

    @Nullable
    private CollectionFiledDescriber collectionFiledDescriber;

    private final List<Describer> describerList = new LinkedList<>();

    public ModelDescriber(Class<T> model) {
        this.model = model;
        columnMapOfName = new LinkedHashMap<>();
        columnMapOfPosition = new LinkedHashMap<>();
        loadModel();
    }

    @Override
    public void reset() {
        describerList.forEach(Describer::reset);
    }

    public void fillValue(T instance, String title, int position, ValueParsePackage valueParsePackage) throws Exception {
        SingleFiledDescriber filedDescriber;

        if (!CollectionUtils.isEmpty(columnMapOfPosition) && columnMapOfPosition.containsKey(position)) {
            filedDescriber = columnMapOfPosition.get(position);
        } else if (!CollectionUtils.isEmpty(columnMapOfName) && columnMapOfName.containsKey(title)) {
            filedDescriber = columnMapOfName.get(title);
        } else if (collectionFiledDescriber != null && collectionFiledDescriber.getColumnStart() <= position
                && collectionFiledDescriber.getColumnEnd() >= position) {
            if (!valueParsePackage.isEmpty()) {
                collectionFiledDescriber.putElement(title, valueParsePackage, instance);
            }

            return;
        } else {
            log.warn("no valid column definition found to for title: {}, position: {}, value: {}", title, position,
                    valueParsePackage);
            return;
        }

        if (!valueParsePackage.isEmpty()) {
            filedDescriber.setValue(instance, valueParsePackage);
            return;

        }

        if (filedDescriber.isRequired()) {
            log.error("no value found for title: {}, but necessary as definition", title);
            throw new ServiceException("必填字段" + title + "缺失");
        }
    }

    private void loadModel() {
        ExcelModel modelAnnotation = model.getAnnotation(ExcelModel.class);

        if (modelAnnotation == null) {
            log.error("excel process model should have ExcelModel annotation.");
            throw new ServiceException("excel.model.load.fail");
        }

        this.titleRow = modelAnnotation.titleRow();
        this.skipRow = Math.max(modelAnnotation.titleRow(), modelAnnotation.skipRow());
        loadFields(model);
    }

    private void loadFields(Class model) {
        Field[] fields = model.getDeclaredFields();

        for (Field field : fields) {
            if (Modifier.isStatic(field.getModifiers())) {
                continue;
            }

            ExcelSingleField singleField = field.getAnnotation(ExcelSingleField.class);

            if (singleField != null) {
                addSingleField(field, singleField);
            } else {
                ExcelCollectionField collectionField = field.getAnnotation(ExcelCollectionField.class);

                if (collectionField == null) {
                    log.error("model field must have ExcelSingleField or ExcelCollectionField annotation");
                    throw new ServiceException("excel.model.load.fail");
                }

                addCollectionField(field, collectionField);
            }
        }
    }

    private void addSingleField(Field field, ExcelSingleField singleField) {
        if (singleField.columnPosition() == AnnotationAgreement.IGNORED_COLUMN_POSITION &&
                StringUtils.isEmpty(singleField.columnName())) {
            log.error("excel model filed must set columnName or columnPosition");
            throw new ServiceException("excel.model.load.fail");
        }

        if (singleField.columnPosition() != AnnotationAgreement.IGNORED_COLUMN_POSITION) {
            int position = singleField.columnPosition();

            if (columnMapOfPosition.containsKey(position)) {
                log.error("model define more than one column position: {}", position);
                throw new ServiceException("excel.model.load.fail");
            }

            SingleFiledDescriber describer = new SingleFiledDescriber(model, field, singleField.required());
            columnMapOfPosition.put(position, describer);
            describerList.add(describer);
        } else {
            String columnName = singleField.columnName();

            if (columnMapOfName.containsKey(columnName)) {
                log.error("model defined more than one columnName: {}", columnName);
                throw new ServiceException("excel.model.load.fail");
            }

            SingleFiledDescriber describer = new SingleFiledDescriber(model, field, singleField.required());
            columnMapOfName.put(columnName, describer);
            describerList.add(describer);
        }
    }

    private void addCollectionField(Field field, ExcelCollectionField collectionField) {
        if (field.getType() != Map.class) {
            log.error("collection only support type of Map<String, ?> kind by find: {}", field.getType());
            throw new ServiceException("excel.model.load.fail");
        }

        if (collectionFiledDescriber != null) {
            log.error("only support one collection file at most.");
            throw new ServiceException("excel.model.load.fail");
        }

        collectionFiledDescriber = new CollectionFiledDescriber(model, field, collectionField.valueType(),
                collectionField.columnStart(), collectionField.columnEnd());
        describerList.add(collectionFiledDescriber);
    }
}

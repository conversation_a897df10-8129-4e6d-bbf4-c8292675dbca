package io.terminus.parana.item.common.schedule.scan.impl;


import com.google.common.base.Throwables;
import com.google.common.collect.Sets;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.schedule.scan.ScanTaskProgress;
import io.terminus.parana.item.common.schedule.scan.ScheduleTask;
import io.terminus.parana.item.common.schedule.scan.process.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-29
 */
@Slf4j
@Data
public class DefaultScheduleTask<P, T> implements ScheduleTask<P> {

    private static final Integer PROGRESS_RESULT_STEP = 50000;

    private Set<Long> pushIdentitySet;
    private Set<Long> pullIdentitySet;
    private final boolean tolerateError = false;
    private Boolean done = false;
    private Boolean error = false;

    /**
     * 数据交换桥.<br>
     * <p>
     * 在推拉任务模型中，数据交换桥是两者的数据流转媒介。
     * </p>
     */
    private final StreamBridge<T> streamBridge;

    /**
     * 任务运行的上下文，用于传递完整生命周期.<br>
     * <p>
     * 默认情况下，会携带用于创建推、拉模型的必要内容，响应{@link TaskCallback}中的各个情况
     * 的环境数据。谨慎构建上下文，避免数据或者方法的重复使用有助于简化代码。任务一旦创建，原则
     * 上上下文中的内容不允许再修改，否则可能导致数据不一致。
     * </p>
     */
    private final P context;

    /**
     * 发号器.<br>
     * <p>
     * 发号器用于控制任务的数据拉过程，用于统筹各个拉分片。各个任务分片将一直处于工作状态，直到
     * 发号器中无任务可执行。包括提前终止任务、任务处理进度都通过发号器控制实现。拉任务在每次向
     * 发号器获取任务时，必须提供处理成功的记录数，以便发号器可以统计已处理任务数。
     * </p>
     *
     * @see Impulse#getNext(int)
     */
    private final Impulse impulse;

    /**
     * 推模型线程数
     */
    private final Integer nPushThread;

    /**
     * 拉模型线程数
     */
    private final Integer nPullThread;

    /**
     * 线程池.<br>
     * 用于执行所有异步化任务的线程池，默认使用固定大小。核心大小为{@link #nPushThread} + {@link #nPullThread}.
     */
    private final ThreadPoolExecutor threadPool;

    /**
     * 推模型的获得方法.<br>
     * 模型采用lazy模式的实例化，降低初始化成本
     *
     * @see #pullExecute
     */
    private final Function<P, ? extends RangedScanner<T>> pushExecute;

    /**
     * 拉模型的获得方法.<br>
     * 模型采用lazy模式的实例化，降低初始化成本
     *
     * @see #pushExecute
     */
    private final Function<P, ? extends Persistent<T>> pullExecute;

    /**
     * 任务回调句柄.<br>
     * 回调句柄支持任务在关键周期的阶段性回调，使得任务的提交方能够响应任务的关键信息状态。
     * <b>默认情况下，调用方应该至少响应错误信息</b>
     *
     * @see TaskBurstCallback#onError(long, WorkKind, ServiceException)
     */
    private final TaskCallback<P> taskCallback;

    DefaultScheduleTask(P context,
                        Impulse impulse,
                        int nPushThread,
                        int nPullThread,
                        Function<P, ? extends RangedScanner<T>> pushExecute,
                        Function<P, ? extends Persistent<T>> pullExecute,
                        TaskCallback<P> taskCallback,
                        int bridgeCapacity) {
        this.context = context;
        this.impulse = impulse;
        this.nPushThread = nPushThread;
        this.nPullThread = nPullThread;
        this.pushExecute = pushExecute;
        this.pullExecute = pullExecute;
        this.taskCallback = taskCallback;

        this.threadPool = (ThreadPoolExecutor) Executors.newFixedThreadPool(nPushThread + nPullThread);
        this.streamBridge = new StreamBridge<>(bridgeCapacity);
    }

    @Override
    public void start() {
        ((AbstractImpulse) impulse).registerProgressCallback(processed -> taskCallback.onProgress(context, processed));
        ((AbstractImpulse) impulse).setReportLength(PROGRESS_RESULT_STEP);

        initializeAndRunBurst(new TaskBurstCallbackResponse());
    }

    private void initializeAndRunBurst(TaskBurstCallbackResponse burstCallback) {
        // 转异步处理
        DelegateStarter delegateStarter = new DelegateStarter(burstCallback);
        threadPool.submit(delegateStarter);
    }

    private class DelegateStarter implements Runnable {

        private final TaskBurstCallbackResponse burstCallback;

        private DelegateStarter(TaskBurstCallbackResponse burstCallback) {
            this.burstCallback = burstCallback;
        }

        @Override
        public void run() {
            long id = System.currentTimeMillis();

            try {
                taskCallback.onStart(context);
                pushIdentitySet = Sets.newHashSetWithExpectedSize(nPushThread);
                pullIdentitySet = Sets.newHashSetWithExpectedSize(nPullThread);

                for (int i = 0; i < nPushThread; i++) {
                    RangedScanner<T> scan = pushExecute.apply(context);
                    scan.registerImpulse(impulse);
                    scan.registerBridgePusher(streamBridge::push);
                    scan.registerCallback(burstCallback);
                    scan.setIdentityId(++id);
                    threadPool.submit(scan);
                    pushIdentitySet.add(scan.getIdentityId());
                }

                for (int i = 0; i < nPullThread; i++) {
                    Persistent<T> persistent = pullExecute.apply(context);
                    persistent.registerBridgePuller(streamBridge::pull);
                    persistent.registerCallback(burstCallback);
                    persistent.setIdentityId(++id);
                    threadPool.submit(persistent);
                    pullIdentitySet.add(persistent.getIdentityId());
                }
            } catch (Exception e) {
                log.error("fail to start task burst, cause: {}", Throwables.getStackTraceAsString(e));
                // 如果发生错误，直接触发停止操作
                stop();
            }
        }
    }

    @Override
    public void stop() {
        impulse.stop();
        streamBridge.setPushDone();
    }

    @Override
    public boolean done() {
        return done;
    }

    @Override
    public ScanTaskProgress getProgress() {
        return new ScanTaskProgress(impulse.getProcessed(), done, error);
    }

    /**
     * 任务回调的分片响应
     */
    private final class TaskBurstCallbackResponse implements TaskBurstCallback {

        @Override
        public void onStart(long identityId, WorkKind workKind) {
            // do nothing.
        }

        @Override
        public void onDone(long identityId, WorkKind workKind) {
            switch (workKind) {
                case PULLER:
                    pullIdentitySet.remove(identityId);
                    break;
                case PUSHER:
                    pushIdentitySet.remove(identityId);
                    if (CollectionUtils.isEmpty(pushIdentitySet)) {
                        streamBridge.setPushDone();
                    }
                    break;
            }

            if (streamBridge.isPushDone() && CollectionUtils.isEmpty(pullIdentitySet)) {
                done = true;

                if (!error) {
                    taskCallback.onDone(context);
                }
            }
        }

        @Override
        public void onError(long identityId, WorkKind workKind, ServiceException exception) {
            if (!tolerateError) {
                error = true;
                stop();
                taskCallback.onError(context, exception);
                log.error("Task stopped for no error tolerate, cause: {}", Throwables.getStackTraceAsString(exception));
            } else {
                log.error("[Task] error occur at: {}, kind: {}, cause: {}", identityId, workKind, exception);
            }
        }
    }
}

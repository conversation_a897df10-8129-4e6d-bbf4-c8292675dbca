package io.terminus.parana.item.shop.common;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.spi.IdAssembleStrategy;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2018-06-24
 */
@Slf4j
public class ShopIdAssembleStrategy implements IdAssembleStrategy {

    private final static Long MAX_SHARD_GENERATE_ID = 99999999L;
    private final static Integer MAX_SHOP_TYPE_CODE = 99;
    private final static Integer MAX_TENANT_ID = 9;

    /**
     * Shop主键生成策略： Shop类型(2位) + tenantId(1位) + 生成ID(8位)
     *
     * @param id        Shard生成的id
     * @param routeArgs 附加路由参数，其顺序为：type,tenantId
     * @return 商品主键id
     */
    @Override
    public Long assemble(Long id, Long... routeArgs) {
        if (routeArgs == null || routeArgs.length != 2) {
            log.error("route args count not valid, routeArgs");
            throw new ServiceException("invalid.service.id.generate.param");
        }

        log.debug("id generate param is id: {}, service Type: {}, tenantId: {}", id, routeArgs[0], routeArgs[1]);

        if (id > MAX_SHARD_GENERATE_ID) {
            log.warn("Shard generate id bigger than design, id: {}, max: {}", id, MAX_SHARD_GENERATE_ID);
            throw new ServiceException("shard.id.overstep.boundary");
        }

        if (routeArgs[0] > MAX_SHOP_TYPE_CODE) {
            log.error("type code overstep boundary");
            throw new ServiceException("type.code.overstep.boundary");
        }

        // if (routeArgs[1] > MAX_TENANT_ID) {
        //     log.error("tenant id overstep boundary");
        //     throw new ServiceException("tenant.id.overstep.boundary");
        // }

        String generatedId = String.format("%02d%01d%08d", routeArgs[0], routeArgs[1], id);

        return Long.parseLong(generatedId);
    }

    @Override
    public Long assemble(Long id, String... routeArgs) {
        return null;
    }
}

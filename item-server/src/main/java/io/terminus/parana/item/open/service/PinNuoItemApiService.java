package io.terminus.parana.item.open.service;

import cn.hutool.json.JSONUtil;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.parana.item.open.api.bean.response.PinNuoItemCardFlowInfo;
import io.terminus.parana.item.open.api.bean.response.PinNuoItemCardInfo;
import io.terminus.parana.item.open.constant.PinNuoItemUrlConstant;
import io.terminus.parana.item.utils.GeneratePinNuoHttpUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class PinNuoItemApiService {

    @Autowired
    private GeneratePinNuoHttpUtils httpUtils;

    public List<PinNuoItemCardInfo> batchQueryCard(List<String> cardList) {
        List<PinNuoItemCardInfo> infos = Lists.newArrayList();
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("cardList", cardList);
            String json = httpUtils.httpPost(PinNuoItemUrlConstant.BATCH_QUERY_CARD_URL, JSONUtil.toJsonStr(params));
            if (!StringUtils.isEmpty(json)) {
                infos = JSONUtil.toList(json, PinNuoItemCardInfo.class);
            }
        } catch (Exception e) {
            log.error("PinNuoItemApiService batchQueryCard error :: {}", Throwables.getStackTraceAsString(e));
        }
        return infos;
    }

    public List<PinNuoItemCardFlowInfo> consumeRecord(String cardNo) {
        List<PinNuoItemCardFlowInfo> infos = Lists.newArrayList();
        try {
            String json = httpUtils.httpPost(PinNuoItemUrlConstant.CONSUME_RECORD_URL + cardNo, "");
            if (!StringUtils.isEmpty(json)) {
                infos = JSONUtil.toList(json, PinNuoItemCardFlowInfo.class);
            }
        } catch (Exception e) {
            log.error("PinNuoItemApiService batchQueryCard error :: {}", Throwables.getStackTraceAsString(e));
        }
        return infos;
    }

    public Boolean cardActiveBind(Integer type, String tag, String hxCardNo, String password) {
        Boolean result = Boolean.FALSE;
        try {
            // 绑定类型 1-手机号;2-三方用户;3-微信openid
            Map<String, Object> params = Maps.newHashMap();
            if (1 == type) {
                params.put("mobile", tag);
            }else if (2 == type) {
                params.put("uid", tag);
            }else if (3 == type) {
                params.put("openid", tag);
            }
            params.put("hxCardNo", hxCardNo);
            params.put("password", password);
            String json = httpUtils.httpPost(PinNuoItemUrlConstant.CARD_ACTIVE_BIND_URL, JSONUtil.toJsonStr(params));
            if (!StringUtils.isEmpty(json)) {
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.error("PinNuoItemApiService batchQueryCard error :: {}", Throwables.getStackTraceAsString(e));
        }
        return result;
    }

    public String getUrlByUserInfo(String mobile, String userId) {
        String result = null;
        try {
            result =  httpUtils.getUrl(userId,mobile);
        } catch (Exception e) {
            log.error("PinNuoItemApiService getUrlByUserInfo error :: {}", Throwables.getStackTraceAsString(e));
        }
        return result;
    }

}

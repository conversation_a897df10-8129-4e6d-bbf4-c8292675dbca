package io.terminus.parana.item.shop.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.eascs.wallet.advance.api.bean.request.AdvancePaymentCreateRequest;
import com.eascs.wallet.advance.api.facade.AdvancePaymentWriteFacade;
import com.google.common.base.Throwables;
import com.yfcloud.settle.magin.api.bean.request.MarginAccountCreateRequest;
import com.yfcloud.settle.magin.api.facade.MarginAccountWriteFacade;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.parana.exception.RestException;
import io.terminus.parana.item.category.api.bean.response.BackCategoryAncestorsInfo;
import io.terminus.parana.item.category.api.bean.response.BackCategoryInfo;
import io.terminus.parana.item.category.manager.BackCategoryService;
import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.common.cache.SimpleRedisHelper;
import io.terminus.parana.item.common.constants.ShopItemFrozenFlag;
import io.terminus.parana.item.common.extension.ExtensionResult;
import io.terminus.parana.item.common.filter.RequestContext;
import io.terminus.parana.item.common.mq.MessageSendHelper;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.item.enums.YYTItemConstant;
import io.terminus.parana.item.partnership.enums.VendorPartnershipStatus;
import io.terminus.parana.item.partnership.model.VendorPartnership;
import io.terminus.parana.item.partnership.repository.VendorPartnershipDao;
import io.terminus.parana.item.shop.bo.ShopCreateBO;
import io.terminus.parana.item.shop.bo.ShopUpdateBO;
import io.terminus.parana.item.shop.component.ShopAuthCategoryCacheHelper;
import io.terminus.parana.item.shop.enums.BaseShopType;
import io.terminus.parana.item.shop.enums.ShopStatus;
import io.terminus.parana.item.shop.enums.ShopType;
import io.terminus.parana.item.shop.events.*;
import io.terminus.parana.item.shop.extension.ShopDomainWriteExtension;
import io.terminus.parana.item.shop.manager.ShopAuthCategoryManager;
import io.terminus.parana.item.shop.manager.ShopTransactionManager;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.model.ShopAuthCategory;
import io.terminus.parana.item.shop.repository.ShopDao;
import io.terminus.parana.item.shop.util.AliyunOssFactory;
import io.terminus.parana.item.shop.util.QRCodeGenerator;
import io.terminus.parana.item.shop.util.ShopIdGenerateHelper;
import io.terminus.parana.misc.generalConfig.bean.request.GeneralConfigQueryRequest;
import io.terminus.parana.misc.generalConfig.bean.response.GeneralConfigInfoResponse;
import io.terminus.parana.misc.generalConfig.facade.GeneralConfigReadFacade;
import io.terminus.parana.trade.common.util.Kit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;

/**
 * Shop写服务
 *
 * <AUTHOR>
 * @since 2018-08-14
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ShopWriteDomainService extends AbsServiceBase {

    private final SimpleRedisHelper simpleRedisHelper;
    private final ShopAuthCategoryCacheHelper shopAuthCategoryCacheHelper;
    private final ShopDao shopDao;
    private final ShopIdGenerateHelper shopIdGenerateHelper;
    private final MessageSendHelper messageSendHelper;
    private final ShopAuthCategoryManager shopAuthCategoryManager;
    private final ShopTransactionManager shopTransactionManager;
    private final BackCategoryService backCategoryService;
    private final VendorPartnershipDao vendorPartnershipDao;
    private final GeneralConfigReadFacade generalConfigReadFacade;
    private final MarginAccountWriteFacade marginAccountWriteFacade;

    @Autowired(required = false)
    private ShopDomainWriteExtension shopDomainWriteExtension;
    private final AdvancePaymentWriteFacade advancePaymentWriteFacade;

    private final AliyunOssFactory aliyunOssFactory;
    public static final Long SASS_PLATFORM_ID = 1L;

    @Transactional(rollbackFor = Exception.class)
    public Long create(Shop shop) {
        try {
            if (!checkNameAvailable(shop.getTenantId(), shop.getName(), shop.getType())) {
                log.warn("店铺名:{}已被使用", shop.getName());
                if (shop.getType().equals(BaseShopType.MERCHANT.resolve())) {
                    throw new ServiceException("merchant.name.has.been.used");
                }
                throw new ServiceException("shop.name.has.been.used");
            }

            shopIdGenerateHelper.generateId(shop);
            shop.setStatus(ShopStatus.NORMAL.resolve());
            if (shop.getType().equals(ShopType.AREA_OPERATOR.getShopTypeByValue())){
                // 1.创建运营商预付款账号
                AdvancePaymentCreateRequest createRequest = new AdvancePaymentCreateRequest();
                createRequest.setTenantId(shop.getTenantId());
                createRequest.setOperatorId(SASS_PLATFORM_ID);
                createRequest.setAppId(shop.getId());
                createRequest.setLesseeName(shop.getName());
                createRequest.setAccountType(3);
                createRequest.setAccountBalance(0L);
                createRequest.setConsumptionAmountSum(0L);
                createRequest.setUserId(Long.valueOf(shop.getCreatedBy()));
                Response<Boolean> advancePaymentResponse = advancePaymentWriteFacade.create(createRequest);
                if (!advancePaymentResponse.isSuccess()) {
                    throw new RestException("创建运营商预付款账号失败");
                }
            }

            // 前置扩展
            processBeforeCreateExtension(shop);

            ShopCreateBO shopCreateBO = new ShopCreateBO();
            shopCreateBO.setShop(shop);
            // 创建供应商时需要同时创建绑定关系
            if (shop.getType().equals(ShopType.VENDOR.getValue())) {

                shop.setHeight(100L);
                shop.setWidth(100L);

                Map<String, String> shopExtra = shop.getExtra();
                if (CollectionUtils.isEmpty(shopExtra)) {
                    log.error("shop.extra.is.empty:{}", shop.getId());
                    throw new ServiceException("shop.extra.is.empty");
                }

                String operatorId = shopExtra.get(YYTItemConstant.SHOP_OPERATOR_ID.getKey());
                shopExtra.remove(YYTItemConstant.SHOP_OPERATOR_ID.getKey());
                if (StringUtils.isEmpty(operatorId)) {
                    log.error("operator id not found, shop id:{}", shop.getId());
                    throw new ServiceException("operator.id.not.found");
                }
                String warehouseCode = shopExtra.get(YYTItemConstant.SHOP_WAREHOUSE_CODE.getKey());
                if (StringUtils.isEmpty(warehouseCode)) {
                    log.error("warehouse code not found, shop id:{}", shop.getId());
                    throw new ServiceException("warehouse.code.not.found");
                }
                String cooperationMode = shopExtra.get(YYTItemConstant.SHOP_COOPERATION_MODE.getKey());
                if (StringUtils.isEmpty(cooperationMode)) {
                    log.error("cooperation mode not found, shop id:{}", shop.getId());
                    throw new ServiceException("cooperation.mode.not.found");
                }
                String logisticsMode = shopExtra.get(YYTItemConstant.SHOP_LOGISTICS_MODE.getKey());
                if (StringUtils.isEmpty(logisticsMode)) {
                    log.error("logistics mode not found, shop id:{}", shop.getId());
                    throw new ServiceException("logistics.mode.not.found");
                }

                VendorPartnership vendorPartnership = new VendorPartnership();
                vendorPartnership.setVendorId(shop.getId());
                vendorPartnership.setOperatorId(Long.parseLong(operatorId));
                vendorPartnership.setWarehouseCode(warehouseCode);
                vendorPartnership.setCooperationMode(Integer.parseInt(cooperationMode));
                vendorPartnership.setLogisticsMode(Integer.parseInt(logisticsMode));
                vendorPartnership.setStatus(VendorPartnershipStatus.AVAILABLE.getStatus());

                String feeRateStr = shopExtra.get(YYTItemConstant.SHOP_FEE_RATE.getKey());
                if (!StringUtils.isEmpty(feeRateStr)) {
                    vendorPartnership.setFeeRate(Double.parseDouble(feeRateStr));
                }

                vendorPartnership.setSettlementPeriod(shopExtra.get(YYTItemConstant.SHOP_SETTLEMENT_PERIOD.getKey()));
                shopCreateBO.setVendorPartnership(vendorPartnership);

                //创建品牌商后往钱包中台插入一条数据
                MarginAccountCreateRequest request = new  MarginAccountCreateRequest();
                request.setTenantId(shop.getTenantId());//租户id
                request.setVendorId(shop.getId());//供应商id
                request.setVendorName(shop.getShopNames());//供应商全称
                //运营商id
                if (StringUtils.isEmpty(operatorId)) {
                    log.error("operator id not found, shop id:{}", shop.getId());
                    throw new ServiceException("operator.id.not.found");
                }
                request.setOperatorId(Long.valueOf(operatorId));

                request.setCreateBy(shop.getCreatedBy());//创建人
                request.setCreateAt(new Date());//创建时间
                //根据运营商id拉取保证金通用配置
                GeneralConfigQueryRequest generalConfigQueryRequest = new GeneralConfigQueryRequest();
                generalConfigQueryRequest.setOperatorId(request.getOperatorId());
                GeneralConfigInfoResponse response = generalConfigReadFacade.view(generalConfigQueryRequest).getResult();
                request.setMarginBalance(0L);//保证金余额
                request.setMandatoryMarginMoney(response.getMarginDefaultAmount());//强制保证金金额
                request.setMandatoryMarginSuperviseDate(Math.toIntExact(response.getMarginConfig()));//强制保证金监管时间天数
                request.setEffectiveDate(new Date());//生效时间
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE,request.getMandatoryMarginSuperviseDate());
                request.setSuperviseDate(calendar.getTime());
                marginAccountWriteFacade.create(request);
            }else if(shop.getType().equals(ShopType.AREA_OPERATOR.getValue())){
                //区域运营商
                Shop shopInfo=shopCreateBO.getShop();

                Integer tenantId=shop.getTenantId();//租户ID
                Long shopId=shop.getId();//区域ID
                Long pid=shop.getPid();//上级区域ID
                Integer type=shop.getType();//类型-2区域运营
                String updateBy=shop.getUpdatedBy();//修改人
                String code=shop.getCode();//区域编码
                Long industryId=shop.getIndustryId();//行业ID
                String pidStr = ",0," + shop.getId() + ",";
                //兼容旧版暂时注释
                if(code==null){
                    code="";
                }
                /*if(StringUtils.isEmpty(code)){
                    log.error(" code is empty, shop id:{}", shop.getId());
                    throw new ServiceException("code is empty");
                }*/

                log.info("----------createShop---------tenantId:"+tenantId+"-shopId:"+shopId+"-pid:"+pid+"-type:"+type+"-code:"+code);

                //判断同一个城市下只能有一个区域运营
                Map<String,String> extraMap=shop.getExtra();
                //特殊区域运营商不加任何限制
//                log.info("getSpecialOperatorKey:::::::::::::::::"+shop.getSpecialOperatorType());
//                if (null == shop.getSpecialOperatorType()) {
//                    if(extraMap!=null &&!StringUtils.isEmpty(extraMap.get("cityId"))){
//                        String cityId=extraMap.get("cityId");
//                        log.info("----------createShop---------cityId:"+cityId);
//                        Integer existCount=shopDao.getExistCityCount(tenantId,null,pid,cityId,type,industryId);
//                        if(existCount>0){
//                            log.error(" city is exist, shop id:{}", shop.getId());
//                            throw new ServiceException("city.is.exist");
//                        }
//                    }
//                }

//                shop.setPcUrl(extraMap.get("pcUrl"));
//                shop.setWapUrl(extraMap.get("wapUrl"));

                // 生成二维码
                generateQRCode(shop);

                if(pid!=null){
                    Short status=1;//正常
                    Shop pShop= shopDao.findShopById(tenantId,pid,status); //校验pid 是否存在且有效
                    if(pShop==null){
                        log.error(" pid is not exist, shop id:{}", shop.getId());
                        throw new ServiceException("shop.create.fail");
                    }else{
                        //区域编码=父级区域编码+区域编码
                        String pCode=pShop.getCode();
                        if(pCode==null){
                            pCode="";
                        }
                        code=pCode+code;
                        //更新父级子节点数量
                        Boolean success= shopDao.updateAddLeafCount(tenantId,pid,updateBy);
                        if(!success){
                           log.error(" update leafcount fail, shop id:{}", shop.getId());
                           throw new ServiceException("shop.create.fail");
                        }
                        pidStr = pShop.getPidStr() + shopId + ",";
                    }
                }else{
                    pid=0l;
                }
                shopInfo.setPidStr(pidStr);
                shopInfo.setPid(pid);
                shopInfo.setCode(code);
                shopInfo.setLeafCount(0);

                shopCreateBO.setShop(shopInfo);
            }

            Boolean isOk = shopTransactionManager.create(shopCreateBO);

            // 后置扩展
            processAfterCreateExtension(shop);

            messageSendHelper.sendWhenOk(isOk, new ShopCreateEvent(shop.getId(), shop.getType(), shop.getTenantId()));
            return shop.getId();
        } catch (ServiceException ex) {
            throw ex;
        } catch (Exception e) {
            log.error("failed to create shop:{}, cause:{}", shop, Throwables.getStackTraceAsString(e));
            throw new ServiceException("shop.create.fail");
        }
    }

    public Boolean update(Shop shop) {
        try {
            log.info("update shop.id:::::::::::::::::"+shop.getId());
            if (StrUtil.isNotBlank(shop.getName())) {
                if (!checkNameAvailable(shop.getTenantId(), shop.getId(), shop.getName(), shop.getType())) {
                    log.warn("店铺名:{}已被使用", shop.getName());
                    if (shop.getType().equals(BaseShopType.MERCHANT.resolve())) {
                        throw new ServiceException("merchant.name.has.been.used");
                    }
                    throw new ServiceException("shop.name.has.been.used");
                }
            }

//            ShopUpdateBO shopUpdateBO = makeUpShopUpdateBO(shop);
//            processBeforeUpdateExtension(shop);
//            Boolean isOk = shopTransactionManager.update(shopUpdateBO);
//            processAfterUpdateExtension(shop);

            if(shop.getType().equals(ShopType.AREA_OPERATOR.getValue())){
                //区域运营商

                Integer tenantId=shop.getTenantId();//租户ID
                Long shopId=shop.getId();//区域ID
                Long pid=shop.getPid();//上级区域ID
                Integer type=shop.getType();//类型-2区域运营
                String updateBy=shop.getUpdatedBy();
                String code=shop.getCode();//区域编码
                Long industryId=shop.getIndustryId();//行业ID
                String pidStr = shop.getPidStr();
                //兼容旧版暂时注释
                if(code==null){
                    code="";
                }
                /*if(StringUtils.isEmpty(code)){
                    log.error(" code is empty, shop id:{}", shop.getId());
                    throw new ServiceException("code is empty");
                }*/

                //判断同一个城市下只能有一个区域运营
//                Map<String,String> extraMap=shop.getExtra();
                //特殊区域运营商不加任何限制
//                log.info("getSpecialOperatorKey:::::::::::::::::"+shop.getSpecialOperatorType());
//                if (null == shop.getSpecialOperatorType()) {
//                    if(extraMap!=null &&!StringUtils.isEmpty(extraMap.get("cityId"))){
//                        String cityId=extraMap.get("cityId");
//                        log.info("----------createShop---------cityId:"+cityId);
//                        Integer existCount=shopDao.getExistCityCount(tenantId,shopId,pid,cityId,type,industryId);
//                        if(existCount>0){
//                            log.error(" city is exist, shop id:{}", shop.getId());
//                            throw new ServiceException("city.is.exist");
//                        }
//                    }
//                }

//                shop.setPcUrl(extraMap.get("pcUrl"));
//                shop.setWapUrl(extraMap.get("wapUrl"));

                Short status=1;//正常
                Shop oldShop= shopDao.findShopById(tenantId,shopId,status); //获取旧的区域父级ID

                // 判断是否有新的拓展信息，如果有把旧的拓展信息没有的key添加到新的拓展信息中
                Map<String, String> newExtra = Kit.ifDefault(shop.getExtra(),new HashMap<>());
                if (CollUtil.isNotEmpty(newExtra)) {
                    Map<String, String> oldExtra = oldShop.getExtra();
                    if (oldExtra != null) {
                        for (Map.Entry<String, String> entry : oldExtra.entrySet()) {
                            if (!newExtra.containsKey(entry.getKey())) {
                                newExtra.put(entry.getKey(), entry.getValue());
                            }
                        }
                    }
                    shop.setExtraJson(JSONUtil.toJsonStr(newExtra));
                }

                // 生成二维码
                if (!oldShop.getWapUrl().equals(shop.getWapUrl())) {
                    generateQRCode(shop);
                }

                if(oldShop==null){
                    log.error(" shopid is not exist, shop id:{}", shopId);
                    throw new ServiceException("shop.update.fail");
                }
                Long oldPid=oldShop.getPid();
                if(oldPid==null){
                    oldPid=0l;
                }

                if(pid!=null && pid !=0 ) {
                    Shop pShop= shopDao.findShopById(tenantId,pid,status);  //校验pid是否存在且有效
                    if(pShop==null){
                        log.error(" pid is not exist, shop id:{}", shop.getId());
                        throw new ServiceException("shop.update.fail");
                    }else{
                        //区域编码=父级区域编码+区域编码
                        String pCode=pShop.getCode();
                        if(pCode==null){
                            pCode="";
                        }
                        code=pCode+code;

                        if(oldPid!=pid){
                            //更新旧的子节点数量
                            Boolean subSuccess= true;
                            try {
                               shopDao.updateSubLeafCount(tenantId,oldPid,updateBy);
                            } catch (Exception e) {
                                subSuccess= false;
                            }
                            //更新新的子节点数量
                            Boolean success= shopDao.updateAddLeafCount(tenantId,pid,updateBy);
                            if(!success||!subSuccess){
                                log.error(" update new shop  leafcount fail, shop id:{}", shop.getId());
                                throw new ServiceException("shop.update.fail");
                            }
                        }
                        pidStr = pShop.getPidStr() + shop.getId() + ",";
                    }
                }else {
                    pid = 0l;
                    //更新旧的子节点数量
                    if(oldPid!=0) {
                        try {
                            shopDao.updateSubLeafCount(tenantId, oldPid, updateBy);
                        } catch (Exception e) {
                            log.error(" update old shop leafcount fail, shop id:{}", shop.getId());
                            throw new ServiceException("shop.update.fail");
                        }
                    }
                    pidStr = ",0," + shop.getId() + ",";
                }
                shop.setPidStr(pidStr);
                shop.setPid(pid);
                shop.setCode(code);
            }



       /*     // 区运营SD报价配置
            if(shop.getSdPriceType() == 3){
                shop.setSdInventoryType(2);
            }else {
                shop.setSdInventoryType(1);
            }*/

            processBeforeUpdateExtension(shop);
            Boolean isOk = shopDao.update(shop);
            //注释更新供应商合作模式
            //processAfterUpdateExtension(shop);

            messageSendHelper.sendWhenOk(isOk, new ShopUpdateEvent(shop.getId(), shop.getTenantId()));
            return isOk;
        } catch (ServiceException ex) {
            throw ex;
        } catch (Exception e) {
            log.error("failed to update shop:{}, cause:{}", shop, Throwables.getStackTraceAsString(e));
            throw new ServiceException("shop.update.fail");
        }
    }


    public boolean updateZqAttr(Shop shop){
        try {
            return shopDao.updateZqAttr(shop.getId(),shop.getVendorType(),
                    shop.getIsBrand(),shop.getIsDeliery(),shop.getUpdatedBy(),shop.getTenantId());
        } catch (Exception e) {
            log.error("failed to updateZqAttr shop(id={}), cause:{}", shop.getId(), Throwables.getStackTraceAsString(e));
            throw new ServiceException("shop.update.fail");
        }
    }

    public ShopUpdateBO makeUpShopUpdateBO(Shop shop) {
        ShopUpdateBO shopUpdateBO = new ShopUpdateBO();
        shopUpdateBO.setShop(shop);
        Map<String, String> extra = shop.getExtra();
        VendorPartnership vendorPartnership = new VendorPartnership();
        if (!CollectionUtils.isEmpty(extra)) {
            vendorPartnership.setSettlementPeriod(extra.get(YYTItemConstant.SHOP_SETTLEMENT_PERIOD.getKey()));
            vendorPartnership.setFeeRate(Double.parseDouble(extra.get(YYTItemConstant.SHOP_FEE_RATE.getKey())));
            vendorPartnership.setOperatorId(Long.parseLong(extra.get(YYTItemConstant.OPERATOR_ID.getKey())));
            vendorPartnership.setLogisticsMode(Integer.parseInt(extra.get(YYTItemConstant.SHOP_LOGISTICS_MODE.getKey())));
            vendorPartnership.setVendorId(shop.getId());
            vendorPartnership.setCooperationMode(Integer.parseInt(extra.get(YYTItemConstant.SHOP_COOPERATION_MODE.getKey())));
            Long id = vendorPartnershipDao.queryIdByOperatorAndVendor(vendorPartnership.getOperatorId(), vendorPartnership.getVendorId());
            if (id == null) {
                throw new ServiceException("vendor.partner.ship.id.not.found");
            }
            vendorPartnership.setId(id);
        }
        shopUpdateBO.setVendorPartnership(vendorPartnership);
        return shopUpdateBO;
    }

    public Boolean simpleFrozen(Long shopId, String updatedBy, Integer tenantId) {
        try {
            return shopDao.updateStatus(shopId, ShopStatus.FROZEN.resolve(), tenantId, updatedBy);
        } catch (Exception e) {
            log.error("failed to frozen shop(id={}), cause:{}", shopId, Throwables.getStackTraceAsString(e));
            throw new ServiceException("shop.update.fail");
        }
    }

    public Boolean frozen(Long shopId, String updatedBy, Integer tenantId) {
        try {
            Shop shop = shopDao.findById(shopId, tenantId,null);
            if (shop == null) {
                log.error("shop info not found, id:{}", shopId);
                throw new ServiceException("frozen.shop.info.not.found");
            }
            if (!checkAndBuildMask(shopId)) {
                throw new ServiceException("shop.under.executing.items");
            }
            Boolean r = shopDao.updateStatus(shopId, ShopStatus.FROZEN.resolve(), tenantId, updatedBy);
            messageSendHelper.sendWhenOk(r, new ShopFrozenEvent(shopId, shop.getType(), tenantId, updatedBy));
            return r;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("failed to frozen shop(id={}), cause:{}", shopId, Throwables.getStackTraceAsString(e));
            throw new ServiceException("shop.frozen.fail");
        }
    }

    public Boolean simpleUnfrozen(Long shopId, String updatedBy, Integer tenantId) {
        try {
            return shopDao.updateStatus(shopId, ShopStatus.NORMAL.resolve(), tenantId, updatedBy);
        } catch (Exception e) {
            log.error("failed to unfrozen shop(id={}), cause:{}", shopId, Throwables.getStackTraceAsString(e));
            throw new ServiceException("shop.update.fail");
        }
    }

    public Boolean unfrozen(Long shopId, String updatedBy, Integer tenantId) {
        try {
            Shop shop = shopDao.findById(shopId, tenantId,null);
            if (shop == null) {
                log.error("shop info not found, id:{}", shopId);
                throw new ServiceException("unfrozen.shop.info.not.found");
            }

            /*if (shop.getBusinessType() != 2 && checkCityExist(tenantId,shopId,shop)) {
                throw new ServiceException("city.is.exist");
            }*/

            /*if (!checkAndBuildMask(shopId)) {
                throw new ServiceException("shop.under.executing.items");
            }*/

            Boolean r = shopDao.updateStatus(shopId, ShopStatus.NORMAL.resolve(), tenantId, updatedBy);
            messageSendHelper.sendWhenOk(r, new ShopUnfrozenEvent(shopId, shop.getType(), tenantId, updatedBy));
            return r;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("failed to unfrozen shop(id={}), cause:{}", shopId, Throwables.getStackTraceAsString(e));
            throw new ServiceException("shop.unfrozen.fail");
        }
    }

    /**
     *  判断同一个城市下只能有一个区域运营
     * @param shopId
     * @return
     */
    private boolean checkCityExist(Integer tenantId,Long shopId,Shop shop) {
        boolean flag=false;
        Long pid=shop.getPid();
        if(pid==null){
            pid=0l;
        }
        Integer type=shop.getType();
        if(type==null||type==1) return false;
        Map<String,String> extraMap=shop.getExtra();
        Long industryId=shop.getIndustryId();
        if(extraMap!=null &&!StringUtils.isEmpty(extraMap.get("cityId"))){
            String cityId=extraMap.get("cityId");
            log.info("----------unfrozen---------cityId:"+cityId);
            Integer existCount= shopDao.getExistCityCount(tenantId,shopId,pid,cityId,type,industryId);
            if(existCount>0){
                log.error(" city is exist, shop id:{}", shop.getId());
                flag=true;
            }
        }
        return flag;
    }

    /**
     * 查看当前店铺是否正在处于冻结或解冻中
     *
     * @param shopId 店铺id
     * @return
     */
    private boolean checkAndBuildMask(Long shopId) {
        // 低频操作，直接悲观锁好了
        synchronized (this) {
            String key = ShopItemFrozenFlag.SHOP_ITEM_OPERATING_MASK + shopId;

            boolean exist = simpleRedisHelper.exists(key);

            if (!exist) {
                simpleRedisHelper.setValue(key, ShopItemFrozenFlag.SHOP_ITEM_OPERATE_EXECUTING);
                return true;
            }

            String value = simpleRedisHelper.getValue(key);

            if (value.equals(ShopItemFrozenFlag.SHOP_ITEM_OPERATE_EXECUTING)) {
                return false;
            } else {
                simpleRedisHelper.remove(key);
            }

            return true;
        }
    }

    public Boolean close(Long shopId, String updatedBy, Integer tenantId) {
        try {
            Shop shop = shopDao.findById(shopId, tenantId,null);
            if (shop == null) {
                log.error("shop info not found, id:{}", shopId);
                throw new ServiceException("close.shop.info.not.found");
            }
            Boolean r = shopDao.updateStatus(shopId, ShopStatus.CLOSED.resolve(), tenantId, updatedBy);
            messageSendHelper.sendWhenOk(r, new ShopCloseEvent(shopId, shop.getType(), tenantId, updatedBy));
            return r;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("failed to close shop(id={}), cause:{}", shopId, Throwables.getStackTraceAsString(e));
            throw new ServiceException("shop.close.fail");
        }
    }

    /**
     * 校验当前店铺名是否可用
     */
    private boolean checkNameAvailable(Integer tenantId, String name, Integer type) {
        return checkNameAvailable(tenantId, null, name, type);
    }

    private boolean checkNameAvailable(Integer tenantId, Long id, String name, Integer type) {
        if (StringUtils.isEmpty(name)) {
            throw new ServiceException("shop.name.is.empty");
        }

        Shop shop = shopDao.findByNameWithId(id, name.trim(), type, tenantId);
        log.info("update shop oldid:::::::::::"+id);
        if (shop != null && !shop.getId().equals(id)) {
            log.info("update shop repeat id:::::::::::"+shop.getId());
            return true;
        }
        return shop == null;
    }

    public Boolean flushAuthCategory(Long shopId, Set<Long> categoryIdSet) {
        List<ShopAuthCategory> authCategoryList = null;
        if (!CollectionUtils.isEmpty(categoryIdSet)) {
            List<BackCategoryAncestorsInfo> infoList = backCategoryService.findFullByIds(categoryIdSet);
            Map<Long, BackCategoryAncestorsInfo> infoMap = AssembleDataUtils.list2map(infoList, BackCategoryInfo::getId);

            authCategoryList = new LinkedList<>();
            for (Long categoryId : categoryIdSet) {
                ShopAuthCategory authCategory = new ShopAuthCategory();
                authCategory.setShopId(shopId);
                authCategory.setCategoryId(categoryId);
                BackCategoryAncestorsInfo info = infoMap.get(categoryId);
                authCategory.setCategoryIdList(AssembleDataUtils.list2list(info.getCategoryIds(), Objects::toString));
                authCategory.setTenantId(RequestContext.getTenantId());
                authCategory.setUpdatedBy(RequestContext.getUpdatedBy());

                authCategoryList.add(authCategory);
            }
        }

        shopAuthCategoryManager.flushShopAuthCategory(shopId, authCategoryList);
        shopAuthCategoryCacheHelper.releaseCache(shopId);
        return Boolean.TRUE;
    }

    private void processBeforeCreateExtension(Shop shop) {
        if (shopDomainWriteExtension != null) {
            ExtensionResult extensionResult = shopDomainWriteExtension.beforeCreate(shop);
            if (!extensionResult.isSuccess()) {
                throw new ServiceException(extensionResult.getMessage());
            }
        }
    }

    private void processAfterCreateExtension(Shop shop) {
        if (shopDomainWriteExtension != null) {
            shopDomainWriteExtension.afterCreate(shop);
        }
    }

    private void processBeforeUpdateExtension(Shop shop) {
        if (shopDomainWriteExtension != null) {
            shopDomainWriteExtension.beforeUpdate(shop);
        }
    }

    private void processAfterUpdateExtension(Shop shop) {
        if (shopDomainWriteExtension != null) {
            shopDomainWriteExtension.afterUpdate(shop);
        }
    }

    private void generateQRCode(Shop shop) {
        String uuid = UUID.randomUUID().toString().replaceAll("-","");
        shop.setSpecialOperatorKey(uuid);
        String url = shop.getWapUrl();
        if (StrUtil.isEmpty(url)) {
            return;
        }
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "https://" + url;
        }
        if (url.endsWith("/")) {
            url = url + "sign-up?sid=" + uuid;
        } else  {
            url = url + "/sign-up?sid=" + uuid;
        }
        log.info("url::::::::::::::::::::"+url);
        try {
            byte[] content = QRCodeGenerator.getQRCodeImage(url, 600, 600);
            InputStream is = new ByteArrayInputStream(content);
            String filePath = aliyunOssFactory.uploadFile(uuid+".png", is);
            shop.setQrcode(filePath);
            log.info("qrcode:::::::::::::::::::::::"+filePath);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("generate qrcode, error {}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("shop.generate.qrcode.error");
        }
    }

    public Boolean setDefaultLogisticsUpdateVendorExtra(Map<String,Object> mp) {
        return shopDao.setDefaultLogisticsUpdateVendorExtra(mp);
    }

    public void updateById(Shop shop) {
        shopDao.update(shop);
    }

    public Boolean upByOpera(Map<String,Object> map){
        log.info("upOpera:{}",map);
       return shopDao.upByOpera(map);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createVendor(Shop shop) {
        try {
            if (!checkNameAvailable(shop.getTenantId(), shop.getName(), shop.getType())) {
                log.warn("店铺名:{}已被使用", shop.getName());
                if (shop.getType().equals(BaseShopType.MERCHANT.resolve())) {
                    throw new ServiceException("merchant.name.has.been.used");
                }
                throw new ServiceException("shop.name.has.been.used");
            }

            shopIdGenerateHelper.generateId(shop);
            shop.setStatus(ShopStatus.CLOSED.resolve());
            if (shop.getType().equals(ShopType.AREA_OPERATOR.getShopTypeByValue())){
                // 1.创建运营商预付款账号
                AdvancePaymentCreateRequest createRequest = new AdvancePaymentCreateRequest();
                createRequest.setTenantId(shop.getTenantId());
                createRequest.setOperatorId(SASS_PLATFORM_ID);
                createRequest.setAppId(shop.getId());
                createRequest.setLesseeName(shop.getName());
                createRequest.setAccountType(3);
                createRequest.setAccountBalance(0L);
                createRequest.setConsumptionAmountSum(0L);
                createRequest.setUserId(Long.valueOf(shop.getCreatedBy()));
                Response<Boolean> advancePaymentResponse = advancePaymentWriteFacade.create(createRequest);
                if (!advancePaymentResponse.isSuccess()) {
                    throw new RestException("创建运营商预付款账号失败");
                }
            }

            // 前置扩展
            processBeforeCreateExtension(shop);

            ShopCreateBO shopCreateBO = new ShopCreateBO();
            shopCreateBO.setShop(shop);
            // 创建供应商时需要同时创建绑定关系
            if (shop.getType().equals(ShopType.VENDOR.getValue())) {

                shop.setHeight(100L);
                shop.setWidth(100L);

                Map<String, String> shopExtra = shop.getExtra();
                if (CollectionUtils.isEmpty(shopExtra)) {
                    log.error("shop.extra.is.empty:{}", shop.getId());
                    throw new ServiceException("shop.extra.is.empty");
                }

//                String operatorId = shopExtra.get(YYTItemConstant.SHOP_OPERATOR_ID.getKey());
//                shopExtra.remove(YYTItemConstant.SHOP_OPERATOR_ID.getKey());
                String operatorId = String.valueOf(shop.getOperatorId());
                if (StringUtils.isEmpty(operatorId)) {
                    log.error("operator id not found, shop id:{}", shop.getId());
                    throw new ServiceException("operator.id.not.found");
                }
                String warehouseCode = shopExtra.get(YYTItemConstant.SHOP_WAREHOUSE_CODE.getKey());
                if (StringUtils.isEmpty(warehouseCode)) {
                    log.error("warehouse code not found, shop id:{}", shop.getId());
                    throw new ServiceException("warehouse.code.not.found");
                }
                String cooperationMode = shopExtra.get(YYTItemConstant.SHOP_COOPERATION_MODE.getKey());
                if (StringUtils.isEmpty(cooperationMode)) {
                    log.error("cooperation mode not found, shop id:{}", shop.getId());
                    throw new ServiceException("cooperation.mode.not.found");
                }
                String logisticsMode = shopExtra.get(YYTItemConstant.SHOP_LOGISTICS_MODE.getKey());
                if (StringUtils.isEmpty(logisticsMode)) {
                    log.error("logistics mode not found, shop id:{}", shop.getId());
                    throw new ServiceException("logistics.mode.not.found");
                }

                VendorPartnership vendorPartnership = new VendorPartnership();
                vendorPartnership.setVendorId(shop.getId());
                vendorPartnership.setOperatorId(Long.parseLong(operatorId));
                vendorPartnership.setWarehouseCode(warehouseCode);
                vendorPartnership.setCooperationMode(Integer.parseInt(cooperationMode));
                vendorPartnership.setLogisticsMode(Integer.parseInt(logisticsMode));
                vendorPartnership.setStatus(VendorPartnershipStatus.SETTLE_AUDIT.getStatus());

                String feeRateStr = shopExtra.get(YYTItemConstant.SHOP_FEE_RATE.getKey());
                if (!StringUtils.isEmpty(feeRateStr)) {
                    vendorPartnership.setFeeRate(Double.parseDouble(feeRateStr));
                }

                vendorPartnership.setSettlementPeriod(shopExtra.get(YYTItemConstant.SHOP_SETTLEMENT_PERIOD.getKey()));
                shopCreateBO.setVendorPartnership(vendorPartnership);

                //创建品牌商后往钱包中台插入一条数据
                MarginAccountCreateRequest request = new  MarginAccountCreateRequest();
                request.setTenantId(shop.getTenantId());//租户id
                request.setVendorId(shop.getId());//供应商id
                request.setVendorName(shop.getShopNames());//供应商全称
                //运营商id
                if (StringUtils.isEmpty(operatorId)) {
                    log.error("operator id not found, shop id:{}", shop.getId());
                    throw new ServiceException("operator.id.not.found");
                }
                request.setOperatorId(Long.valueOf(operatorId));

                request.setCreateBy(shop.getCreatedBy());//创建人
                request.setCreateAt(new Date());//创建时间
                //根据运营商id拉取保证金通用配置
                GeneralConfigQueryRequest generalConfigQueryRequest = new GeneralConfigQueryRequest();
                generalConfigQueryRequest.setOperatorId(request.getOperatorId());
                GeneralConfigInfoResponse response = generalConfigReadFacade.view(generalConfigQueryRequest).getResult();
                request.setMarginBalance(0L);//保证金余额
                request.setMandatoryMarginMoney(response.getMarginDefaultAmount());//强制保证金金额
                request.setMandatoryMarginSuperviseDate(Math.toIntExact(response.getMarginConfig()));//强制保证金监管时间天数
                request.setEffectiveDate(new Date());//生效时间
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE,request.getMandatoryMarginSuperviseDate());
                request.setSuperviseDate(calendar.getTime());
                marginAccountWriteFacade.create(request);
            }else if(shop.getType().equals(ShopType.AREA_OPERATOR.getValue())){
                //区域运营商
                Shop shopInfo=shopCreateBO.getShop();

                Integer tenantId=shop.getTenantId();//租户ID
                Long shopId=shop.getId();//区域ID
                Long pid=shop.getPid();//上级区域ID
                Integer type=shop.getType();//类型-2区域运营
                String updateBy=shop.getUpdatedBy();//修改人
                String code=shop.getCode();//区域编码
                Long industryId=shop.getIndustryId();//行业ID
                String pidStr = ",0," + shop.getId() + ",";
                //兼容旧版暂时注释
                if(code==null){
                    code="";
                }
                /*if(StringUtils.isEmpty(code)){
                    log.error(" code is empty, shop id:{}", shop.getId());
                    throw new ServiceException("code is empty");
                }*/

                log.info("----------createShop---------tenantId:"+tenantId+"-shopId:"+shopId+"-pid:"+pid+"-type:"+type+"-code:"+code);

                //判断同一个城市下只能有一个区域运营
                Map<String,String> extraMap=shop.getExtra();
                //特殊区域运营商不加任何限制
//                log.info("getSpecialOperatorKey:::::::::::::::::"+shop.getSpecialOperatorType());
//                if (null == shop.getSpecialOperatorType()) {
//                    if(extraMap!=null &&!StringUtils.isEmpty(extraMap.get("cityId"))){
//                        String cityId=extraMap.get("cityId");
//                        log.info("----------createShop---------cityId:"+cityId);
//                        Integer existCount=shopDao.getExistCityCount(tenantId,null,pid,cityId,type,industryId);
//                        if(existCount>0){
//                            log.error(" city is exist, shop id:{}", shop.getId());
//                            throw new ServiceException("city.is.exist");
//                        }
//                    }
//                }

//                shop.setPcUrl(extraMap.get("pcUrl"));
//                shop.setWapUrl(extraMap.get("wapUrl"));

                // 生成二维码
                generateQRCode(shop);

                if(pid!=null){
                    Short status=1;//正常
                    Shop pShop= shopDao.findShopById(tenantId,pid,status); //校验pid 是否存在且有效
                    if(pShop==null){
                        log.error(" pid is not exist, shop id:{}", shop.getId());
                        throw new ServiceException("shop.create.fail");
                    }else{
                        //区域编码=父级区域编码+区域编码
                        String pCode=pShop.getCode();
                        if(pCode==null){
                            pCode="";
                        }
                        code=pCode+code;
                        //更新父级子节点数量
                        Boolean success= shopDao.updateAddLeafCount(tenantId,pid,updateBy);
                        if(!success){
                            log.error(" update leafcount fail, shop id:{}", shop.getId());
                            throw new ServiceException("shop.create.fail");
                        }
                        pidStr = pShop.getPidStr() + shopId + ",";
                    }
                }else{
                    pid=0l;
                }
                shopInfo.setPidStr(pidStr);
                shopInfo.setPid(pid);
                shopInfo.setCode(code);
                shopInfo.setLeafCount(0);

                shopCreateBO.setShop(shopInfo);
            }

            Boolean isOk = shopTransactionManager.create(shopCreateBO);

            // 后置扩展
            processAfterCreateExtension(shop);

            messageSendHelper.sendWhenOk(isOk, new ShopCreateEvent(shop.getId(), shop.getType(), shop.getTenantId()));
            log.info("shopId:{}",shop.getId());
            return shop.getId();
        } catch (ServiceException ex) {
            throw ex;
        } catch (Exception e) {
            log.error("failed to create shop:{}, cause:{}", shop, Throwables.getStackTraceAsString(e));
            throw new ServiceException("shop.create.fail");
        }
    }

    /**
     * 更新SD结算客户维度配置
     *
     * @param shopId 店铺ID
     * @param sdSettlementCustomerDimension SD结算客户维度
     * @param updatedBy 更新人
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSdSettlementCustomerDimension(Long shopId, Integer sdSettlementCustomerDimension, String updatedBy) {
        try {
            // 查找店铺
            Shop shop = shopDao.findById(shopId, null, null);
            if (shop == null) {
                log.error("shop not found, id: {}", shopId);
                throw new ServiceException("shop.not.found");
            }

            // 验证店铺类型是否为运营商
            if (ShopType.AREA_OPERATOR.getValue() != shop.getType()) {
                log.error("shop type is not operator, id: {}, type: {}", shopId, shop.getType());
                throw new ServiceException("sd.settlement.customer.dimension.only.for.operator");
            }

            // 更新extra字段中的SD结算客户维度配置
            Map<String, String> extra = shop.getExtra();
            if (extra == null) {
                extra = new HashMap<>();
            }
            extra.put("sdSettlementCustomerDimension", String.valueOf(sdSettlementCustomerDimension));
            shop.setExtra(extra);
            shop.setExtraJson(JSONUtil.toJsonStr(extra));
            shop.setUpdatedBy(updatedBy);
            shop.setUpdatedAt(new Date());

            // 更新店铺信息
            Boolean result = shopDao.update(shop);
            if (result) {
                log.info("SD结算客户维度配置更新成功, shopId: {}, dimension: {}", shopId, sdSettlementCustomerDimension);
            } else {
                log.error("SD结算客户维度配置更新失败, shopId: {}, dimension: {}", shopId, sdSettlementCustomerDimension);
            }

            return result;
        } catch (ServiceException ex) {
            throw ex;
        } catch (Exception e) {
            log.error("failed to update SD settlement customer dimension, shopId: {}, dimension: {}, cause: {}",
                     shopId, sdSettlementCustomerDimension, Throwables.getStackTraceAsString(e));
            throw new ServiceException("sd.settlement.customer.dimension.update.fail");
        }
    }
}

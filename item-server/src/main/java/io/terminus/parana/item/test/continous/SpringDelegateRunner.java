package io.terminus.parana.item.test.continous;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.kevinsawicki.http.HttpRequest;
import io.terminus.parana.item.test.TestDescription;
import io.terminus.parana.item.test.continous.protocol.InvokeFailedException;
import io.terminus.parana.item.test.continous.protocol.InvokeRequest;
import io.terminus.parana.item.test.continous.protocol.InvokeResult;
import org.junit.runner.Description;
import org.junit.runner.notification.Failure;
import org.junit.runner.notification.RunNotifier;
import org.junit.runners.model.FrameworkMethod;
import org.junit.runners.model.InitializationError;
import org.junit.runners.model.Statement;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-15
 */
public class SpringDelegateRunner extends ModifiedSpringJUnit4ClassRunner {

    private ObjectMapper objectMapper = new ObjectMapper();

    private final Class<?> testClass;

    private final Boolean DEBUG_MODE = DebugMaskUtil.debugMode();

    public SpringDelegateRunner(Class<?> clazz) throws InitializationError {
        super(clazz);
        this.testClass = clazz;

        if (!DebugMaskUtil.debugMode()) {
            super.createContext(clazz);
        }
    }

    @Override
    public Description getDescription() {
        return Description.createTestDescription(testClass, testClass.getName());
    }

    private Description describe(FrameworkMethod method) {
        final TestDescription methodAnnotation = method.getAnnotation(TestDescription.class);
        final TestDescription classAnnotation = testClass.getAnnotation(TestDescription.class);

        StringBuilder sb = new StringBuilder(method.getName());
        if (methodAnnotation != null || classAnnotation != null) {
            sb.append(":");

            if (classAnnotation != null) {
                sb.append("[").append(classAnnotation.value()).append("]");
            }

            if (methodAnnotation != null) {
                sb.append(methodAnnotation.value());
            }
        }

        return Description.createTestDescription(testClass, sb.toString());
    }

    /**
     * 递交给远程执行
     *
     * @param method   执行的方法
     * @param notifier Runner通知
     */
    private void runRemote(FrameworkMethod method, RunNotifier notifier) {
        Description description = describe(method);
        if (isIgnored(method)) {
            notifier.fireTestIgnored(description);
            return;
        }

        InvokeRequest invokeRequest = new InvokeRequest();
        invokeRequest.setTestClass(method.getDeclaringClass());
        invokeRequest.setMethodName(method.getName());

        try {
            notifier.fireTestStarted(description);

            String json = objectMapper.writeValueAsString(invokeRequest);
            String body = HttpRequest.post("http://127.0.0.1:" + DebugMaskUtil.getPort()).send(json).body();

            if (StringUtils.isEmpty(body)) {
                notifier.fireTestFailure(new Failure(description, new RuntimeException("远程执行失败")));
            }

            InvokeResult invokeResult = objectMapper.readValue(body, InvokeResult.class);
            Boolean success = invokeResult.getSuccess();

            if (success) {
                notifier.fireTestFinished(description);
            } else {
                InvokeFailedException exception = invokeResult.getException();

                if (exception.getAssertionError() != null) {
                    notifier.fireTestFailure(new Failure(description, exception.getAssertionError()));
                } else {
                    notifier.fireTestFailure(new Failure(description, invokeResult.getException()));
                }
            }
        } catch (Exception e) {
            notifier.fireTestFailure(new Failure(description, e));
        }
    }

    private void runLocal(FrameworkMethod method, RunNotifier notifier) {
        // 不能复写，否则idea单方法运行时报无test错误
        Description description = describe(method);
        if (isIgnored(method)) {
            notifier.fireTestIgnored(description);
            return;
        }

        try {
            notifier.fireTestStarted(description);

            final Statement statement = methodBlock(method);
            statement.evaluate();
        } catch (Throwable e) {
            notifier.fireTestFailure(new Failure(description, e));
        } finally {
            notifier.fireTestFinished(description);
        }
    }

    @Override
    protected void runChild(FrameworkMethod method, RunNotifier notifier) {
        if (DEBUG_MODE) {
            runRemote(method, notifier);
        } else {
            runLocal(method, notifier);
        }
    }
}

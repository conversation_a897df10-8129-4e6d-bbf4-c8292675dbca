package io.terminus.parana.item.task.model;

import lombok.Data;

import java.util.Date;

@Data
public class TaskExecutionControl {

    private Long id;
    /**
     * 租户
     */
    private Integer tenantId;
    /**
     * 中台名
     */
    private String centerName;

    /**
     * 任务名
     */
    private String taskName;
    /**
     * 执行id
     */
    private String source;
    /**
     * 最后执行开始时间
     */
    private Date lastBeginTime;
    /**
     * 最后执行结束时间
     */
    private Date lastEndTime;
    /**
     * 执行状态 1执行中 0空闲
     */
    private Integer status;
    /**
     * 版本号
     */
    private Integer version;
}

package io.terminus.parana.item.common.cache.plugin;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.cache.CacheOperate;
import io.terminus.parana.item.common.response.MapResultPack;

import java.util.Map;
import java.util.Set;

/**
 * 简单缓存的接口定义。简单缓存指：在缓存过程中，仅需要某个属性即可满足唯一性要求的缓存形式，如id。
 * <p>
 * 当使用此接口实现缓存时，需要自动将缓存未命中的数据从数据库中捞出，并将有效数据放入缓存中。
 * </p>
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-15
 */
public interface SimpleCache<TI, TR> extends CacheOperate<TR> {

    /**
     * 由入参生成唯一缓存key
     *
     * @param input 入参
     * @param args  附加参数
     * @return 唯一缓存key
     */
    String generateKey(TI input, Object... args);

    /**
     * 获取数据
     * <p>
     * 此方法中的input为缓存的唯一键，args仅作为辅助
     * </p>
     *
     * @param input 入参
     * @param args  扩展参数
     * @return 数据对象
     */
    TR get(TI input, Object... args);

    /**
     * 这是数据库提供数据的指定办法
     *
     * @param input 入参
     * @param args  扩展参数
     * @return 数据对象
     */
    TR databaseGet(TI input, Object... args);

    /**
     * 从数据库中获取数据
     *
     * @param inputSet 入参集合
     * @param args     附加参数
     * @return 结果集合
     */
    default Map<TI, TR> databaseGet(Set<TI> inputSet, Object... args) {
        throw new ServiceException("未设置批量获取数据源逻辑");
    }

    /**
     * 批量获取数据结果
     *
     * @param inputSet 入参集合
     * @param args     附加参数
     * @return 结果集合
     */
    MapResultPack<TI, TR> get(Set<TI> inputSet, Object... args);

    /**
     * 移除缓存
     *
     * @param input 入参
     * @param args  附加参数
     */
    void remove(TI input, Object... args);

    /**
     * 移除缓存
     *
     * @param inputSet 入参集合
     * @param args     附加参数
     */
    void remove(Set<TI> inputSet, Object... args);

    /**
     * 当条件成立时，移除缓存
     *
     * @param condition 条件
     * @param input     入参
     * @param args      附加参数
     */
    default void removeOnCondition(boolean condition, TI input, Object... args) {
        if (condition) {
            remove(input, args);
        }
    }

    /**
     * 当条件成立时，移除缓存
     *
     * @param condition 条件
     * @param inputSet  入参集合
     * @param args      附加参数
     */
    default void removeOnCondition(boolean condition, Set<TI> inputSet, Object... args) {
        if (condition) {
            remove(inputSet, args);
        }
    }
}

package io.terminus.parana.item.shop.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierInfoModel implements Serializable{

	/**
	 * id
	 */
	private Long id;
	/**
	 * 申请人用户id
	 */
	private Long userId;
	/**
	 * 申请人手机号
	 */
	private String applyPhone;
	/**
	 * 申请人证件照片正面
	 */
	private String applyCertificatePhotoFront;
	/**
	 * 申请人证件照片反面
	 */
	private String applyCertificatePhotoBack;
	/**
	 * 申请人姓名
	 */
	private String applyName;
	/**
	 * 申请人证件号码
	 */
	private String applyCertificateNumber;
	/**
	 * 申请人证件有效开始时间
	 */
	private java.util.Date applyCertificateValidStartTime;
	/**
	 * 申请人证件有效结束时间
	 */
	private java.util.Date applyCertificateValidEndTime;
	/**
	 * 企业营业执照
	 */
	private String enterpriseBusinessLicense;
    /**
     * 企业logo
     */
    private String enterpriseLogo;
    /**
     * 企业邮箱
     */
    private String enterpriseEmail;
	/**
	 * 统一社会信用代码
	 */
	private String unifiedSocialCreditCode;
	/**
	 * 企业简称
	 */
	private String enterpriseNameAbbreviation;
	/**
	 * 企业全称
	 */
	private String enterpriseName;
	/**
	 * 企业类型
	 */
	private String enterpriseType;
	/**
	 * 营业执照所在地编码
	 */
	private String businessLicenseCode;
	/**
	 * 营业执照详细地址
	 */
	private String businessLicenseAddress;
	/**
	 * 注册资本
	 */
	private String registrationCapital;
	/**
	 * 成立日期
	 */
	private java.util.Date enterpriseRegistrationTime;
	/**
	 * 营业开始时间
	 */
	private java.util.Date operatingStartTime;
	/**
	 * 营业结束时间
	 */
	private java.util.Date operatingEndTime;
	/**
	 * 经营范围
	 */
	private String businessScope;
	/**
	 * 法人证件照正面
	 */
	private String legalPersonCertificatePhotoFront;
	/**
	 * 法人证件照反面
	 */
	private String legalPersonCertificatePhotoBack;
	/**
	 * 法人姓名
	 */
	private String legalPersonName;
	/**
	 * 法人证件类型 1：大陆身份证 2：其他证件
	 */
	private Integer legalPersonCertificateType;
	/**
	 * 法人证件号码
	 */
	private String legalPersonCertificateNumber;
	/**
	 * 法人证件有效开始时间
	 */
	private java.util.Date legalPersonCertificateValidStartTime;
	/**
	 * 法人证件有效结束时间
	 */
	private java.util.Date legalPersonCertificateValidEndTime;
	/**
	 * 企业实际经营地址编码
	 */
	private String enterpriseBusinessAddressCode;
	/**
	 * 企业实际经营地址
	 */
	private String enterpriseBusinessAddress;
	/**
	 * 企业电话
	 */
	private String enterprisePhone;
	/**
	 * 银行账户名
	 */
	private String bankAccountName;
	/**
	 * 银行卡号
	 */
	private String bankCardNumber;
	/**
	 * 开户银行
	 */
	private String bankName;
	/**
	 * 开户支行所在地代码
	 */
	private String bankArea;
	/**
	 * 开户支行名称
	 */
	private String bankBranchName;
	/**
	 * 开户支行联行号
	 */
	private String bankBranceCode;
	/**
	 * 纳税人类型 1：一般纳税人 2：小规模纳税人
	 */
	private Integer taxpayerType;
	/**
	 * 审核状态 0：草稿 1：待审核 2：审核通过 3：审核驳回
	 */
	private Integer auditStatus;
	/**
	 * 审核意见
	 */
	private String auditOpinion;
	/**
	 * 结算周期 1周结 2半月结 3月结
	 */
	private Integer billingCycle;
	/**
	 * 审核有效开始时间
	 */
	private java.util.Date auditStartTime;
	/**
	 * 审核有效结束时间
	 */
	private java.util.Date auditEndTime;
	/**
	 * 租户ID
	 */
	private Long tenantId;
	/**
	 * 创建人员
	 */
	private Long createUserId;
	/**
	 * 修改人员
	 */
	private Long updateUserId;
	/**
	 * 锁定状态
	 */
	private Integer stateLocked;
	/**
	 * 申请时间
	 */
	private java.util.Date appliedTime;
	/**
	 * 创建时间
	 */
	private java.util.Date createTime;
	/**
	 * 修改时间
	 */
	private java.util.Date updateTime;
	/**
	 * 	联系人
	 */
	private String concactName;
	/**
	 * 	联系电话
	 */
	private String concactMobile;
	/**
	 * 	退货地址编码
	 */
	private String concactCode;
	/**
	 * 	退货地址详细地址
	 */
	private String concactAddress;
	/**
	 * 	运营商id
	 */
	private Long operatorId;
	/**
	 * 	入驻状态0:入驻1:修改
	 */
	private Integer settleStatus;

	private String contract;

	private Long operationId;

	private String extraJson;

	@ApiModelProperty(value = "入驻时间起&&申请时间起")
	private java.util.Date settleTimeBefore;
	@ApiModelProperty(value = "入驻时间止&&申请时间止")
	private java.util.Date settleTimeAfter;
	@ApiModelProperty(value = "更新时间起&&审核时间起")
	private java.util.Date updateTimeBefore;
	@ApiModelProperty(value = "更新时间止&&审核时间止")
	private java.util.Date updateTimeAfter;

	@ApiModelProperty(value = "是否外部品牌商 Y 是 N 否")
	private String inType;

	@ApiModelProperty(value = "是否同步SD Y 是 N 否")
	private String syncSd;

	@ApiModelProperty(value = "sd客户流水号")
	private String sdNo;

	@ApiModelProperty(value = "sd客户名称")
	private String sdName;
}

package io.terminus.parana.item.brand.api.facade;

import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.brand.api.bean.request.*;
import io.terminus.parana.item.brand.api.bean.response.OuterBrandInfo;
import io.terminus.parana.item.brand.api.converter.OuterBrandConverter;
import io.terminus.parana.item.brand.api.service.OuterBrandReadService;
import io.terminus.parana.item.brand.model.Brand;
import io.terminus.parana.item.brand.model.OuterBrand;
import io.terminus.parana.item.brand.model.OuterBrandBinding;
import io.terminus.parana.item.brand.repository.BrandDAO;
import io.terminus.parana.item.brand.repository.OuterBrandBindingDAO;
import io.terminus.parana.item.category.util.Constant;
import io.terminus.parana.item.outerbrandbinding.model.OuterBrandBindingModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :2021-01-14 10:25:32
 */

@Slf4j
@Component
public class OuterBrandReadFacadeImpl implements OuterBrandReadFacade {

    @Autowired
    private OuterBrandReadService readService;
    
    @Autowired
    private OuterBrandConverter outerBrandConverter;

    @Autowired
    private OuterBrandBindingDAO outerBrandBindingDAO;
    @Autowired
    private BrandDAO brandDAO;

    @Override
    public Response<List<OuterBrandInfo>> fuzzyByNameQuery(OuterBrandFuzzyRequest outerBrandFuzzyRequest) {

        return Response.ok(readService.fuzzyByNameQuery(outerBrandFuzzyRequest));
    }

    @Override
    public Response<Paging<OuterBrandInfo>> pagingQuery(OuterBrandPagingRequest outerBrandPagingRequest) {
        outerBrandPagingRequest.setOperatorId(Constant.ZQ_DEFAULT_OPERATOR_ID);
        PageInfo pageInfo=new PageInfo(outerBrandPagingRequest.getPageNo(),outerBrandPagingRequest.getPageSize());
        //转换
        Paging<OuterBrand> pageBrand = readService.pagingQuery(outerBrandPagingRequest, pageInfo);
        List<OuterBrandInfo> outerBrandInfoList = new ArrayList<>();

        Set<Long> operatorIds = new HashSet<>();

        for(OuterBrand item : pageBrand.getData()){
            OuterBrandInfo outerBrandInfo = outerBrandConverter.toInfo(item);

            OuterBrandBinding outerBrandBinding = new OuterBrandBinding();
            outerBrandBinding.setOutBrandId(item.getOutId() == null ? null : Long.parseLong(item.getOutId()));
            outerBrandBinding.setTenantId(outerBrandPagingRequest.getTenantId());
            //查询绑定品牌信息
            List<OuterBrandBinding> bindingDaoByOutIds = outerBrandBindingDAO.list(outerBrandBinding);
            if (bindingDaoByOutIds != null &&  bindingDaoByOutIds.size()>0) {
                List<Long> brandIds = bindingDaoByOutIds.stream().map(OuterBrandBinding::getBrandId).collect(Collectors.toList());
                outerBrandInfo.setBrandId(brandIds);
                List<Brand> brandList = brandDAO.findByIds(brandIds);
                List<String> listName = brandList.stream().map(Brand::getName).collect(Collectors.toList());
                outerBrandInfo.setBrandNames(String.join(",",listName));
            }
            outerBrandInfoList.add(outerBrandInfo);
        }
        return Response.ok(new Paging<OuterBrandInfo>(pageBrand.getTotal(),outerBrandInfoList));
    }

    @Override
    public Response<OuterBrandInfo> findById(OuterBrandFindByIdRequest outerBrandFindByIdRequest) {
        outerBrandFindByIdRequest.setOperatorId(Constant.ZQ_DEFAULT_OPERATOR_ID);
        return Response.ok(readService.findById(outerBrandFindByIdRequest));
    }

    @Override
    public Response<List<OuterBrandInfo>> getList(GetOuterBrandListRequest request) {
        Integer tenantId = request.getTenantId();
        Long operatorId = request.getOperatorId();
        return Response.ok(readService.getList(tenantId,operatorId,request));
    }



}

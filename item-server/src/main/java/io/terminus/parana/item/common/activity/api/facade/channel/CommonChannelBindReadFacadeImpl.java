package io.terminus.parana.item.common.activity.api.facade.channel;

import io.terminus.common.model.Response;
import io.terminus.parana.item.channel.model.Channel;
import io.terminus.parana.item.channel.service.ChannelReadDomainService;
import io.terminus.parana.item.common.activity.api.bean.request.ChannelBatchAncestorsRequest;
import io.terminus.parana.item.common.activity.api.bean.request.ChannelQueryRequest;
import io.terminus.parana.item.common.activity.api.bean.response.CommonChannel;
import io.terminus.parana.item.common.activity.api.bean.response.CommonChannelInfo;
import io.terminus.parana.item.common.activity.api.converter.CommonChannelConverter;
import io.terminus.parana.item.common.activity.api.facade.CommonChannelBindReadFacade;
import io.terminus.parana.item.common.activity.model.CommonActivityManage;
import io.terminus.parana.item.common.activity.service.CommonActivityChannelBindingService;
import io.terminus.parana.item.common.activity.service.CommonActivityManageService;
import io.terminus.parana.item.common.converter.GeneralConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 常用活动关联渠道读服务
 *
 * <AUTHOR>
 * @date 2021-07-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonChannelBindReadFacadeImpl implements CommonChannelBindReadFacade {

    private final ChannelReadDomainService channelReadDomainService;
    private final CommonActivityChannelBindingService commonActivityChannelBindingService;
    private final CommonActivityManageService commonActivityManageService;

    private final CommonChannelConverter commonChannelConverter;

    @Override
    public Response<List<CommonChannelInfo>> getAncestors(ChannelBatchAncestorsRequest request) {
        log.info("CommonChannelBindReadFacadeImpl.getAncestors() request：{}", request);
        // 查询渠道信息
        String[] channelIdArray = request.getChannelIds().split(",");
        Set<Long> channelIds = new HashSet<>();
        for (String channelIdStr : channelIdArray) {
            channelIds.add(Long.parseLong(channelIdStr));
        }
        List<Channel> channelList = channelReadDomainService.findByIds(channelIds);
        if (CollectionUtils.isEmpty(channelList)) {
            return Response.ok(Collections.emptyList());
        }
        // 封装数据返回
        List<CommonChannelInfo> resultList = new ArrayList<>(channelList.size());
        for (Channel channel : channelList) {
            CommonChannelInfo channelInfo = new CommonChannelInfo();
            channelInfo.setId(channel.getId());
            channelInfo.setName(channel.getName());
            channelInfo.setLevel(channel.getLevel());
            channelInfo.setPid(channel.getPid());
            channelInfo.setCategoryIds(Arrays.asList(channelInfo.getPid(), channel.getId()));
            channelInfo.setHasChildren(channel.getLevel() != 2);
            resultList.add(channelInfo);
        }
        return Response.ok(resultList);
    }

    @Override
    public Response<List<CommonChannel>> findCommonChannelList(ChannelQueryRequest request) {
        List<Long> commonRelationIds = commonActivityManageService.queryCommonActivityManage(request.getCommonType(),request.getOperatorId());
        if (CollectionUtils.isEmpty(commonRelationIds)) {
            return Response.ok(Collections.emptyList());
        }
        List<Long> channelIds = commonActivityChannelBindingService.findCommonChannelList(request.getOperatorId(),commonRelationIds);
        if (CollectionUtils.isEmpty(channelIds)) {
            return Response.ok(Collections.emptyList());
        }
        return Response.ok(commonChannelConverter.domain2info(channelReadDomainService.findByIds(new HashSet<>(channelIds))));
    }
}

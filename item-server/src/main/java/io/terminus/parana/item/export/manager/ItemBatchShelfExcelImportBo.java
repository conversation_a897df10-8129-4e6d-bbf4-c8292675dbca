package io.terminus.parana.item.export.manager;

import io.terminus.parana.item.util.ExcelAnnotation;
import io.terminus.parana.item.util.excel.annotation.ExcelModel;
import io.terminus.parana.item.util.excel.annotation.ExcelSingleField;
import io.terminus.parana.item.web.excel.AbstractExcelImportModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 商品导入下架模型
 *
 * @date 2025-10-15
 */
@Data
@ExcelModel
@EqualsAndHashCode(callSuper = true)
public class ItemBatchShelfExcelImportBo extends AbstractExcelImportModel implements Serializable {

    private static final long serialVersionUID = 6995376571949953451L;


    @ExcelSingleField(columnPosition = 0,columnName = "商品ID",required = false)
    @ExcelAnnotation(columnIndex = 0,columnName = "商品ID",isLock = false)
    private String itemId;

    @ExcelSingleField(columnPosition = 1,columnName = "导入结果",required = false)
    @ExcelAnnotation(columnIndex = 1,columnName = "导入结果",isLock = false)
    private String results;

    @ExcelSingleField(columnPosition = 2,columnName = "异常信息",required = false)
    @ExcelAnnotation(columnIndex = 2,columnName = "异常信息",isLock = false)
    private String reason;
}

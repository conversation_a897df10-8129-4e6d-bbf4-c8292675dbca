package io.terminus.parana.item.task.repository;

import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.task.model.TaskExecutionControl;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class TaskExecutionControlDAO extends MyBatisDao<TaskExecutionControl> {

    public Integer updateGet(TaskExecutionControl taskExecutionControl){
        return getSqlSession().update(sqlId("updateGet"),taskExecutionControl);
    }

    public Boolean updateVersion(TaskExecutionControl taskExecutionControl){
        return getSqlSession().update(sqlId("updateVersion"),taskExecutionControl) == 1;
    }
    public Boolean updateRelease(TaskExecutionControl taskExecutionControl){
        return getSqlSession().update(sqlId("updateRelease"),taskExecutionControl) == 1;
    }

    public List<TaskExecutionControl> findByTaskNameAndSource(TaskExecutionControl taskExecutionControl){
        return getSqlSession().selectList(sqlId("findByTaskNameAndSource"),taskExecutionControl);
    }

    public List<TaskExecutionControl> findByTaskName(TaskExecutionControl taskExecutionControl){
        return getSqlSession().selectList(sqlId("findByTaskName"),taskExecutionControl);
    }
}

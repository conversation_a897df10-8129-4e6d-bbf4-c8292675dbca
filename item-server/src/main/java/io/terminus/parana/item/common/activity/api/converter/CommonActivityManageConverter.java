package io.terminus.parana.item.common.activity.api.converter;

import io.terminus.parana.item.common.activity.api.bean.response.CommonActivityManageInfo;
import io.terminus.parana.item.common.activity.api.bean.response.CommonActivityManageListInfo;
import io.terminus.parana.item.common.activity.model.CommonActivityManage;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface CommonActivityManageConverter {

    CommonActivityManageInfo domain2info(CommonActivityManage commonActivityManage);

    CommonActivityManageListInfo domain2infoList(CommonActivityManage commonActivityManage);
}

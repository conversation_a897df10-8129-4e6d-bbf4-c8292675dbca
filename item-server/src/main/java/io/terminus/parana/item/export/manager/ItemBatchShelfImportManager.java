package io.terminus.parana.item.export.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.item.area.api.bean.request.AdminAreaItemOffShelfRequest;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.repository.AreaItemDao;
import io.terminus.parana.item.area.service.AreaItemWriteDomainService;
import io.terminus.parana.item.choicelot.model.DistributorItemLibModel;
import io.terminus.parana.item.choicelot.repository.DistributorItemLibDao;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.ParanaItemGroup;
import io.terminus.parana.item.item.repository.ItemDao;
import io.terminus.parana.item.item.repository.ParanaItemGroupDao;
import io.terminus.parana.item.plugin.third.api.misc.api.ExcelReportWriteApi;
import io.terminus.parana.item.shop.util.AliyunOssFactory;
import io.terminus.parana.item.third.param.ThirdUploadReportCreateRequest;
import io.terminus.parana.item.third.param.ThirdUploadReportDetailRequst;
import io.terminus.parana.item.util.ExcelAnnotation;
import io.terminus.parana.item.web.excel.ProcessResult;
import io.terminus.parana.misc.itemorder.api.api.bean.request.ParanaThirdMessageBean;
import io.terminus.parana.misc.itemorder.api.api.bean.request.ParanaThirdMessageCreateRequest;
import io.terminus.parana.misc.itemorder.api.api.facade.ParanaThirdMessageWriteFacade;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.lang.reflect.Field;
import java.net.URL;
import java.util.*;

@Slf4j
@Component
public class ItemBatchShelfImportManager {

    @Autowired
    private ExcelReportWriteApi excelReportWriteApi;

    @Autowired
    private ItemBatchShelfImportTranscriptProcessor itemBatchShelfImportTranscriptProcessor;

    @Autowired
    private AreaItemWriteDomainService areaItemWriteDomainService;

    @Autowired
    private AliyunOssFactory aliyunOssFactory;



    @SneakyThrows
    public void execute() {
        log.info("中台导入商品下架启动初始化开始=====");
        Long startTime = System.currentTimeMillis();
        //删除超过一周的数据
        excelReportWriteApi.deleteWeekAgo("item-upload");
        int pageNo = 1;
        int pageSize = 20;
        ThirdUploadReportCreateRequest uploadRequest = new ThirdUploadReportCreateRequest();
        uploadRequest.setReportType("item_batch_shelf_import");
        uploadRequest.setUploadStauts(0);
        uploadRequest.setCenter("item-upload");
        uploadRequest.setPageNo(pageNo);
        uploadRequest.setPageSize(pageSize);
        List<ThirdUploadReportCreateRequest> result = excelReportWriteApi.pageRead(uploadRequest);
        log.info("ItemBatchShelfImport excelReportWriteApi result::{}", result.size());
        if (CollectionUtils.isNotEmpty(result)) {
            for (ThirdUploadReportCreateRequest reportInfoResponse : result) {
                try {
                    // 更新导入记录状态
                    updateReport(reportInfoResponse.getId(), 1); // 处理中

                    // 从OOS上下载文件
                    InputStream inputStream = new URL(reportInfoResponse.getFileUrl()).openStream();
                    MultipartFile multipartFile = new MockMultipartFile(reportInfoResponse.getFileName(), reportInfoResponse.getFileName() + ".xlsx", "", inputStream);
                    // 开始解析文件，所有商品信息
                    ProcessResult<ItemBatchShelfExcelImportBo> process = itemBatchShelfImportTranscriptProcessor.process(multipartFile);
                    List<ItemBatchShelfExcelImportBo> excelData = process.getData();
                    log.info("导入商品绑定组信息:{}", JSON.toJSONString(excelData));
                    String createdBy = reportInfoResponse.getUserId() + "";

                    List<ItemBatchShelfExcelImportBo> results = Lists.newArrayList();
                    int i =0;//行
                    for (ItemBatchShelfExcelImportBo importBo : excelData) {
                        i++;
                        int cell = 0;
                        try {
                            if(StringUtils.isEmpty(importBo.getItemId()) || !NumberUtil.isLong(importBo.getItemId())){
                                cell = 1;
                                throw new ServiceException("商品ID不能为空或者不是数字类型！");
                            }
                            AdminAreaItemOffShelfRequest request = new AdminAreaItemOffShelfRequest();
                            request.setUpdateBy(createdBy);
                            request.setTenantId(RequestContext.getTenantId());
                            request.setOffShelTime(new Date());
                            Map<Long, String> operatorIdAndItemIdsMap = Maps.newHashMap();
                            operatorIdAndItemIdsMap.put(1L, importBo.getItemId());
                            request.setOperatorIdAndItemIdsMap(operatorIdAndItemIdsMap);
                            areaItemWriteDomainService.adminOperatorOffShelfBatch(request);
                        } catch (ServiceException e) {
                            importBo.setResults("失败");
                            importBo.setReason(e.getMessage());
                            insertErrorExcel(reportInfoResponse,  cell, e.getMessage(), i , e.getMessage());
                            results.add(importBo);
                            continue;
                        } catch (Exception e) {
                            importBo.setResults("失败");
                            importBo.setReason("导入失败！");
                            log.error("fail to import item coupon, cause: {}", Throwables.getStackTraceAsString(e));
                            results.add(importBo);
                            insertErrorExcel(reportInfoResponse,  cell, e.getMessage(), i , e.getMessage());
                            continue;
                        }
                        importBo.setResults("成功");
                        importBo.setReason("");
                        results.add(importBo);
                    }

                    ItemBatchShelfExcelImportBo importBo = results.stream().filter(e -> "失败".equals(e.getResults())).findFirst().orElse(null);
                    if (importBo == null){
                        updateReport(reportInfoResponse.getId(), 2); // 处理成功
                    } else {
                        String url = exportedExcelErrorList(results, reportInfoResponse.getId());
                        updateReport(reportInfoResponse, -1, url);
                    }

                } catch (Exception e) {
                    ThirdUploadReportDetailRequst thirdUploadReportDetailRequst = new ThirdUploadReportDetailRequst();
                    thirdUploadReportDetailRequst.setCol(0);
                    thirdUploadReportDetailRequst.setRow(0);
                    thirdUploadReportDetailRequst.setUploadReportId(reportInfoResponse.getId());
                    thirdUploadReportDetailRequst.setReason("导入失败系统错误");
                    thirdUploadReportDetailRequst.setSuggest("导入失败系统错误");
                    thirdUploadReportDetailRequst.setCreateAt(new Date());
                    thirdUploadReportDetailRequst.setUpdatedAt(new Date());
                    excelReportWriteApi.detailCreate(thirdUploadReportDetailRequst);
                    updateReport(reportInfoResponse.getId(),-1);
                    log.error("item coupon upload error:{}", Throwables.getStackTraceAsString(e));
                }
            }

        }

        log.info("[ITEM JOB:卡券导入定时导入]=====处理完成，文件个数{}===== 耗时：{}ms", result.size(), System.currentTimeMillis() - startTime);
    }



    /**
     * 更新导入记录状态
     *
     * @param id
     * @param status
     */
    private void updateReport(Long id, int status) {
        // 更新导入记录状态
        ThirdUploadReportCreateRequest uploadReportUpdateRequest = new ThirdUploadReportCreateRequest();
        uploadReportUpdateRequest.setUploadStauts(status);
        uploadReportUpdateRequest.setId(id);
        uploadReportUpdateRequest.setUpdatedAt(new Date());
        excelReportWriteApi.update(uploadReportUpdateRequest);
    }

    private void updateReport(ThirdUploadReportCreateRequest reportInfoResponse, int status, String url) {
        // 更新导入记录状态
        ThirdUploadReportCreateRequest uploadReportUpdateRequest = new ThirdUploadReportCreateRequest();
        uploadReportUpdateRequest.setUploadStauts(status);
        uploadReportUpdateRequest.setId(reportInfoResponse.getId());
        uploadReportUpdateRequest.setUpdatedAt(new Date());
        Map<String, Object> map = Maps.newHashMap();
        map.put("url",url);
        uploadReportUpdateRequest.setExtendJson(JSONUtil.toJsonStr(map));
        excelReportWriteApi.update(uploadReportUpdateRequest);
    }

    private String exportedExcelErrorList(List<ItemBatchShelfExcelImportBo> importErrorExportTemplates, Long reportId){
        try {
            if (org.apache.commons.collections.CollectionUtils.isEmpty(importErrorExportTemplates)){
                return null;
            }
            String fileName = "item_coupon_import_results" + DateUtil.now() + ".xlsx";
            ByteArrayOutputStream bao = new ByteArrayOutputStream();
            itemDownloadTemplate(importErrorExportTemplates, ItemBatchShelfExcelImportBo.class, bao);
            byte[] bytes = bao.toByteArray();

            String url = aliyunOssFactory.uploadFile(fileName, new ByteArrayInputStream(bytes));

            return url;


        } catch (IllegalAccessException e) {
            log.error("exportedExcelErrorList upload error::{}",e.getMessage());
            return null;
        }
    }

    public void itemDownloadTemplate(List list, Class<?> clazz, OutputStream outputStream) throws IllegalAccessException {

        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet xssfSheet = wb.createSheet(clazz.getSimpleName());

        XSSFCellStyle titleStyle = (XSSFCellStyle) wb.createCellStyle();
        titleStyle.setLocked(true);

        XSSFCellStyle cellStyle = (XSSFCellStyle) wb.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());

        int i = 0;
        //标题
        Row titleRow = xssfSheet.createRow(i);
        for (Field field : clazz.getDeclaredFields()) {
            ExcelAnnotation excelAnnotation = field.getAnnotation(ExcelAnnotation.class);
            if (excelAnnotation != null) {
                int columnIndex = excelAnnotation.columnIndex();
                Cell cell = titleRow.createCell(columnIndex);
                cell.setCellValue(excelAnnotation.columnName());
            }
        }
        for (Object obj : list) {
            i++;
            Row row = xssfSheet.createRow(i);
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                ExcelAnnotation excelAnnotation = field.getAnnotation(ExcelAnnotation.class);
                if (excelAnnotation != null) {
                    int columnIndex = excelAnnotation.columnIndex();
                    Cell cell = row.createCell(columnIndex);
//                    log.info("rownum:::::::::::"+columnIndex);
//                    log.info("row:::::::::::"+field.getName()+":"+field.get(obj));
                    if (null != field.get(obj)) {
                        cell.setCellValue(field.get(obj).toString());
                    } else {
                        cell.setCellValue("");
                    }
                    if (excelAnnotation.isLock()) {
                        cell.setCellStyle(cellStyle);
                    }
                }
            }
        }
        try {
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("fail to download excel template:{}, cause:{}",
                    clazz.getSimpleName(), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 新增导入记录详情,生成错误记录
     *
     * @param request
     * @param col
     * @param reason
     * @param row
     * @param suggest
     */
    private void insertErrorExcel(ThirdUploadReportCreateRequest request, Integer col, String reason, Integer row, String suggest) {
        ThirdUploadReportDetailRequst thirdUploadReportDetailRequst = new ThirdUploadReportDetailRequst();
        thirdUploadReportDetailRequst.setCreateAt(new Date());
        thirdUploadReportDetailRequst.setUpdatedAt(new Date());
        thirdUploadReportDetailRequst.setUploadReportId(request.getId());
        thirdUploadReportDetailRequst.setCol(col);
        thirdUploadReportDetailRequst.setReason(reason);
        thirdUploadReportDetailRequst.setRow(row);
        thirdUploadReportDetailRequst.setSuggest(suggest);
        excelReportWriteApi.detailCreate(thirdUploadReportDetailRequst);
    }
}

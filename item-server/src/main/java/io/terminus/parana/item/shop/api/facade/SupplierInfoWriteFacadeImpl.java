package io.terminus.parana.item.shop.api.facade;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.eascs.user.journal.api.bean.request.OperationJournalCreateRequest;
import com.eascs.user.ppt.api.bean.request.UserSupplierAddressCreateRequest;
import com.eascs.user.ppt.api.facade.UserSupplierAddressWriteFacade;
import com.eascs.user.userauthentication.api.facade.UserAuthenticationWriteFacade;
import com.eascs.user.userorganization.api.bean.request.UserOrganizationRequest;
import com.eascs.user.userorganization.api.facade.UserOrganizationWriteFacade;
import com.eascs.user.userregister.api.bean.request.PptSendSmsCodeRequest;
import com.eascs.user.usersettledvendor.api.bean.request.VendorUserAddRoleRequest;
import com.eascs.user.usersettledvendor.api.facade.UserSettledVendorFacade;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.google.common.collect.Sets;
import io.terminus.common.model.Response;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.item.common.base.JsonSupport;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.item.enums.YYTItemConstant;
import io.terminus.parana.item.partnership.api.facade.VendorPartnershipWriteFacade;
import io.terminus.parana.item.partnership.model.VendorPartnership;
import io.terminus.parana.item.partnership.service.VendorPartnershipWriteDomainService;
import io.terminus.parana.item.plugin.third.api.user.api.UserWriteApi;
import io.terminus.parana.item.search.request.SupplierContractRequest;
import io.terminus.parana.item.shop.api.bean.request.*;
import io.terminus.parana.item.shop.api.bean.request.param.ShopParam;
import io.terminus.parana.item.shop.api.bean.request.param.SupplierInfoCreateParam;
import io.terminus.parana.item.shop.api.bean.response.SupplierInfoInfoResponse;
import io.terminus.parana.item.shop.api.converter.ShopApiConverter;
import io.terminus.parana.item.shop.api.converter.SupplierInfoApiConverter;
import io.terminus.parana.item.shop.enums.ShopType;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.model.SupplierInfoLogModel;
import io.terminus.parana.item.shop.model.SupplierInfoModel;
import io.terminus.parana.item.shop.service.*;
import io.terminus.parana.user.api.facade.AddressReadFacade;
import io.terminus.parana.user.api.request.address.FindAddressByIdsRequest;
import io.terminus.parana.user.api.response.AddressInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SupplierInfoWriteFacadeImpl implements SupplierInfoWriteFacade {

    private final SupplierInfoWriteService supplierInfoWriteService;
    private final SupplierInfoReadService supplierInfoReadService;
    private final SupplierInfoApiConverter supplierInfoApiConverter;
    //	private final OperationJournalWriteService operationJournalWriteService;
//	private final SignUpFacade signUpFacade;
    private final UserWriteApi userWriteApi;
    private final ShopWriteDomainService shopWriteDomainService;
    private final ShopReadDomainService shopReadDomainService;
    private final ShopApiConverter shopApiConverter;
    private final UserAuthenticationWriteFacade userAuthenticationWriteFacade;
    private final UserOrganizationWriteFacade userOrganizationWriteFacade;
    private final SupplierInfoLogWriteService supplierInfoLogWriteService;
    private final SupplierInfoLogReadService supplierInfoLogReadService;
    private final IdGenerator idGenerator;
    private final VendorPartnershipWriteDomainService vendorPartnershipWriteDomainService;
    private final UserSupplierAddressWriteFacade userSupplierAddressWriteFacade;
    private final UserSettledVendorFacade userSettledVendorFacade;
    private final AddressReadFacade addressReadFacade;
    private final ShopWriteFacade shopWriteFacade;

    @Override
    public Response<Boolean> create(SupplierInfoCreateRequest request) {
//        SupplierInfoModel model = supplierInfoApiConverter.get(request);
//        Boolean isSuccess = supplierInfoWriteService.create(model);
//        if (isSuccess) {
//            return Response.ok(isSuccess);
//        } else {
//        }
        return Response.fail("创建供应商信息表失败。");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<Boolean> update(SupplierInfoUpdateRequest request) {
        log.info("SupplierInfoUpdateRequest.update::{}",JSONUtil.toJsonStr(request));
        SupplierInfoModel model = supplierInfoApiConverter.get(request);

        SupplierInfoModel supplierInfoModel=new SupplierInfoModel();
        supplierInfoModel.setId(model.getId());
        SupplierInfoModel view = supplierInfoReadService.view(supplierInfoModel);
        if (Objects.isNull(view)){
            return Response.fail("供应商信息不存在!");
        }

        //审核页面 入驻  并且审核通过 处理供应商信息
        //SettleStatus  入驻类型0：入驻1：修改
        //type  0:新增修改页面调用    1:审核页面调用 审核通过
        if (request.getSettleStatus().equals(0) && request.getType() !=null && request.getType() == 1){
            ShopUnfrozenRequest shopUnfrozenRequest = new ShopUnfrozenRequest();
            shopUnfrozenRequest.setIdSet(Sets.newHashSet(request.getId()));
            shopUnfrozenRequest.setTenantId(1);
            shopUnfrozenRequest.setUpdatedBy(request.getUpdateByUserId());
            shopWriteFacade.unfrozen(shopUnfrozenRequest);

            if (request.getAuditStatus().equals(2)){
                vendorPartnershipWriteDomainService.audit(request.getVendorPartnershipId(),Boolean.TRUE);
            }else if (request.getAuditStatus().equals(3)){
                vendorPartnershipWriteDomainService.audit(request.getVendorPartnershipId(),Boolean.FALSE);
            }
        }

        //驳回
        if (request.getAuditStatus()==3){

            SupplierInfoLogModel supplierInfoLogModel = supplierInfoLogReadService.getViewByVendorId(view.getId());
            //判断空 防止历史数据
            if (Objects.nonNull(supplierInfoLogModel)){
                //还原修改记录
                SupplierInfoModel infoModel = new  SupplierInfoModel();
                BeanUtils.copyProperties(supplierInfoLogModel,infoModel);
                infoModel.setId(view.getId());
                infoModel.setSettleStatus(request.getSettleStatus());
                infoModel.setAuditEndTime(model.getAuditEndTime());
                infoModel.setAuditOpinion(model.getAuditOpinion());
                infoModel.setAuditStatus(model.getAuditStatus());
                infoModel.setTenantId(model.getTenantId());
                supplierInfoWriteService.update(infoModel);

                //修改为已删除 运营平台 查看对比修改前 修改后用
                SupplierInfoLogModel supplierInfoLogModel1 = new SupplierInfoLogModel();
                supplierInfoLogModel1.setId(supplierInfoLogModel.getId());
                supplierInfoLogModel1.setIsDel(Boolean.TRUE);
                Boolean update = supplierInfoLogWriteService.update(supplierInfoLogModel1);
                return Response.ok(update);
            }
        }

        //添加供应商历史记录
        if (request.getType() !=null && request.getType() == 0){

            Shop shop = new Shop();
            shop.setName(model.getEnterpriseNameAbbreviation());
            shop.setShopNames(model.getEnterpriseName());
            shop.setTenantId(1);
            shop.setId(model.getId());
            shop.setType(ShopType.VENDOR.getValue());
            Boolean update = shopWriteDomainService.update(shop);
            if (!update){
                return Response.fail("修改名称失败!");
            }

            SupplierInfoLogModel supplierInfoLogModel = new SupplierInfoLogModel();
            BeanUtils.copyProperties(view,supplierInfoLogModel);
            supplierInfoLogModel.setVendorId(view.getId());
            supplierInfoLogModel.setId(idGenerator.nextValue(SupplierInfoLogModel.class,supplierInfoLogModel.getVendorId()));
            supplierInfoLogWriteService.create(supplierInfoLogModel);
        }

        Boolean isSuccess = supplierInfoWriteService.update(model);
        if (isSuccess) {
            return Response.ok(isSuccess);
        } else {
            return Response.fail("修改供应商信息表失败。");
        }
    }

    @Override
    public Response<Boolean> delete(SupplierInfoDeleteRequest request) {
        SupplierInfoModel model = supplierInfoApiConverter.get(request);
        Boolean isSuccess = supplierInfoWriteService.delete(model);
        if (isSuccess) {
            return Response.ok(isSuccess);
        } else {
            return Response.fail("删除供应商信息表失败。");
        }
    }

    @Override
    public Response<Boolean> checkEnterpriseNameAbbreviation(String enterpriseNameAbbreviation) {
        SupplierInfoModel query = new SupplierInfoModel();
        query.setEnterpriseNameAbbreviation(enterpriseNameAbbreviation);
        query.setTenantId(RequestContext.getTenantId().longValue());
        SupplierInfoModel view = supplierInfoReadService.view(query);
        if (null != view) {
            return Response.fail("企业名称已存在，请修改后重试!");
        }
        return Response.ok(Boolean.TRUE);
    }

    @Override
    public Response<Boolean> save(ShopCreateRequest request) {
        ShopParam shopParam = request.getShopParam();
        Shop shop = shopApiConverter.param2domain(shopParam);
        SupplierInfoCreateParam supplierInfoCreateParam = request.getSupplierInfoCreateParam();
        // 添加供应商信息
        SupplierInfoModel model = new SupplierInfoModel();
        BeanUtils.copyProperties(supplierInfoCreateParam, model);
        // 供应商绑定账号的时候，需要回调供应商信息中的user_id
        if (StringUtils.isBlank(model.getApplyPhone())) {
            model.setApplyPhone("");
        }
        model.setUserId(0L);
        model.setStateLocked(0);
        // 审核通过
        model.setAuditStatus(2);
        model.setAppliedTime(new Date());
        log.info("创建供应商入参：{}", JSON.toJSONString(model));
        shop.setTenantId(request.getTenantId());
        if(null == shop.getUpdatedBy()){
            shop.setUpdatedBy(request.getUpdatedBy());
        }
        Boolean isSuccess = supplierInfoWriteService.create(model,shop);
        if (isSuccess) {
            return Response.ok(isSuccess);
        } else {
            return Response.fail("创建供应商并且提交失败。");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<Map<String,Object>> register(ShopCreateRequest request) {
        ShopParam shopParam = request.getShopParam();
        Shop shop = shopApiConverter.param2domain(shopParam);

        SupplierInfoCreateParam supplierInfoCreateParam = request.getSupplierInfoCreateParam();
        // 添加供应商信息
        SupplierInfoModel model = new SupplierInfoModel();
        BeanUtils.copyProperties(supplierInfoCreateParam, model);
        // 供应商绑定账号的时候，需要回调供应商信息中的user_id
        if (StringUtils.isBlank(model.getApplyPhone())) {
            model.setApplyPhone("");
        }
//        model.setUserId(0L);
        model.setStateLocked(0);
        model.setConcactAddress(supplierInfoCreateParam.getConcactAddress());
        model.setConcactCode(supplierInfoCreateParam.getConcactCode());
        model.setConcactMobile(supplierInfoCreateParam.getConcactMobile());
        model.setConcactName(supplierInfoCreateParam.getConcactName());
        log.info("创建供应商入参：{}", JSON.toJSONString(model));

        if (null != supplierInfoCreateParam.getOperatorId()) {
            shop.setIsOp("N");
        } else {
            shop.setIsOp("Y");
        }
        shop.setOperatorId(shopParam.getOperatorId());
        shop.setTenantId(request.getTenantId());
        model.setOperationId(shopParam.getOperatorId());
        if(null == shop.getUpdatedBy()){
            shop.setUpdatedBy(request.getUpdatedBy());
        }
        Map<String,Object> map = supplierInfoWriteService.createVendor(model,shop);
        log.info("createVendor.res::{}", JSONUtil.toJsonStr(map));
        Boolean isSuccess = (Boolean) map.get("flag");
        if (isSuccess) {
            String shopId = map.get("shopId").toString();
            this.createAcl(Long.valueOf(shopId));
            this.createUserSupplierAddressCreateRequest(supplierInfoCreateParam,Long.valueOf(shopId));
            return Response.ok(map);
        } else {
            return Response.fail("创建供应商并且提交失败。");
        }
    }


    private void createUserSupplierAddressCreateRequest(SupplierInfoCreateParam supplierInfoCreateParam,Long shopId) {
        try {
            if (ObjectUtil.isNull(supplierInfoCreateParam) || StringUtils.isBlank(supplierInfoCreateParam.getConcactAddress())) {
                return;
            }
            String concactCode = supplierInfoCreateParam.getConcactCode();
            List<Long> addressIdList = Arrays.asList(concactCode.split("/")).stream().map(Long::valueOf).collect(Collectors.toList());

            FindAddressByIdsRequest byIdsRequest = new FindAddressByIdsRequest();
            byIdsRequest.setIds(addressIdList);
            log.info("user.byIdsRequest::{}", JSONUtil.toJsonStr(byIdsRequest));
            List<AddressInfo> addressInfoList = addressReadFacade.findByIds(byIdsRequest).getResult();
            Map<Long/*id*/, AddressInfo> addressInfoMap = addressInfoList.stream()
                    .collect(Collectors.toMap(AddressInfo::getId, Function.identity()));


            UserSupplierAddressCreateRequest createRequest = new UserSupplierAddressCreateRequest();
            Long provinceId = addressIdList.get(0);
            AddressInfo provinceInfo = addressInfoMap.get(provinceId);
            createRequest.setProvinceId(provinceId);
            createRequest.setProvinceName(provinceInfo.getName());

            Long cityId = addressIdList.get(1);
            AddressInfo cityInfo = addressInfoMap.get(provinceId);
            createRequest.setCityId(cityId);
            createRequest.setCityName(cityInfo.getName());

            Long districtId = addressIdList.get(2);
            AddressInfo districtInfo = addressInfoMap.get(districtId);
            createRequest.setDistrictId(districtId);
            createRequest.setDistrictName(districtInfo.getName());

            createRequest.setContactsTelephone(supplierInfoCreateParam.getConcactMobile());
            createRequest.setContactsName(supplierInfoCreateParam.getConcactName());
            createRequest.setDetailAddress(supplierInfoCreateParam.getConcactAddress());
            createRequest.setSupplierId(shopId);
            createRequest.setWhetherDefault(Boolean.TRUE);
            createRequest.setAddressName("入驻默认地址");
            userSupplierAddressWriteFacade.create(createRequest);
        }catch (Exception e){
            log.error("供应商入驻创建退货地址失败");
            log.error(Throwables.getStackTraceAsString(e));
        }

    }

    public Response<Boolean> createAcl(Long shopId) {
//        List<AclRolePermissionGrantRequest.AclPermission> permissions = new ArrayList<>();
//        AclRolePermissionGrantRequest.AclPermission aclPermission = new AclRolePermissionGrantRequest.AclPermission();
//        aclPermission.setPermissionRule("help");
//        permissions.add(aclPermission);
        SupplierInfoModel supplierInfoQueryRequest = new SupplierInfoModel();
        supplierInfoQueryRequest.setId(shopId);
        SupplierInfoModel view = supplierInfoReadService.view(supplierInfoQueryRequest);
//        AclRoleAdjustForItemRequest aclRoleAdjustForItemRequest = new AclRoleAdjustForItemRequest();
//        aclRoleAdjustForItemRequest.setAffiliationId(shopId.toString());
//        aclRoleAdjustForItemRequest.setAffiliationType("SELLER");
//        aclRoleAdjustForItemRequest.setBizId(view.getUserId());
//        aclRoleAdjustForItemRequest.setName("角色");
//        aclRoleAdjustForItemRequest.setDesc("角色");
//        aclRoleAdjustForItemRequest.setType("function");
//        aclRoleAdjustForItemRequest.setPermissions(permissions);
//        log.info("aclRoleAdjustForItemRequest :{}",aclRoleAdjustForItemRequest);
//        Response<Boolean> response = userAuthenticationWriteFacade.createAclForItem(aclRoleAdjustForItemRequest);

        try {
            UserOrganizationRequest userOrganizationRequest = new UserOrganizationRequest();
            userOrganizationRequest.setUserId(view.getUserId().toString());
            userOrganizationRequest.setAffiliationId(shopId.toString());
            userOrganizationRequest.setType("SELLER");
            userOrganizationRequest.setCreatedBy(view.getUserId().toString());
            userOrganizationRequest.setUpdatedBy(view.getUserId().toString());
            userOrganizationWriteFacade.createUserOrganization(userOrganizationRequest);
        } catch (Exception e) {
            log.error("账号创建失败::{}", Throwables.getStackTraceAsString(e));
            e.printStackTrace();
        }

        try {
            VendorUserAddRoleRequest vendorUserAddRoleRequest = new VendorUserAddRoleRequest();
            vendorUserAddRoleRequest.setUserId(view.getUserId());
            vendorUserAddRoleRequest.setVendorId(view.getId());
            Response<Boolean> response = userSettledVendorFacade.vendorUserAddDefaultRole(vendorUserAddRoleRequest);
            if (!response.isSuccess() || !response.getResult()){
                log.error("用户权限赋予失败res:{}",response.getError());
            }
        } catch (Exception e) {
            log.error("用户权限赋予失败::{}",Throwables.getStackTraceAsString(e));
            e.printStackTrace();
        }


        return Response.ok();
    }

    @Override
    public Response<Boolean> contract(SupplierContractRequest request) {
        if (request.getId()==null || StringUtils.isEmpty(request.getContract())){
            return Response.fail("参数不能为空");
        }
//        Boolean isSuccess = supplierInfoWriteService.contract(request);
        VendorPartnership vendorPartnership = new VendorPartnership();
        vendorPartnership.setVendorId(request.getId());
        vendorPartnership.setOperatorId(request.getOperatorId());

        String extraJson = vendorPartnership.getExtraJson();

        Map<String, Object> params = JSONUtil.parseObj(extraJson);

        params.put("contract",request.getContract());

        vendorPartnership.setExtraJson(JSONUtil.toJsonStr(params));
        Boolean aBoolean = vendorPartnershipWriteDomainService.updateByVendorIdAndOperatorId(vendorPartnership);
        if (aBoolean) {
            return Response.ok(aBoolean);
        } else {
            return Response.fail("上传合同失败。");
        }
    }

    @Override
    public Response<Boolean> createSup(SupplierInfoCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        SupplierInfoModel supplierInfoModel = new SupplierInfoModel();

        supplierInfoModel.setUserId( request.getUserId() );
        supplierInfoModel.setApplyPhone( request.getApplyPhone() );
        supplierInfoModel.setApplyCertificatePhotoFront( request.getApplyCertificatePhotoFront() );
        supplierInfoModel.setApplyCertificatePhotoBack( request.getApplyCertificatePhotoBack() );
        supplierInfoModel.setApplyName( request.getApplyName() );
        supplierInfoModel.setApplyCertificateNumber( request.getApplyCertificateNumber() );
        supplierInfoModel.setApplyCertificateValidStartTime( request.getApplyCertificateValidStartTime() );
        supplierInfoModel.setApplyCertificateValidEndTime( request.getApplyCertificateValidEndTime() );
        supplierInfoModel.setEnterpriseBusinessLicense( request.getEnterpriseBusinessLicense() );
        supplierInfoModel.setEnterpriseLogo( request.getEnterpriseLogo() );
        supplierInfoModel.setEnterpriseEmail( request.getEnterpriseEmail() );
        supplierInfoModel.setUnifiedSocialCreditCode( request.getUnifiedSocialCreditCode() );
        supplierInfoModel.setEnterpriseNameAbbreviation( request.getEnterpriseNameAbbreviation() );
        supplierInfoModel.setEnterpriseName( request.getEnterpriseName() );
        supplierInfoModel.setEnterpriseType( request.getEnterpriseType() );
        supplierInfoModel.setBusinessLicenseCode( request.getBusinessLicenseCode() );
        supplierInfoModel.setBusinessLicenseAddress( request.getBusinessLicenseAddress() );
        supplierInfoModel.setRegistrationCapital( request.getRegistrationCapital() );
        supplierInfoModel.setEnterpriseRegistrationTime( request.getEnterpriseRegistrationTime() );
        supplierInfoModel.setOperatingStartTime( request.getOperatingStartTime() );
        supplierInfoModel.setOperatingEndTime( request.getOperatingEndTime() );
        supplierInfoModel.setBusinessScope( request.getBusinessScope() );
        supplierInfoModel.setLegalPersonCertificatePhotoFront( request.getLegalPersonCertificatePhotoFront() );
        supplierInfoModel.setLegalPersonCertificatePhotoBack( request.getLegalPersonCertificatePhotoBack() );
        supplierInfoModel.setLegalPersonName( request.getLegalPersonName() );
        supplierInfoModel.setLegalPersonCertificateType( request.getLegalPersonCertificateType() );
        supplierInfoModel.setLegalPersonCertificateNumber( request.getLegalPersonCertificateNumber() );
        supplierInfoModel.setLegalPersonCertificateValidStartTime( request.getLegalPersonCertificateValidStartTime() );
        supplierInfoModel.setLegalPersonCertificateValidEndTime( request.getLegalPersonCertificateValidEndTime() );
        supplierInfoModel.setEnterpriseBusinessAddressCode( request.getEnterpriseBusinessAddressCode() );
        supplierInfoModel.setEnterpriseBusinessAddress( request.getEnterpriseBusinessAddress() );
        supplierInfoModel.setEnterprisePhone( request.getEnterprisePhone() );
        supplierInfoModel.setBankAccountName( request.getBankAccountName() );
        supplierInfoModel.setBankCardNumber( request.getBankCardNumber() );
        supplierInfoModel.setBankName( request.getBankName() );
        supplierInfoModel.setBankArea( request.getBankArea() );
        supplierInfoModel.setBankBranchName( request.getBankBranchName() );
        supplierInfoModel.setBankBranceCode( request.getBankBranceCode() );
        supplierInfoModel.setTaxpayerType( request.getTaxpayerType() );
        supplierInfoModel.setAuditStatus( request.getAuditStatus() );
        supplierInfoModel.setAuditOpinion( request.getAuditOpinion() );
        supplierInfoModel.setBillingCycle( request.getBillingCycle() );
        supplierInfoModel.setAuditStartTime( request.getAuditStartTime() );
        supplierInfoModel.setAuditEndTime( request.getAuditEndTime() );
//        if ( request.getTenantId() != null ) {
//            supplierInfoModel.setTenantId( request.getTenantId().longValue() );
//        }
        supplierInfoModel.setTenantId(request.getId());
        supplierInfoModel.setCreateUserId( request.getCreateUserId() );
        supplierInfoModel.setUpdateUserId( request.getUpdateUserId() );
        supplierInfoModel.setStateLocked( request.getStateLocked() );
        supplierInfoModel.setAppliedTime( request.getAppliedTime() );
        supplierInfoModel.setCreateTime( request.getCreateTime() );
        supplierInfoModel.setUpdateTime( request.getUpdateTime() );
        supplierInfoModel.setOperationId( request.getOperationId() );
        Boolean isSuccess = supplierInfoWriteService.createSup(supplierInfoModel);
        if (isSuccess) {
            return Response.ok(isSuccess);
        } else {
            return Response.fail("创建修改失败。");
        }
    }

    @Override
    public Response<Boolean> updateVendorDetailAuditTime(SupplierInfoUpdateRequest request) {
        return Response.ok(supplierInfoWriteService.updateVendorDetailAuditTime(request.getId()));
    }

    @Override
    public Response<Boolean> updateUserId(UpdateUserIdRequest request) {
        try {
            supplierInfoWriteService.updateUserId(request);
        } catch (Exception e) {
            log.error("updateUserId fail", e);
            return Response.fail("更新用户id失败");
        }
        return Response.ok(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<Boolean> updateSettlementInfo(SupplierInfoModSettlementRequest request) {
        SupplierInfoModel model = supplierInfoApiConverter.get(request);
        log.info("updateSettlementInfo.request:{},model:{}", request, model);
//		try {
        PptSendSmsCodeRequest pptSendSmsCodeRequest = new PptSendSmsCodeRequest();
        pptSendSmsCodeRequest.setMobile(request.getMobile());
        pptSendSmsCodeRequest.setSmsCode(request.getSmsCode());
        pptSendSmsCodeRequest.setTenantId(request.getTenantId());
        Response<Boolean> res = userWriteApi.checkSendSendSmsCode(pptSendSmsCodeRequest);
        if (!res.isSuccess()) {
            return Response.fail(res.getError());
        }
        if (!res.getResult()) {
            return Response.fail("验证码错误");
        }
        model.setTenantId(request.getTenantId().longValue());
        supplierInfoWriteService.update(model);
        Shop byId = shopReadDomainService.findById(request.getId(),request.getTenantId(),null);
        String extraJson = byId.getExtraJson();
        /**
         * 需要同时更新shop表的信息
         * "bankName": "开户银行",  1
         * "bankSubBranchName": "开户支行名称", 1
         * "bankAccount": "银行卡号", 1
         * "bankAccountName": "银行账户名", 1
         */
        Map<String, String> map = new HashMap<>();
        try {
            map = JsonSupport.JSON.objectMapper.readValue(extraJson, new TypeReference<Map<String, String>>(){});
        } catch (IOException e) {
            log.error("修改企业结算信息转换json错误",e);
            return Response.fail("修改企业结算信息转换json错误");
        }
        map.put(YYTItemConstant.SHOP_BANK_NAME.getKey(), request.getBankName());
        map.put(YYTItemConstant.SHOP_BANK_SUB_BRANCH_NAME.getKey(), request.getBankBranchName());
        map.put(YYTItemConstant.SHOP_BANK_ACCOUNT.getKey(), request.getBankCardNumber());
        map.put(YYTItemConstant.SHOP_BANK_ACCOUNT_NAME.getKey(), request.getBankAccountName());
        Shop shop = new Shop();
        shop.setId(request.getId());
        shop.setExtra(map);
        shopWriteDomainService.updateById(shop);
        OperationJournalCreateRequest operationJournalCreateRequest = new OperationJournalCreateRequest();
        operationJournalCreateRequest.setEnterpriseId(request.getId());
        operationJournalCreateRequest.setMobile(request.getMobile());
        operationJournalCreateRequest.setOperationContent("修改结算信息");
        operationJournalCreateRequest.setOperationName(request.getUserName());
        operationJournalCreateRequest.setOperationView("企业信息");
        operationJournalCreateRequest.setUserId(request.getUserId());
        operationJournalCreateRequest.setTenantId(request.getTenantId());
        userWriteApi.saveOperationJournal(operationJournalCreateRequest);
//		} catch (Exception e) {
//			log.error("updateSettlementInfo fail:{},meg:{}", Throwables.getStackTraceAsString(e),e.getMessage());
//			return Response.fail("修改供应商结算信息失败。");
//		}
        return Response.ok(Boolean.TRUE);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<Boolean> updateCompanyInfo(SupplierInfoModCompanyRequest request) {
        SupplierInfoModel model = supplierInfoApiConverter.get(request);
        model.setTenantId(request.getTenantId().longValue());
        log.info("updateSettlementInfo.request:{},model:{}", request, model);
//        try {
            supplierInfoWriteService.update(model);
            /**
             * 需要同时更新shop表的信息
             * 	"shopNames": "企业全称",
             */
            Shop shop = new Shop();
            shop.setId(request.getId());
//            shop.setShopNames(request.getEnterpriseName());
            shop.setName(request.getEnterpriseNameAbbreviation());
            shopWriteDomainService.updateById(shop);
            OperationJournalCreateRequest operationJournalCreateRequest = new OperationJournalCreateRequest();
            operationJournalCreateRequest.setEnterpriseId(request.getId());
            operationJournalCreateRequest.setMobile(request.getMobile());
            operationJournalCreateRequest.setOperationContent("修改企业信息");
            operationJournalCreateRequest.setOperationName(request.getUserName());
            operationJournalCreateRequest.setOperationView("企业信息");
            operationJournalCreateRequest.setUserId(request.getUserId());
            operationJournalCreateRequest.setTenantId(request.getTenantId());
            userWriteApi.saveOperationJournal(operationJournalCreateRequest);
//        } catch (Exception e) {
//            log.error("updateCompanyInfo fail", e);
//            Response.fail("修改供应商公司信息失败。");
//        }
        return Response.ok(Boolean.TRUE);
    }



}

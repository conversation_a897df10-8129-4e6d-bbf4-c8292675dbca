package io.terminus.parana.item.open.repository;

import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.open.bo.OpenUpdateItemsStatusBO;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class SkuOpenDao extends MyBatisDao<Sku> {

    public boolean openUpdateSkuStatus(OpenUpdateItemsStatusBO bo) {
        return sqlSession.update(sqlId("openUpdateSkuStatus"), bo) != 0;
    }


}

package io.terminus.parana.item.export.manager;

import io.terminus.parana.item.util.ExcelAnnotation;
import io.terminus.parana.item.util.excel.annotation.ExcelModel;
import io.terminus.parana.item.util.excel.annotation.ExcelSingleField;
import io.terminus.parana.item.web.excel.AbstractExcelImportModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 商品导入模型
 *
 * @date 2021年11月18日14:21:23
 */
@Data
@ExcelModel
@EqualsAndHashCode(callSuper = true)
public class ItemExcelTranscriptImportBo extends AbstractExcelImportModel implements Serializable {

    private static final long serialVersionUID = 6995376571949953451L;


    @ExcelSingleField(columnPosition = 0,columnName = "商品名称",required = false)
    @ExcelAnnotation(columnIndex = 0,columnName = "商品名称",isLock = false)
    private String name;

    @ExcelSingleField(columnPosition = 1,columnName = "广告语",required = false)
    @ExcelAnnotation(columnIndex = 1,columnName = "广告语",isLock = false)
    private String advertise;

    @ExcelSingleField(columnPosition = 2,columnName = "关键词",required = false)
    @ExcelAnnotation(columnIndex = 2,columnName = "关键词",isLock = false)
    private String keyword;

    @ExcelSingleField(columnPosition = 3,columnName = "商品类目",required = false)
    @ExcelAnnotation(columnIndex = 3,columnName = "商品类目",isLock = false)
    private String category;

    @ExcelSingleField(columnPosition = 4,columnName = "税收分类编码",required = false)
    @ExcelAnnotation(columnIndex = 4,columnName = "税收分类编码",isLock = false)
    private String taxCode;

    @ExcelSingleField(columnPosition = 5,columnName = "品牌名称",required = false)
    @ExcelAnnotation(columnIndex = 5,columnName = "品牌名称",isLock = false)
    private String brandName;

    @ExcelSingleField(columnPosition = 6,columnName = "运费模板名称",required = false)
    @ExcelAnnotation(columnIndex = 6,columnName = "运费模板名称",isLock = false)
    private String deliveryFeeTempName;

    @ExcelSingleField(columnPosition = 7,columnName = "是否可退货",required = false)
    @ExcelAnnotation(columnIndex = 7,columnName = "是否可退货",isLock = false)
    private String supportReturn;

    @ExcelSingleField(columnPosition = 8,columnName = "商品规格",required = false)
    @ExcelAnnotation(columnIndex = 8,columnName = "商品规格",isLock = false)
    private String itemAttribute;

    @ExcelSingleField(columnPosition = 9, columnName = "商品国际编码",required = false)
    @ExcelAnnotation(columnIndex = 9,columnName = "商品国际编码",isLock = false)
    private String itemTaxCode;

    @ExcelSingleField(columnPosition = 10,columnName = "供货价",required = false)
    @ExcelAnnotation(columnIndex = 10,columnName = "供货价",isLock = false)
    private String supplyPrice;

    @ExcelSingleField(columnPosition = 11,columnName = "库存",required = false)
    @ExcelAnnotation(columnIndex = 11,columnName = "库存",isLock = false)
    private String stockNum;

    @ExcelSingleField(columnPosition = 12,columnName = "起售数量",required = false)
    @ExcelAnnotation(columnIndex = 12,columnName = "起售数量",isLock = false)
    private String minQuantity;

    @ExcelSingleField(columnPosition = 13,columnName = "建议零售价",required = false)
    @ExcelAnnotation(columnIndex = 13,columnName = "建议零售价",isLock = false)
    private String suggestedRetailPrice;

    @ExcelSingleField(columnPosition = 14,columnName = "规格条码",required = false)
    @ExcelAnnotation(columnIndex = 14,columnName = "规格条码",isLock = false)
    private String pmodelCode;

    @ExcelSingleField(columnPosition = 15,columnName = "是否启用",required = false)
    @ExcelAnnotation(columnIndex = 15,columnName = "是否启用",isLock = false)
    private String enable;

}

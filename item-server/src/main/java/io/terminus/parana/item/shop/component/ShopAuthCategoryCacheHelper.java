package io.terminus.parana.item.shop.component;

import io.terminus.parana.item.common.cache.SimpleRedisHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-05-12
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ShopAuthCategoryCacheHelper {
    private final SimpleRedisHelper simpleRedisHelper;
    private static final Map<Long, String> localDirectKeyCache = new HashMap<>();
    private static final Map<Long, String> localPathKeyCache = new HashMap<>();

    public String generateDirectCacheKey(Long shopId) {
        return localDirectKeyCache.computeIfAbsent(shopId, it -> String.format("Shop_Auth_Category_By_Shop:%d", it));
    }

    public String generatePathCacheKey(Long shopId) {
        return localPathKeyCache.computeIfAbsent(shopId, it -> String.format("Shop_Auth_Category_Path_By_Shop:%d", shopId));
    }

    public boolean existCache(Long shopId) {
        return simpleRedisHelper.exists(generateDirectCacheKey(shopId), generatePathCacheKey(shopId));
    }

    public void releaseCache(Long shopId) {
        simpleRedisHelper.remove(generateDirectCacheKey(shopId), generatePathCacheKey(shopId));
    }
}

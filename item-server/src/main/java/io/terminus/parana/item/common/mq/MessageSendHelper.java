package io.terminus.parana.item.common.mq;

import com.google.common.base.Throwables;
import io.terminus.parana.item.common.events.MQReadable;
import io.terminus.parana.item.common.filter.RequestContext;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">Caily</a>
 * @date 2018-06-19
 */
@Slf4j
public class MessageSendHelper implements DisposableBean {

    @Autowired
    private MQProducer mqProducer;

    @Value("${terminus.rocketmq.codeResendMaxCount:3}")
    private Integer codeResendMaxCount;

    @Value("${terminus.topic}")
    private String topic;

    private ConcurrentLinkedQueue<MessagePackage> toSendMessageQueue = new ConcurrentLinkedQueue<>();

    private volatile Boolean running = Boolean.TRUE;

    @Autowired(required = false)
    private MessageExtendAbility messageExtendAbility;

    @PostConstruct
    private void init() {
        Executors.newFixedThreadPool(1).submit(this::run);
    }

    private void run() {
        MessagePackage messagePackage;

        do {
            try {
                if (toSendMessageQueue.isEmpty()) {
                    Thread.sleep(20);
                    continue;
                }

                while ((messagePackage = toSendMessageQueue.poll()) != null) {
                    Message message;

                    if (messageExtendAbility != null) {
                        if (messageExtendAbility.filter(messagePackage)) {
                            continue;
                        }

                        message = messageExtendAbility.wrap(messagePackage);
                    } else {
                        message = messagePackage.getMessageBody();
                    }

                    sendMessageCore(message);
                }

            } catch (Exception e) {
                log.error("fail to send message, cause: {}", Throwables.getStackTraceAsString(e));
            }
        } while (running);
    }


    private void sendMessageCore(Message message) {
        int trySendCount = 0;

        do {
            try {
                if (log.isDebugEnabled()) {
                    log.debug("try to send mq message at {} time", trySendCount + 1);
                }

                SendResult result = mqProducer.send(message);
                log.info("send mq message: id: {}, tag: {}, body: {}, delayTimeLevel: {}", result.getMessageId(),
                        message.getTags(), message.getBody(), message.getDelayTimeLevel());
                return;
            } catch (Exception e) {
                log.error("fail to send mq message at {} time retry, cause: {}", trySendCount, Throwables.getStackTraceAsString(e));
            }

            ++trySendCount;

            // 可能是网络问题，线程等待
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                // ignore it
            }
        } while (trySendCount < codeResendMaxCount);

        log.error("fail to send mq message {} after {} time retry", message, trySendCount);
    }

    public void sendMessage(MQReadable event) {
        toSendMessageQueue.add(MessagePackage.build(event, topic));
    }

    public void sendMessage(String tag, Object payload) {
        sendMessage(topic, tag, payload, null);
    }

    public void sendDelayMessage(String tag, Object payload, DelayTimeLevel delayTimeLevel) {
        sendMessage(topic, tag, payload, delayTimeLevel);
    }

    public void sendMessage(String topic, String tag, Object payload, @Nullable DelayTimeLevel delayTimeLevel) {
        toSendMessageQueue.add(MessagePackage.build(topic, tag, payload, delayTimeLevel));
    }

    public void sendWhenOk(boolean condition, MQReadable event) {
        if (condition) {
            sendMessage(event);
        }
    }

    @Override
    public void destroy() {
        running = Boolean.FALSE;
    }

    @Getter
    @RequiredArgsConstructor
    public static final class MessagePackage {
        private final Integer tenantId;
        private final String updatedBy;
        private final Message messageBody;

        private static MessagePackage build(MQReadable event, String topic) {
            return build(topic, event.getTag(), event, null);
        }

        private static MessagePackage build(String topic, String tag, Object payload,
                                            @Nullable DelayTimeLevel delayTimeLevel) {
            Message message = new Message();
            message.setTopic(topic);
            message.setTags(tag);
            message.setBody(payload);
            message.setDelayTimeLevel(delayTimeLevel);
            return new MessagePackage(RequestContext.getTenantId(), RequestContext.getUpdatedBy(), message);
        }
    }

    /**
     * 消息扩展能力
     * <p>
     * 消息能力扩展，其主要目的在于消息实际发送前的加工处理，其可操作包括：
     * <li>判断消息是否需要被抛弃</li>
     * </p>
     */
    public interface MessageExtendAbility {

        /**
         * 消息过滤，用于控制消息是否允许发送
         *
         * @param messagePackage 消息包
         * @return 过滤时true，否则false
         */
        default boolean filter(MessagePackage messagePackage) {
            return false;
        }

        /**
         * 消息的包装或转写操作，此操作允许基于当前的消息内容对消息本身坐调整
         *
         * @param messagePackage 消息包
         * @return 新的消息对象内容
         */
        default Message wrap(MessagePackage messagePackage) {
            return messagePackage.getMessageBody();
        }
    }
}

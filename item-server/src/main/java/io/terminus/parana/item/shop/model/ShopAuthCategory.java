package io.terminus.parana.item.shop.model;

import io.terminus.parana.item.common.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ShopAuthCategory extends BaseModel {
    private static final long serialVersionUID = 6934248921319619627L;

    private Long id;

    private Long shopId;

    private Long categoryId;

    private String path;

    private List<String> categoryIdList;

    private Integer tenantId;

    private Date createdAt;

    private String updatedBy;

    public void setPath(String path) {
        this.path = path;
        this.categoryIdList = json2object(path, LIST_OF_STRING, Collections::emptyList, "");
    }

    public void setCategoryIdList(List<String> categoryIdList) {
        this.categoryIdList = categoryIdList;
        this.path = object2json(categoryIdList, "");
    }
}

package io.terminus.parana.item.shop.repository;

import io.terminus.common.model.Paging;
import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.shop.model.SupplierInfoModel;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class SupplierInfoDao extends MyBatisDao<SupplierInfoModel> {

	/**
	 *	创建供应商信息表
	 */
	public Boolean createModel(SupplierInfoModel model) {
		return this.create(model);
	}
	/**
	 *	修改供应商信息表
	 */
	public Boolean updateModel(SupplierInfoModel model) {
		return this.update(model);
	}
	/**
	 *	删除供应商信息表
	 */
	public Boolean deleteById(SupplierInfoModel model) {
		return this.delete(model.getId());
	}
	/**
	 *	查看供应商信息表
	 */
	public SupplierInfoModel queryOne(SupplierInfoModel model){
		return this.sqlSession.selectOne(sqlId("queryOne"), model);
	}
	/**
	 *	查看供应商信息表集合
	 */
	public List<SupplierInfoModel> queryList(Map<String, Object> params){
		return this.sqlSession.selectList(sqlId("findByIds"), params);
	}

	public  List<SupplierInfoModel> listByRequest(Map<String, Object> params){
		return this.sqlSession.selectList(sqlId("listByRequest"), params);
	}
	/**
	 *	列表查询供应商信息表
	 */
	public List<SupplierInfoModel> listByModel(SupplierInfoModel model){
		return this.list(model);
	}
	/**
	 *	分页查询供应商信息表
	 */
	public Paging<SupplierInfoModel> page(Map<String, Object> params, Integer offset, Integer limit){
		return this.paging(offset, limit, params);
	}

	public void updateUserId(Map<String, Object> criteria) {
		this.sqlSession.update(this.sqlId("updateUserId"), criteria);
	}

	public Boolean contract(Map<String, Object> criteria) {
		return getSqlSession().update(sqlId("contract"),criteria)>0;
	}

	public Paging<SupplierInfoModel> selSeller(Map<String, Object> params, Integer offset, Integer limit){
		return this.selSeller(params,offset,limit);
	}

	public SupplierInfoModel queryByOperation(Long id){
		return this.sqlSession.selectOne(sqlId("queryByOperation"), id);
	}

	public SupplierInfoModel queryByTenant(Long id){
		return this.sqlSession.selectOne(sqlId("queryByTenant"), id);
	}

	public Long getAuditShopCount(Map<String,Object> params){
		return this.sqlSession.selectOne(sqlId("getAuditShopCount"),params);
	}

	public Boolean updateVendorDetailAuditTime(Long id){
		return this.sqlSession.update(sqlId("updateVendorDetailAuditTime"),id)>0;
	}
}

package io.terminus.parana.item.shop.extension;

import com.google.common.collect.Maps;
import io.terminus.common.model.Response;
import io.terminus.parana.item.area.enums.LogisticsMode;
import io.terminus.parana.item.common.extension.ExtensionResult;
import io.terminus.parana.item.common.filter.RequestContext;
import io.terminus.parana.item.item.enums.YYTItemConstant;
import io.terminus.parana.item.partnership.api.bean.request.VendorPartnershipUpdateRequest;
import io.terminus.parana.item.partnership.api.bean.request.param.VendorPartnershipParam;
import io.terminus.parana.item.partnership.api.facade.VendorPartnershipWriteFacade;
import io.terminus.parana.item.shop.enums.ShopType;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.plugin.third.api.inventory.api.WarehouseWriteApi;
import io.terminus.parana.item.third.param.ThirdWarehouseCreateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class DefaultShopDomainWriteExtension implements ShopDomainWriteExtension {

    @Autowired
    private WarehouseWriteApi warehouseWriteApi;
    @Autowired
    private VendorPartnershipWriteFacade vendorPartnershipWriteFacade;

    @Override
    public ExtensionResult beforeCreate(Shop shop) {
        if (shop.getType().equals(ShopType.AREA_OPERATOR.getValue()) || shop.getType().equals(ShopType.VENDOR.getValue())) {
            ThirdWarehouseCreateRequest warehouseRequest = new ThirdWarehouseCreateRequest();
            warehouseRequest.setTenantId(RequestContext.getTenantId());
            warehouseRequest.setWarehouseName(shop.getName());
            warehouseRequest.setAddress(shop.getAddress());
            if (shop.getType().equals(ShopType.AREA_OPERATOR.getValue())) {
                // 创建区域运营
                warehouseRequest.setOperatorId(shop.getId());
                warehouseRequest.setLogisticsMode(LogisticsMode.GOODS_COLLECTION.getValue());
            } else {
                // 创建供应商
                String operatorIdStr = shop.getExtra().get(YYTItemConstant.SHOP_OPERATOR_ID.getKey());

                if (StringUtils.isEmpty(operatorIdStr)) {
                    log.error("operator id not found, shop id:{}", shop.getId());
                    return ExtensionResult.fail(false, "warehouse.create.failed");
                }
                String logisticsMode = shop.getExtra().get(YYTItemConstant.SHOP_LOGISTICS_MODE.getKey());
                if (StringUtils.isEmpty(logisticsMode)) {
                    log.error("logisticsMode not found, shop id:{}", shop.getId());
                    return ExtensionResult.fail(false, "warehouse.create.failed");
                }
                warehouseRequest.setOperatorId(Long.parseLong(operatorIdStr));
                warehouseRequest.setVendorId(shop.getId());
                warehouseRequest.setLogisticsMode(Integer.parseInt(logisticsMode));
            }

            Response<String> warehouseResponse = warehouseWriteApi.createWarehouse(warehouseRequest);
            if (!warehouseResponse.isSuccess()) {
                log.error("warehouse create failed, param:{}", warehouseRequest);
                return ExtensionResult.fail(false, "warehouse.create.failed");
            }

            Map<String, String> extra = shop.getExtra();
            if (CollectionUtils.isEmpty(extra)) {
                extra = Maps.newHashMap();
            }
            extra.put("warehouseCode", warehouseResponse.getResult());
        }
        return ExtensionResult.internal();
    }


    @Override
    public ExtensionResult afterUpdate(Shop shop) {
        if (shop.getType().equals(ShopType.AREA_OPERATOR.getValue()) || shop.getType().equals(ShopType.VENDOR.getValue())) {

            if (shop.getType().equals(ShopType.VENDOR.getValue())) {
                Map<String, String> extra = shop.getExtra();
                if (!CollectionUtils.isEmpty(extra)) {

                    // 合作模式变更：物流模式、集货模式、仓库编码切换
                    String cooperationMode = extra.get(YYTItemConstant.SHOP_COOPERATION_MODE.getKey());
                    String logisticsMode = extra.get(YYTItemConstant.SHOP_LOGISTICS_MODE.getKey());
                    String operatorId = extra.get(YYTItemConstant.SHOP_OPERATOR_ID.getKey());
                    // 这里只会更新 合作模式、物流模式
                    VendorPartnershipUpdateRequest request = new VendorPartnershipUpdateRequest();
                    request.setUpdatedBy(RequestContext.getUpdatedBy());
                    VendorPartnershipParam param = new VendorPartnershipParam();
                    param.setVendorId(shop.getId());
                    param.setOperatorId(Long.parseLong(operatorId));
                    param.setCooperationMode(Integer.parseInt(cooperationMode));
                    param.setLogisticsMode(Integer.parseInt(logisticsMode));
                    request.setParam(param);
                    vendorPartnershipWriteFacade.update(request);

                }


            } else {

            }

        }
        return ExtensionResult.internal();
    }
}

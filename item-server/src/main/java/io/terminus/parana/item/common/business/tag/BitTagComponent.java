package io.terminus.parana.item.common.business.tag;

import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 注册位标处理方法{@link io.terminus.parana.item.common.business.tag.BitTagProcessor}的注解。
 * <p>
 * 此注解仅用于位标过程方法的注册过程，作用的类对象必须为{@link io.terminus.parana.item.common.business.tag.BitTagProcessor}，
 * 否则将被拒绝。
 * </p>
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-26
 */
@Component
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface BitTagComponent {

    /**
     * 是否为内部标
     *
     * @return 是否为内部标
     */
    boolean internal() default false;

    /**
     * 位标执行时机
     *
     * @return 执行时机集合
     */
    ExecuteType[] executeTypes();

    /**
     * 期望的执行循序
     * <p>注意：保证内部位标处理一定高于外部</p>
     * <p>从设计上，位标处理不应该区分先后顺序，否则容易出现数据未就绪问题。
     * 如果非要使用，建议仅使用最高优先级即可，切忌复杂顺序编排！</p>
     *
     * @return 执行顺序
     */
    int order() default Ordered.LOWEST_PRECEDENCE;
}

package io.terminus.parana.item.export.manager;
import io.terminus.parana.item.web.excel.AbstractExcelFileProcessor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021.06.17
 */

@Component
public class ItemImportTranscriptProcessor extends AbstractExcelFileProcessor<ItemExcelTranscriptImportBo> {


    public ItemImportTranscriptProcessor() {
        super(ItemExcelTranscriptImportBo.class);
    }

    @Override
    public ItemExcelTranscriptImportBo createNewObject() {
         return new ItemExcelTranscriptImportBo();
    }

}

package io.terminus.parana.item.shop.api.converter;

import io.terminus.parana.item.shop.api.bean.response.ShopTreeInfo;
import io.terminus.parana.item.shop.model.Shop;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public class ShopTreeApiInfoConverter {

    public static ShopTreeInfo DO2Domain(Shop dataObject) {
        ShopTreeInfo domain = new ShopTreeInfo();
        Long id=dataObject.getId();
        Long pid=dataObject.getPid();
        String name=dataObject.getName();
        String code=dataObject.getCode();
        Integer leafCount=dataObject.getLeafCount();
        if(id==null)id=0l;
        if(pid==null)pid=0l;
        if(name==null)name="";
        if(code==null)code="";
        if(leafCount==null)leafCount=0;
        if(leafCount!=null&&leafCount>0)leafCount=1;
        domain.setTitle(name);
        domain.setKey(String.valueOf(id));
        domain.setValue(id);
        domain.setId(String.valueOf(id));
        domain.setPid(String.valueOf(pid));
        domain.setCode(code);
        domain.setLeafCount(leafCount);
        return domain;
    }
}
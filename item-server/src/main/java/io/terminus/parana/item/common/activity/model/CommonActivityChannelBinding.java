package io.terminus.parana.item.common.activity.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CommonActivityChannelBinding implements Serializable {

    private static final long serialVersionUID = -8378018644356835082L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("常用活动id")
    private Long commonRelationId;

    @ApiModelProperty("渠道id")
    private Long channelId;

    @ApiModelProperty("创建时间")
    private Date createdAt;

    @ApiModelProperty("更新时间")
    private Date updatedAt;

    @ApiModelProperty("区域运营ID")
    private Long operatorId;

}

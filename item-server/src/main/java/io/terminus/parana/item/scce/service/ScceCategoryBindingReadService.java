package io.terminus.parana.item.scce.service;

import io.terminus.common.model.Paging;
import io.terminus.parana.item.scce.dao.ScceCategoryBindingDao;
import io.terminus.parana.item.scce.model.ScceCategoryBindingModel;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class ScceCategoryBindingReadService {

	private final ScceCategoryBindingDao scceCategoryBindingDao;
	/**
	 * 查询
	 */
	public ScceCategoryBindingModel view(ScceCategoryBindingModel model) {
		return scceCategoryBindingDao.queryOne(model);
	}
	/**
	 * 查询列表
	 */
	public List<ScceCategoryBindingModel> list(ScceCategoryBindingModel model) {
		return scceCategoryBindingDao.listByModel(model);
	}
	/**
	 * 分页查询列表
	 */
	public Paging<ScceCategoryBindingModel> page(Map<String, Object> params, Integer offset, Integer limit) {
		return scceCategoryBindingDao.page(params, offset, limit);
	}
}

package io.terminus.parana.item.export.manager;
import io.terminus.parana.item.web.excel.AbstractExcelFileProcessor;
import org.springframework.stereotype.Component;

@Component
public class ItemBatchUpdateImportTranscriptProcessor extends AbstractExcelFileProcessor<ItemBatchUpdateExcelImportBo> {


    public ItemBatchUpdateImportTranscriptProcessor() {
        super(ItemBatchUpdateExcelImportBo.class);
    }

    @Override
    public ItemBatchUpdateExcelImportBo createNewObject() {
         return new ItemBatchUpdateExcelImportBo();
    }

}

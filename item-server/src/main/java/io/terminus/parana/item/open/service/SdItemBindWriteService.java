package io.terminus.parana.item.open.service;

import io.terminus.parana.item.open.dao.SdItemBindDao;
import io.terminus.parana.item.open.model.SdItemBindBO;
import org.springframework.stereotype.Component;


import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class SdItemBindWriteService {

	private final SdItemBindDao sdItemBindDao;
	/**
	 * 创建
	 */
	public Boolean create(SdItemBindBO model) {
		return sdItemBindDao.createModel(model);
	}
	/**
	 * 修改
	 */
	public Boolean update(SdItemBindBO model) {
		return sdItemBindDao.updateModel(model);
	}
	/**
	 * 删除
	 */
	public Boolean delete(SdItemBindBO model) {
		return sdItemBindDao.deleteById(model);
	}
}

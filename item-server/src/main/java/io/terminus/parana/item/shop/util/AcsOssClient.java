package io.terminus.parana.item.shop.util;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.CreateBucketRequest;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 阿里云OSS客户端
 *
 * <AUTHOR>
 * @date 2020/03/27
 */
@Data
@Component
@ConfigurationProperties(prefix = "item.third-party.client.aliyun.oss")
public class AcsOssClient {

    private String endpoint;

    private String accessKeyId;

    private String accessKeySecret;

    private String bucketName;

    private OSSClient ossClient;

    public OSSClient getInstance(){
        if (ossClient == null) {
            ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        }

        //如果bucket不存在，就创建
        if (!ossClient.doesBucketExist(bucketName)) {
            ossClient.createBucket(bucketName);
            CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);
            createBucketRequest.setCannedACL(CannedAccessControlList.Private);
            ossClient.createBucket(createBucketRequest);
        }

        return ossClient;
    }
}

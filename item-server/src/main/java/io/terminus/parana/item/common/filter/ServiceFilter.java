package io.terminus.parana.item.common.filter;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.Tracer;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import io.terminus.api.request.AbstractRequest;
import io.terminus.common.model.Response;
import io.terminus.parana.item.common.filter.fallback.ServiceFallbackRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.springframework.core.Ordered;

/**
 * 基于dubbo-filter的服务接口check与限流封装<br>
 * 注意：测试代码中，check逻辑需要另外实现
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-23
 */
@Slf4j
@Activate(group = CommonConstants.PROVIDER, order = Ordered.LOWEST_PRECEDENCE)
public class ServiceFilter extends AbstractWithSentinelApiAop implements Filter {

    private static final EntryType DEFAULT_ENTRY_TYPE = EntryType.IN;

    static {
        log.info("Item Service route and sentinel component loaded!");
    }

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        String resourceName = getResourceName(invoker, invocation);
        Object[] args = invocation.getArguments();
        Entry entry = null;
        try {
            // 获取流控可用令牌，阻塞抛出BlockException异常
            entry = SphU.entry(resourceName, DEFAULT_ENTRY_TYPE, 1, args);

            // Api逻辑执行
            if (args != null && args.length > 0 && args[0] instanceof AbstractRequest) {
                return internalInvoke(invoker, invocation);
            } else {
                return invoker.invoke(invocation);
            }
        } catch (BlockException ex) {
            // 处理流控
            return ServiceFallbackRegistry.getProviderFallback().handle(invoker, invocation, ex);
        } catch (Throwable ex) {
            Tracer.trace(ex);
            return AsyncRpcResult.newDefaultAsyncResult(Response.fail(ex.getMessage()), invocation);
        } finally {
            // 释放流控令牌
            if (entry != null) {
                entry.exit(1, args);
            }
        }
    }
}

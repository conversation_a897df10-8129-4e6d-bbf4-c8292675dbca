package io.terminus.parana.item.common.activity.service;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.activity.model.CommonActivityGoodsBinding;
import io.terminus.parana.item.common.activity.repository.CommonActivityGoodsBindingDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommonActivityGoodsBindingService {

    private final CommonActivityGoodsBindingDao commonActivityGoodsBindingDao;

    /**
     * 新增用户常用商品清单
     *
     * @param commonActivityGoodsBinding 新增入参对象
     * @return 新增结果
     */
    public Boolean create(CommonActivityGoodsBinding commonActivityGoodsBinding) {
        try {
            return commonActivityGoodsBindingDao.create(commonActivityGoodsBinding);
        } catch (Exception e) {
            log.error("[CommonActivityGoodsBindingService] create failed, param:{} cause:{}", commonActivityGoodsBinding, e);
            throw new ServiceException("create.common.activity.item.binding.failed");
        }
    }

    /**
     * 根据常用活动ID查询所关联的商品IDs
     * @param commonRelationId 常用活动ID
     * @param operatorId 区域运营ID
     * @return 所关联的商品IDs
     */
    public List<Long> findItemIdsByCommonRelationId(Long commonRelationId, Long operatorId) {
        try {
            return commonActivityGoodsBindingDao.findItemIdsByCommonRelationId(commonRelationId, operatorId);
        } catch (Exception e) {
            log.error("[CommonActivityGoodsBindingService] findItemIdsByCommonRelationId fail, " +
                    "commonRelationId:{} operatorId:{} cause:{}", commonRelationId, operatorId, e);
            throw new ServiceException("find.common.relation.item.ids.failed");
        }

    }
}

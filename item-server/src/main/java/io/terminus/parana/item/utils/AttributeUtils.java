package io.terminus.parana.item.utils;

import io.terminus.parana.common.lang.util.JsonUtils;
import io.terminus.parana.item.common.enums.AttributeGroupEnum;
import io.terminus.parana.item.item.api.bean.response.item.GroupedOtherAttributeInfo;
import io.terminus.parana.item.item.api.bean.response.item.GroupedSkuAttributeInfo;
import io.terminus.parana.item.item.api.bean.response.item.OtherAttributeInfo;
import io.terminus.parana.item.item.api.bean.response.sku.SkuAttributeInfo;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @version 2020-01-17 下午12:57
 */
public final class AttributeUtils {

    public static Map<String, Set<String>> getSkuSaleAttributeMap(String skuAttributesJson) {
        if (StringUtils.isEmpty(skuAttributesJson)) {
            return new HashMap<>(1);
        }
        List<SkuAttributeInfo> skuAttributes = JsonUtils.fromJsonArray(skuAttributesJson, ArrayList.class, SkuAttributeInfo.class);
        return getSkuSaleAttributeMap(skuAttributes);
    }

    public static Map<String, Set<String>> getSkuSaleAttributeMap(List<SkuAttributeInfo> skuAttributes) {
        Map<String, Set<String>> attributeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(skuAttributes)) {
            for (SkuAttributeInfo sa : skuAttributes) {
                if (StringUtils.isEmpty(sa.getAttrVal())) {
                    continue;
                }
                Set<String> values = attributeMap.computeIfAbsent(sa.getAttrKey(), k -> new HashSet<>());
                values.add(sa.getAttrVal());
            }
        }
        return attributeMap;
    }

    public static Map<String, Set<String>> getSkuAttributeMap(String skuAttributesJson) {
        if (StringUtils.isEmpty(skuAttributesJson)) {
            return new HashMap<>(1);
        }
        List<GroupedSkuAttributeInfo> skuAttributes = JsonUtils.fromJsonArray(skuAttributesJson, ArrayList.class, GroupedSkuAttributeInfo.class);
        return getSkuAttributeMap(skuAttributes);
    }

    public static Map<String, Set<String>> getSkuAttributeMap(List<GroupedSkuAttributeInfo> skuAttributes) {
        Map<String, Set<String>> attributeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(skuAttributes)) {
            for (GroupedSkuAttributeInfo gsa : skuAttributes) {
                List<SkuAttributeInfo> attrs = gsa.getSkuAttributes();
                if (null != attrs) {
                    for (SkuAttributeInfo sa : attrs) {
                        if (StringUtils.isEmpty(sa.getAttrVal())) {
                            continue;
                        }
                        Set<String> values = attributeMap.computeIfAbsent(sa.getAttrKey(), k -> new HashSet<>());
                        values.add(sa.getAttrVal());
                    }
                }
            }
        }
        return attributeMap;
    }

    public static Map<String, String> getOtherAttributeMap(String otherAttributesJson) {
        if (StringUtils.isEmpty(otherAttributesJson)) {
            return new HashMap<>(1);
        }
        List<GroupedOtherAttributeInfo> otherAttributes = JsonUtils.fromJsonArray(otherAttributesJson, ArrayList.class, GroupedOtherAttributeInfo.class);
        return getOtherAttributeMap(otherAttributes);
    }

    public static Map<String, String> getOtherAttributeMap(List<GroupedOtherAttributeInfo> otherAttributes) {
        Map<String, String> attributeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(otherAttributes)) {
            for (GroupedOtherAttributeInfo goa : otherAttributes) {
                if (AttributeGroupEnum.BASIC.name().equals(goa.getGroup())) {
                    List<OtherAttributeInfo> attrs = goa.getOtherAttributes();
                    if (null != attrs) {
                        for (OtherAttributeInfo oa : attrs) {
                            if (StringUtils.isEmpty(oa.getAttrVal())) {
                                continue;
                            }
                            // basic分组可以覆盖其他分组同名属性
                            attributeMap.put(oa.getAttrKey(), oa.getAttrVal());
                        }
                    }
                } else {
                    List<OtherAttributeInfo> attrs = goa.getOtherAttributes();
                    if (null != attrs) {
                        for (OtherAttributeInfo oa : attrs) {
                            if (StringUtils.isEmpty(oa.getAttrVal())) {
                                continue;
                            }
                            if (attributeMap.containsKey(oa.getAttrKey())) {
                                continue;
                            }
                            attributeMap.put(oa.getAttrKey(), oa.getAttrVal());
                        }
                    }
                }
            }
        }
        return attributeMap;
    }

}

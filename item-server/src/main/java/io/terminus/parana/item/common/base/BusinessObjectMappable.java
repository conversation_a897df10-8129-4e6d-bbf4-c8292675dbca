package io.terminus.parana.item.common.base;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import io.terminus.parana.item.common.utils.RepositoryMap;

import java.util.Map;
import java.util.TimeZone;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-04-10
 */
public interface BusinessObjectMappable extends JsonSupport {
    /**
     * 强制修改Jackson，取消对于时间(<b>Date</b>)的时间戳处理，防止MYSQL不识别此类型
     */
    ObjectMapper objectMapper = new ObjectMapper()
            .setSerializationInclusion(JsonInclude.Include.NON_EMPTY)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .setTimeZone(TimeZone.getTimeZone("GMT+08:00"))
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

    default Map<String, Object> toMap() {
        @SuppressWarnings("unchecked")
        Map<String, Object> map = objectMapper.convertValue(this, Map.class);
        return RepositoryMap.appendOther(map);
    }
}

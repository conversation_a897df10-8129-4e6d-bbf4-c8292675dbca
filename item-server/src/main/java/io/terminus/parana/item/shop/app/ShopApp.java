package io.terminus.parana.item.shop.app;

import com.google.common.base.Throwables;
import com.google.common.collect.Sets;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.category.api.bean.request.BackCategoryQueryByPidRequest;
import io.terminus.parana.item.category.api.bean.response.BackCategoryInfo;
import io.terminus.parana.item.category.api.facade.BackCategoryReadFacade;
import io.terminus.parana.item.category.manager.BackCategoryService;
import io.terminus.parana.item.common.filter.RequestContext;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.item.enums.YYTItemConstant;
import io.terminus.parana.item.partnership.api.bean.response.VendorWithPartnerShip;
import io.terminus.parana.item.partnership.service.VendorPartnershipReadDomainService;
import io.terminus.parana.item.shop.api.bean.request.ShopSingleQueryByIdRequest;
import io.terminus.parana.item.shop.api.bean.response.ShopInfo;
import io.terminus.parana.item.shop.api.converter.ShopApiInfoConverter;
import io.terminus.parana.item.shop.component.ShopAuthCategoryCacheHelper;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.model.ShopAuthCategory;
import io.terminus.parana.item.shop.repository.ShopAuthCategoryDao;
import io.terminus.parana.item.shop.repository.ShopDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;
import redis.clients.util.Pool;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 封装店铺授权类目的相关渲染逻辑。
 *
 * <h3>缓存策略</h3>
 * <p>
 * 店铺授权类目存在两种缓存，带path的类目集合(简称一类缓存)与不带path的类目集合(简称二类缓存)。两者的使用目的与场景存在差别，
 * 主要考虑如下：
 * <li>1. 一类缓存中存放了path中涉及到的类目，可以快速判断某个类目下(包括自身)是否含有授权类目</li>
 * <li>2. 二类缓存是直接授权的类目，这些类目的子类目(如果存在)必为授权类目</li>
 * </p>
 * <h3>是否为授权类目的判断策略</h3>
 * <p>
 * 判断是否为授权类目，主要存在以下两种场景:
 * <li>1. 可用授权类目的列表渲染，如商家创建商品时的类目列表，此时通过pid值获取授权的子类目列表</li>
 * <li>2. 判断单个类目是否为授权类目，一般用于商品创建等场景</li>
 * </p>
 * <h3>两种场景的判断逻辑与策略</h3>
 * <p>
 * <li>1. 对于第一种场景：对于该类目下的子节点，需要根据类型判断：a)如果为叶子类目，</li>
 * <li>2. 对于第二种场景，将该类目的完整path匹配二类缓存，只要存在一个匹配，则授权，否则未授权</li>
 * </p>
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-05-06
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ShopApp {
    private final Pool<Jedis> jedisPool;
    private final ShopAuthCategoryDao shopAuthCategoryDao;
    private final BackCategoryService backCategoryService;
    private final BackCategoryReadFacade backCategoryReadFacade;
    private final ShopAuthCategoryCacheHelper shopAuthCategoryCacheHelper;
    private final VendorPartnershipReadDomainService vendorPartnershipReadDomainService;
    private final ShopDao shopDao;
    private final ShopApiInfoConverter shopApiInfoConverter;


    public List<BackCategoryInfo> renderShopAuthCategory(Long shopId, Long pid, Integer extensionType) {
        List<BackCategoryInfo> categoryInfoList = queryCategoryByPid(pid, extensionType);
        if (CollectionUtils.isEmpty(categoryInfoList)) {
            return Collections.emptyList();
        }

        loadToCacheIfNeed(shopId);

        // 如果父类目是选中的授权类目，直接返回所有类目信息
        if (isStrictAuthorized(shopId, pid)) {
            return categoryInfoList;
        }

        Set<Long> categoryIdSet = AssembleDataUtils.list2set(categoryInfoList, BackCategoryInfo::getId);
        Set<Long> authedCategoryIdSet = filterAuthedCategoryOfSamePid(shopId, categoryIdSet);

        return AssembleDataUtils.listFilter(categoryInfoList, it -> authedCategoryIdSet.contains(it.getId()));
    }

    /**
     * 判断单个类目是否为严格的授权类目
     *
     * @param shopId     店铺id
     * @param categoryId 类目id
     * @return 是否授权
     */
    public boolean isStrictAuthorized(Long shopId, Long categoryId) {
        loadToCacheIfNeed(shopId);
        String cacheKey = shopAuthCategoryCacheHelper.generateDirectCacheKey(shopId);
        if (isMember(cacheKey, categoryId.toString())) {
            return true;
        }

        List<Long> ancestorIdList = backCategoryService.findAncestorIdsOf(categoryId);
        return !member(cacheKey, ancestorIdList).isEmpty();
    }

    /**
     * 过滤授权的兄弟类目
     * <p>
     * 进入此方法，意味着pid节点中都没有直接选中的了，所以如果类目在一类缓存中不存在，则一定不是授权类目了。原因如下：
     * <li>如果该类目是直接选中的授权类目，则该类目一定在一类缓存中</li>
     * <li>如果该类目的子类目(甚至更深层级)存在直接选中的授权类目，则该类目一定在一类缓存中</li>
     * </p>
     *
     * @param shopId        店铺id
     * @param categoryIdSet 类目id集合
     * @return 授权的类目
     */
    private Set<Long> filterAuthedCategoryOfSamePid(Long shopId, Set<Long> categoryIdSet) {
        String pathCacheKey = shopAuthCategoryCacheHelper.generatePathCacheKey(shopId);
        return member(pathCacheKey, categoryIdSet);
    }

    private boolean isMember(String key, String member) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.sismember(key, member);
        }
    }

    private Set<Long> member(String cacheKey, Iterable<Long> ids) {
        Map<Long, Response<Boolean>> pipelineResponseMap = new HashMap<>();
        try (Jedis jedis = jedisPool.getResource()) {
            try (Pipeline pipeline = jedis.pipelined()) {
                for (Long member : ids) {
                    Response<Boolean> response = pipeline.sismember(cacheKey, member.toString());
                    pipelineResponseMap.put(member, response);
                }
                pipeline.sync();
            }
        } catch (Exception e) {
            log.error("fail to query category auth, cause: {}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("fail.to.render.shop.auth.category");
        }

        Set<Long> members = new HashSet<>();
        for (Map.Entry<Long, Response<Boolean>> entry : pipelineResponseMap.entrySet()) {
            if (entry.getValue().get()) {
                members.add(entry.getKey());
            }
        }

        return members;
    }

    private void loadToCacheIfNeed(Long shopId) {
        if (shopAuthCategoryCacheHelper.existCache(shopId)) {
            return;
        }

        List<ShopAuthCategory> directAuthedCategoryList = shopAuthCategoryDao.listByShopId(shopId);
        if (CollectionUtils.isEmpty(directAuthedCategoryList)) {
            return;
        }

        Set<String> directAuthedCategoryIdSet = new HashSet<>();
        Set<String> pathAuthedCategoryIdSet = new HashSet<>();
        for (ShopAuthCategory authCategory : directAuthedCategoryList) {
            directAuthedCategoryIdSet.add(authCategory.getCategoryId().toString());
            pathAuthedCategoryIdSet.addAll(authCategory.getCategoryIdList());
        }

        String directCacheKey = shopAuthCategoryCacheHelper.generateDirectCacheKey(shopId);
        String pathCacheKey = shopAuthCategoryCacheHelper.generatePathCacheKey(shopId);
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.sadd(directCacheKey, directAuthedCategoryIdSet.toArray(new String[0]));
            jedis.sadd(pathCacheKey, pathAuthedCategoryIdSet.toArray(new String[0]));
        } catch (Exception e) {
            log.error("fail to put shop auth category id to cache, cause: {}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("fail.to.render.shop.auth.category");
        }
    }

    private List<BackCategoryInfo> queryCategoryByPid(Long pid, Integer extensionType) {
        BackCategoryQueryByPidRequest request = new BackCategoryQueryByPidRequest();
        request.setPid(pid);
        request.setExtensionType(extensionType);
        request.setTenantId(RequestContext.getTenantId());
        return Assert.take(backCategoryReadFacade.findChildrenByPid(request));
    }

    /**
     * @param id 运营商ID
     * @return 供应商营id集合（存在合作关系的）
     */
    public Set<Long> queryVendorPartnerShipById(Long id) {

        List<Long> vendorIds = vendorPartnershipReadDomainService.queryVendorIdListByOperator(id);

        return Sets.newHashSet(vendorIds);

    }

    /**
     * @param id 供应商ID
     * @return 区域运营id 集合（存在合作关系的））
     */
    public Set<Long> queryOperatorPartnerShipById(Long id, Integer cooperationMode) {

        List<Long> operatorIds = vendorPartnershipReadDomainService.queryOperatorByVendor(id, cooperationMode);

        return Sets.newHashSet(operatorIds);
    }

    /**
     * @param id 合作关系表主键
     * @return
     */
    public ShopInfo findVendorPartnershipInfo(Long id) {

        VendorWithPartnerShip vendorShip = vendorPartnershipReadDomainService.findVendorShipById(id);

        if (vendorShip == null) {
            log.error("fail to find vendorShip by id:{}", id);
            throw new ServiceException("fail.to.find.vendorShip");
        }
        ShopInfo shopInfoResponse;
        ShopSingleQueryByIdRequest request = new ShopSingleQueryByIdRequest();
        request.setTenantId(RequestContext.getTenantId());
        request.setId(vendorShip.getVendorId());

        // 合作关系:合作模式、物流模式、费率、周期、status
        Shop shop = shopDao.findById(request.getId(), request.getTenantId(),null);
        shopInfoResponse = shopApiInfoConverter.domain2info(shop);

        if(null != shop && null != shop.getExtra()){
            shopInfoResponse.setContactName(String.valueOf(shop.getExtra().get(YYTItemConstant.SHOP_CONTACT_NAME)));
            shopInfoResponse.setContactMobile(String.valueOf(shop.getExtra().get(YYTItemConstant.SHOP_CONTACT_MOBILE)));
        }
        Map<String, String> extra = shopInfoResponse.getExtra();
        if (shop != null) {
            extra.put(YYTItemConstant.SHOP_COOPERATION_MODE.getKey(), String.valueOf(vendorShip.getCooperationMode()));
            extra.put(YYTItemConstant.SHOP_FEE_RATE.getKey(), vendorShip.getFeeRate() == null ? null : String.valueOf(vendorShip.getFeeRate()));
            extra.put(YYTItemConstant.SHOP_SETTLEMENT_PERIOD.getKey(), vendorShip.getSettlementPeriod());
            extra.put(YYTItemConstant.SHOP_LOGISTICS_MODE.getKey(), String.valueOf(vendorShip.getLogisticsMode()));
            extra.put(YYTItemConstant.SHOP_OPERATOR_ID.getKey(), String.valueOf(vendorShip.getOperatorId()));
            extra.put(YYTItemConstant.SHOP_VENDOR_PARTNERSHIP_STATUS.getKey(), String.valueOf(vendorShip.getStatus()));

            if(vendorShip.getCreatedAt() != null){
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                extra.put(YYTItemConstant.SHOP_VENDOR_PARTNERSHIP_CREATED_AT.getKey(), String.valueOf(sdf.format(vendorShip.getCreatedAt())));
            }


        }
        return shopInfoResponse;
    }
}

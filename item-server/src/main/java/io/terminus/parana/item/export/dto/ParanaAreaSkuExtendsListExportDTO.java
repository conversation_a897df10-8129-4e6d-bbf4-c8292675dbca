package io.terminus.parana.item.export.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.excel.annotation.ExcelExport;
import io.terminus.excel.annotation.ExcelExportCell;
import io.terminus.excel.model.ExcelBaseModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ExcelExport
public class ParanaAreaSkuExtendsListExportDTO extends ExcelBaseModel implements Serializable {

    @ExcelExportCell(titleName = "商品id", colIndex = 0)
    private Long itemId;

    @ExcelExportCell(titleName = "商品名称", colIndex = 1)
    private String itemName;

    @ExcelExportCell(titleName = "通用名称", colIndex = 2)
    private String universalName;

    @ExcelExportCell(titleName = "外部分类名称", colIndex = 3)
    private String categoryName;

    @ExcelExportCell(titleName = "外部品牌名称", colIndex = 4)
    private String brandName;

    @ExcelExportCell(titleName = "外部项目分类名称", colIndex = 5)
    private String outProjectCategoryName;

    @ExcelExportCell(titleName = "计量单位", colIndex = 6)
    private String unit;

    @ExcelExportCell(titleName = "税率", colIndex = 7)
    private BigDecimal vatrate;

    @ExcelExportCell(titleName = "税收分类编码", colIndex = 8)
    private String taxCode;

    @ExcelExportCell(titleName = "税收分类名称", colIndex = 9)
    private String taxName;

    @ExcelExportCell(titleName = "skuId", colIndex = 10)
    private Long skuId;

    @ExcelExportCell(titleName = "规格", colIndex = 11)
    private String pmodel;

    @ExcelExportCell(titleName = "商品条码", colIndex = 12)
    private String barcode;

    @ExcelExportCell(titleName = "商品型号", colIndex = 13)
    private String commoditymodel;

    @ExcelExportCell(titleName = "颜色", colIndex = 14)
    private String colour;

}

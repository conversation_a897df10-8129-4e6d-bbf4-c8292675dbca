package io.terminus.parana.item.shop.repository;

import io.terminus.parana.item.common.base.AbstractMybatisDao;
import io.terminus.parana.item.common.utils.RepositoryMap;
import io.terminus.parana.item.shop.model.ShopAuthCategory;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-05-06
 */
@Repository
public class ShopAuthCategoryDao extends AbstractMybatisDao<ShopAuthCategory> {

    public boolean deleteByShopId(Long shopId) {
        return sqlSession.delete(sqlId("deleteByShopId"), RepositoryMap.of("shopId", shopId)) != 0;
    }

    public boolean hasShopAuthCategory(Long shopId) {
        return sqlSession.selectOne(sqlId("hasShopAuthCategory"), RepositoryMap.of("shopId", shopId)) != null;
    }

    public List<ShopAuthCategory> listByShopId(Long shopId) {
        return sqlSession.selectList(sqlId("listByShopId"), RepositoryMap.of("shopId", shopId));
    }
}

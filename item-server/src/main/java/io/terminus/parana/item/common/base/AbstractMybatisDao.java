package io.terminus.parana.item.common.base;

import io.terminus.common.model.Paging;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.common.utils.RepositoryMap;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-14
 */
public abstract class AbstractMybatisDao<Model> {

    private final boolean WITH_TENANT_ID;

    private final String nameSpace;

    protected AbstractMybatisDao() {
        this(false);
    }

    protected AbstractMybatisDao(boolean withTenantId) {
        WITH_TENANT_ID = withTenantId;

        nameSpace = this.getClass().getName();
    }

    @Autowired
    protected SqlSession sqlSession;

    public boolean create(Model model) {
        return sqlSession.insert(sqlId("create"), model) == 1;
    }

    public int creates(List<Model> modelList) {
        return sqlSession.insert(sqlId("creates"), modelList);
    }

    public boolean delete(Long id) {
        return sqlSession.delete(sqlId("delete"), id) == 1;
    }

    public boolean deletes(List<Long> ids) {
        return sqlSession.delete(sqlId("deletes"), ids) != 0;
    }

    public boolean update(Model model) {
        return sqlSession.update(sqlId("update"), model) == 1;
    }

    public Model findById(Long id) {
        Assert.isFalse(WITH_TENANT_ID, "租户id模式下，必须传入");
        return sqlSession.selectOne(sqlId("findById"), id);
    }

    @Deprecated
    public Model findById(Long id, Integer tenantId) {
        if (tenantId == null || !WITH_TENANT_ID) {
            return findById(id);
        }

        return sqlSession.selectOne(sqlId("findById"), RepositoryMap.of("id", id));
    }

    public List<Model> listAll() {
        return sqlSession.selectList(sqlId("listAll"));
    }

    public Long count(Map<String, Object> criteria) {
        return sqlSession.selectOne(sqlId("count"), criteria);
    }

    public <T extends AbstractModelPagingBO> Paging<Model> paging(T bo) {
        Assert.nonNull(bo);
        Map<String, Object> criteria = bo.toMap();
        return paging(criteria);
    }

    /**
     * 基于map的条件分页，建议使用{@link #paging(AbstractModelPagingBO)}方法来代替
     *
     * @param criteria 分页条件
     * @return 分页结果
     */
    @SuppressWarnings("unchecked")
    public Paging<Model> paging(Map<String, Object> criteria) {
        Long total = count(criteria);
        if (total <= 0L) {
            return Paging.empty();
        } else {
            List<Model> datas = this.sqlSession.selectList(sqlId("paging"), criteria);
            return new Paging(total, datas);
        }
    }

    protected String sqlId(String id) {
        return this.nameSpace + "." + id;
    }

    protected SqlSession getSqlSession() {
        return this.sqlSession;
    }
}

package io.terminus.parana.item.utils;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class GenerateAppleSignUtils {

    @Value(value = "${apple.accessKey:}")
    private String accessKey;

    /**
     * 需要签名的参数
     * @param objMap
     * @return String
     */
    public String createSign(Map<String, Object> objMap){
        //参数进行排序，排除值为null的参数
        StringBuilder buff = new StringBuilder();
        String planttext = objMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(
                        ()-> buff,
                        (buf,entry)->{
                            if (Objects.isNull(entry.getValue())){
                                buf.append(entry.getKey()).append("=").append("&");
                            }else {
                                buf.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                            }
                        },
                        (buf1,buf2)->{}
                ).toString();
        String paramStr = planttext.substring(0, planttext.length() - 1);

        //进行MD5三次加密
        String s1 = toMD5(paramStr + accessKey);
        String s2 = toMD5(s1 + accessKey);
        String s3 = toMD5(s2 + accessKey);
        System.out.println(s3);
        return s3;
    }

    /**
     * 生成MD5
     * @param s 需要加密的字符串
     * @return
     */
    private String toMD5(String s){
        MessageDigest algorithm;
        try
        {
            algorithm = MessageDigest.getInstance("MD5");
            algorithm.reset();
            algorithm.update(s.getBytes("UTF-8"));
            byte[] messageDigest = algorithm.digest();
            if (messageDigest == null)
            {
                return null;
            }
            StringBuffer buf = new StringBuffer(messageDigest.length * 2);
            int i;

            for (i = 0; i < messageDigest.length; i++)
            {
                if ((messageDigest[i] & 0xff) < 0x10)
                {
                    buf.append("0");
                }
                buf.append(Long.toString(messageDigest[i] & 0xff, 16));
            }
            return buf.toString();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }



}

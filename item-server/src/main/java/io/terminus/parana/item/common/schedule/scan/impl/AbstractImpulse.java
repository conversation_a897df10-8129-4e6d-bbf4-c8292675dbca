package io.terminus.parana.item.common.schedule.scan.impl;


import io.terminus.parana.item.common.schedule.scan.process.Impulse;
import io.terminus.parana.item.common.schedule.scan.process.ImpulseExchangeData;
import io.terminus.parana.item.common.schedule.scan.process.ReportProgress;

import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-30
 */
public abstract class AbstractImpulse implements Impulse, ReportProgress {

    /**
     * 结束id
     */
    private long stop;

    /**
     * 开始id
     */
    private long start;

    /**
     * 当前结束id
     */
    private long end;

    /**
     * 是否开启无效数据区间跳跃
     */
    private final boolean withSkipAbility;

    /**
     * 数据包步长
     */
    private final int step;

    /**
     * 任务是否已完成
     */
    private boolean done = false;

    /**
     * 已经处理的数据记录数
     */
    private AtomicLong processed = new AtomicLong(0);

    /**
     * 报告步长
     */
    private int reportStep;

    /**
     * 报告步长剩余数
     */
    private int reportStepLeft = Integer.MAX_VALUE;

    /**
     * 处理进度回调方法
     */
    private Consumer<Long> progressCallback;

    public AbstractImpulse(int step) {
        this(false, step);
    }

    public AbstractImpulse(boolean withSkipAbility, int step) {
        this.withSkipAbility = withSkipAbility;
        this.step = step;
    }

    @Override
    public void init(long start, long stop) {
        this.start = start;
        this.end = start - 1;
        this.stop = stop;
    }

    @Override
    public ImpulseExchangeData getNext(int processed) {
        if (processed > 0) {
            this.processed.addAndGet(processed);
            reportStepLeft -= processed;

            if (reportStepLeft <= 0) {
                reportProgress();
            }
        }

        if (!withSkipAbility || processed > 0) {
            return getNextWithoutSkip();
        }

        throw new UnsupportedOperationException("跳跃能力未实现");
    }

    @Override
    public void stop() {
        this.stop = -1L;
        this.done = true;
    }

    private ImpulseExchangeData getNextWithoutSkip() {
        if (done) {
            return ImpulseExchangeData.done();
        }

        synchronized (AbstractImpulse.class) {
            if (done) {
                return ImpulseExchangeData.done();
            }

            start = end + 1;
            end = end + step;
            if (end >= stop) {
                done = true;
                end = stop;
            }
        }

        // 强制再走一波，获取处理数量
        return ImpulseExchangeData.make(start, end, false);
    }

    @Override
    public long getProcessed() {
        return processed.get();
    }

    private void reportProgress() {
        if (progressCallback != null) {
            progressCallback.accept(processed.get());
        }

        // 重置步长
        if (!done) {
            reportStepLeft = reportStep;
        }
    }

    @Override
    public void registerProgressCallback(Consumer<Long> callback) {
        this.progressCallback = callback;
    }

    @Override
    public void setReportLength(int length) {
        this.reportStep = length;
        this.reportStepLeft = reportStep;
    }
}

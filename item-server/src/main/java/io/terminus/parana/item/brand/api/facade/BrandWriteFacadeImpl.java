package io.terminus.parana.item.brand.api.facade;

import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.parana.item.brand.api.bean.request.BrandCreateRequest;
import io.terminus.parana.item.brand.api.bean.request.BrandDeleteRequest;
import io.terminus.parana.item.brand.api.bean.request.BrandStatusUpdateRequest;
import io.terminus.parana.item.brand.api.bean.request.BrandUpdateRequest;
import io.terminus.parana.item.brand.api.converter.BrandConverter;
import io.terminus.parana.item.brand.api.manager.BrandService;
import io.terminus.parana.item.brand.model.Brand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2018-07-12 下午5:41
 */
@Slf4j
@Component
public class BrandWriteFacadeImpl implements BrandWriteFacade {
    @Autowired
    private BrandConverter converter;
    @Autowired
    private BrandService brandService;

    @Override
    public Response<Long> create(BrandCreateRequest request) {
        try {
            Brand brand = converter.vo2domain(request);
            return Response.ok(brandService.create(brand));
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.create.fail");
        }
    }

    @Override
    public Response<Boolean> update(BrandUpdateRequest request) {
        try {
            Brand brand = converter.vo2domain(request);
            return Response.ok(brandService.update(brand));
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.update.fail");
        }
    }

    @Override
    public Response<Boolean> delete(BrandDeleteRequest request) {
        try {
            return Response.ok(brandService.delete(request.getId(),request.getUpdatedBy()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.unbinding.fail");
        }
    }

    @Override
    public Response<Boolean> updateStatus(BrandStatusUpdateRequest request) {
        try {
            return Response.ok(brandService.updateStatus(request.getId(), request.getStatus(), request.getUpdatedBy()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.update.fail");
        }
    }

    @Override
    public Response<Boolean> updateWeight(BrandStatusUpdateRequest request) {
        try {
            return Response.ok(brandService.updateWeight(request.getId(), request.getWeight(), request.getUpdatedBy()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.update.fail");
        }
    }
}

package io.terminus.parana.item.shop.bo;

import io.terminus.parana.item.partnership.model.VendorPartnership;
import io.terminus.parana.item.shop.model.Shop;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopCreateBO {

    private Shop shop;

    private VendorPartnership vendorPartnership;

}

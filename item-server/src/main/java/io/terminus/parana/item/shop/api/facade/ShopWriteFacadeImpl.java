package io.terminus.parana.item.shop.api.facade;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.eascs.user.usersettledvendor.api.bean.request.UserSettledBindVendorParam;
import com.eascs.user.usersettledvendor.api.facade.UserSettledVendorFacade;
import com.google.common.base.Throwables;
import io.terminus.api.utils.StringUtil;
import io.terminus.common.model.Response;
import io.terminus.parana.exception.RestException;
import io.terminus.parana.item.shop.api.bean.request.*;
import io.terminus.parana.item.shop.api.bean.request.param.ShopParam;
import io.terminus.parana.item.shop.api.bean.request.param.SupplierInfoCreateParam;
import io.terminus.parana.item.shop.api.converter.ShopApiConverter;
import io.terminus.parana.item.shop.api.converter.SupplierInfoApiConverter;
import io.terminus.parana.item.shop.enums.ShopType;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.model.SupplierInfoModel;
import io.terminus.parana.item.shop.service.ShopWriteDomainService;
import io.terminus.parana.item.shop.service.SupplierInfoWriteService;
import io.terminus.parana.misc.generalConfig.bean.request.GeneralConfigCreateRequest;
import io.terminus.parana.misc.generalConfig.facade.GeneralConfigWriteFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019-02-26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ShopWriteFacadeImpl implements ShopWriteFacade {

    private final SupplierInfoWriteService supplierInfoWriteService;
    private final ShopWriteDomainService shopWriteDomainService;
    private final ShopApiConverter shopApiConverter;
    private final SupplierInfoApiConverter supplierInfoConverter;
    private final GeneralConfigWriteFacade generalConfigWriteFacade;

    @Autowired
    private UserSettledVendorFacade userSettledVendorFacade;

    private final static String APP_ID_ZQ = "zqbuyer";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<Long> create(ShopCreateRequest request) {
        try {
            Shop shop = shopApiConverter.param2domain(request.getShopParam());
            shop.setTenantId(request.getTenantId());
            if (null == shop.getUpdatedBy()) {
                shop.setUpdatedBy(request.getUpdatedBy());
            }
            if (StringUtil.isEmpty(shop.getIsBrand())) {
                shop.setIsBrand("Y");
            }
            if (StringUtil.isEmpty(shop.getIsDeliery())) {
                shop.setIsDeliery("N");
            }

            //供应商创建时 根据区域运营判断是否zq标示
            if (shop.getType().equals(ShopType.VENDOR.getValue())) {
                if (StringUtils.isNotBlank(shop.getIsZq()) && StringUtils.equals("Y", shop.getIsZq())) {
                    shop.setIsZq("Y");
                } else {
                    shop.setIsZq("N");
                }
                shop.setWidth(Long.valueOf("800"));
                shop.setHeight(Long.valueOf("800"));
            } else if (shop.getType().equals(ShopType.AREA_OPERATOR.getValue())) {
                if (StringUtil.isNotEmpty(shop.getAppId()) && shop.getAppId().equals(APP_ID_ZQ)) {
                    shop.setIsZq("Y");
                } else {
                    shop.setIsZq("N");
                }
            }

            Long shopId = shopWriteDomainService.create(shop);
            shop.setId(shopId);
            log.info("ShopWriteFacadeImpl.create() userSettledId:{}", request.getUserSettledId());
            // 绑定用户入驻供应商ID
            if (request.getUserSettledId() != null) {
                UserSettledBindVendorParam bindVendorParam = new UserSettledBindVendorParam();
                bindVendorParam.setVendorId(shopId);
                bindVendorParam.setBuyerId(request.getUserSettledId());
                log.info("ShopWriteFacadeImpl.create() bindVendorParam:{}", bindVendorParam);
                userSettledVendorFacade.bindingVendor(bindVendorParam);
            }
            if (shop.getType().equals(ShopType.AREA_OPERATOR.getValue())) {
                SupplierInfoCreateParam supplierInfoCreateParam = request.getSupplierInfoCreateParam();
                // 添加运营商信息
                SupplierInfoModel model = new SupplierInfoModel();
                BeanUtils.copyProperties(supplierInfoCreateParam, model);
                supplierInfoWriteService.createOperator(model, shop);

                GeneralConfigCreateRequest configCreateRequest = new GeneralConfigCreateRequest();
                configCreateRequest.setCreatedAt(new Date());
                configCreateRequest.setCreatedBy(shop.getCreatedBy());
                configCreateRequest.setUpdatedAt(new Date());
                configCreateRequest.setUpdatedBy(shop.getCreatedBy());
                configCreateRequest.setMarginConfig(0L);
                configCreateRequest.setMarginDefaultAmount(0L);
                configCreateRequest.setMarginMinCashoutMoney(0L);
                configCreateRequest.setMarginMinRechargeMoney(0L);
                configCreateRequest.setOperatorId(shopId);
                log.info("初始化保证金配置::{}", JSONUtil.toJsonStr(configCreateRequest));
                Response<Boolean> response = generalConfigWriteFacade.create(configCreateRequest);
                if (!response.isSuccess()) {
                    log.error("configCreateRequest:{}", JSON.toJSON(configCreateRequest));
                    throw new RestException("保证金配置创建失败 ");
                }
            }
            return Response.ok(shopId);
        } catch (Exception e) {
            log.error("error : " + Throwables.getStackTraceAsString(e));
            log.error("error:{}", JSON.toJSON(e));
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> update(ShopUpdateRequest request) {
        try {
            ShopParam shopParam = request.getShopParam();
            Shop shop = shopApiConverter.param2domain(request.getShopParam());
            shop.setRegionalOperationName(request.getShopParam().getRegionalOperationName());
            shop.setTenantId(request.getTenantId());
            if (null == shop.getUpdatedBy()) {
                shop.setUpdatedBy(request.getUpdatedBy());
            }

            shopWriteDomainService.update(shop);
            if (ObjectUtil.isNotNull(shopParam.getSupplierInfoCreateParam())) {
                SupplierInfoModel supplierInfoModel = supplierInfoConverter.param2domain(shopParam.getSupplierInfoCreateParam());
                supplierInfoModel.setOperatorId(0L);
                supplierInfoModel.setId(shop.getId());
                supplierInfoWriteService.update(supplierInfoModel);
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> updateZqAttr(ShopUpdateZqRequest request) {

        try {
            ShopParam shopParam = request.getShopParam();

            Shop shopVO = new Shop();
            if (null == shopParam.getId()) {
                return Response.fail("id不能为空！");
            }
            shopVO.setId(shopParam.getId());
            if (StringUtils.isNotBlank(shopParam.getIsZq())) {
                shopVO.setIsZq(shopParam.getIsZq());
            }
            if (StringUtils.isNotBlank(shopParam.getIsBrand())) {
                shopVO.setIsBrand(shopParam.getIsBrand());
            }
            if (StringUtils.isNotBlank(shopParam.getIsDeliery())) {
                shopVO.setIsDeliery(shopParam.getIsDeliery());
            }
            if (null != shopParam.getVendorType()) {
                shopVO.setVendorType(shopParam.getVendorType());
            }
            shopVO.setUpdatedBy(request.getUpdatedBy());
            shopVO.setTenantId(request.getTenantId());

            shopWriteDomainService.updateZqAttr(shopVO);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> frozen(ShopFrozenRequest request) {
        try {
            Set<Long> ids = request.getIdSet();
            Integer tenantId = request.getTenantId();
            String updatedBy = request.getUpdatedBy();

            ids.forEach(id -> shopWriteDomainService.frozen(id, updatedBy, tenantId));
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> unfrozen(ShopUnfrozenRequest request) {
        try {
            Set<Long> ids = request.getIdSet();
            Integer tenantId = request.getTenantId();
            String updatedBy = request.getUpdatedBy();

            ids.forEach(id -> shopWriteDomainService.unfrozen(id, updatedBy, tenantId));
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> close(ShopCloseRequest request) {
        try {
            Set<Long> ids = request.getIdSet();
            Integer tenantId = request.getTenantId();
            String updatedBy = request.getUpdatedBy();

            ids.forEach(id -> shopWriteDomainService.close(id, updatedBy, tenantId));
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> flushAuthCategory(ShopFlushAuthCategoryRequest request) {
        return Response.ok(shopWriteDomainService.flushAuthCategory(request.getShopId(), request.getCategoryIdSet()));
    }

    /**
     * 店铺修改
     *
     * @param request
     * @return
     */
    @Override
    public Response<Boolean> updateVendorExtra(ShopUpdateRequest request) {
        try {
            log.info("==================ShopWriteFacadeImpl.updateVendorExtra1==============" + JSON.toJSONString(request.getShopParam()));
            Shop shop = shopApiConverter.param2domain(request.getShopParam());
            shop.setTenantId(request.getTenantId());
            if (null == shop.getUpdatedBy()) {
                shop.setUpdatedBy(request.getUpdatedBy());
            }

            log.info("==================ShopWriteFacadeImpl.updateVendorExtra==============" + JSON.toJSONString(shop));
            shopWriteDomainService.update(shop);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> setDefaultLogisticsUpdateVendorExtra(ShopUpdateRequest request) {
        ShopParam shopParam = request.getShopParam();
        Map<String, String> extra = shopParam.getExtra();
        Map<String, Object> mp = new HashMap<>();
        mp.put("id", shopParam.getId());
        mp.put("tenantId", shopParam.getTenantId());
        mp.put("extra", JSON.toJSONString(extra));
        log.info("shopWriteDomainService.setDefaultLogisticsUpdateVendorExtra.request:{}", mp);
        Boolean flag = shopWriteDomainService.setDefaultLogisticsUpdateVendorExtra(mp);
        return Response.ok(flag);
    }

    @Override
    public Response<Boolean> upByOpera(ShopUpdateRequest request) {

        ShopParam param = request.getShopParam();
        Map<String, Object> map = new HashMap<>();
        map.put("id", param.getId());
        map.put("updatedBy", request.getUpdatedBy() == null ? "" : request.getUpdatedBy());
        map.put("status", param.getStatus());
        Boolean flag = shopWriteDomainService.upByOpera(map);
        return Response.ok(flag);
    }

    @Override
    public Response<Boolean> freeze(ShopUpdateRequest request) {
        Shop shop = shopApiConverter.param2domain(request.getShopParam());
        ShopParam param = request.getShopParam();
        Map<String, Object> map = new HashMap<>();
        map.put("id", param.getId());
        map.put("updatedBy", param.getUpdatedBy());
        map.put("status", param.getStatus());
        log.info("map:{}", map);
        Boolean flag = shopWriteDomainService.update(shop);
//        Boolean flag = shopWriteDomainService.upByOpera(map);
        return Response.ok(flag);
    }

    @Override
    public Response<Boolean> updateFlowRatio(UpdateFlowRatioRequest request) {
        // 验证 flowRatio 的范围
        BigDecimal flowRatio = request.getFlowRatio();
        if (flowRatio.compareTo(BigDecimal.ZERO) < 0 || flowRatio.compareTo(new BigDecimal("1.0")) > 0) {
            return Response.fail("流水比例必须在0到1之间");
        }

        Shop shop = new Shop();
        shop.setId(request.getId());
        shop.setType(ShopType.AREA_OPERATOR.getValue());
        shop.setTenantId(request.getTenantId());
        Map<String, String> extra = new HashMap<>();
        extra.put("flowRatio", request.getFlowRatio().setScale(4, RoundingMode.HALF_UP).toString());
        shop.setExtra(extra);
        // 更新 Shop 信息
        boolean update;
        try {
            update = shopWriteDomainService.update(shop);
        } catch (Exception e) {
            log.error("更新失败: {}", Throwables.getStackTraceAsString(e));
            // 处理异常情况
            return Response.fail("更新失败");
        }

        if (!update) {
            return Response.fail("更新失败");
        }
        return Response.ok(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<Boolean> updateSdSettlementCustomerDimension(SdCustomerDimensionUpdateRequest request) {
        try {
            request.checkParam();
            Boolean result = shopWriteDomainService.updateSdSettlementCustomerDimension(
                request.getShopId(),
                request.getSdSettlementCustomerDimension(),
                request.getUpdatedBy()
            );
            return Response.ok(result);
        } catch (Exception e) {
            log.error("更新SD结算客户维度失败, request: {}", request, e);
            return Response.fail("sd.settlement.customer.dimension.update.fail");
        }
    }
}

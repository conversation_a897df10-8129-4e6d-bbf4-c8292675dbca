package io.terminus.parana.item.common.schedule.scan.impl;

import com.google.common.base.MoreObjects;
import io.terminus.parana.item.common.schedule.scan.ScheduleTask;
import io.terminus.parana.item.common.schedule.scan.process.Impulse;
import io.terminus.parana.item.common.schedule.scan.process.Persistent;
import io.terminus.parana.item.common.schedule.scan.process.RangedScanner;
import io.terminus.parana.item.common.schedule.scan.process.TaskCallback;
import io.terminus.parana.item.common.utils.Assert;

import java.util.function.Function;

/**
 * 任务构造
 *
 * @param <P> 上下文类型
 * @param <T> 中间数据类型
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-10-22
 */
public class ScheduleTaskBuilder<P, T> {

    private P context;
    private int nPush;
    private int nPull;
    private int bridgeCapacity;
    private Impulse impulse;

    private Function<P, ? extends Persistent<T>> pullExecute;
    private Function<P, ? extends RangedScanner<T>> pushExecute;

    private TaskCallback<P> taskCallback;

    private void paramCheck() {
        nPull = MoreObjects.firstNonNull(nPull, 1);
        nPush = MoreObjects.firstNonNull(nPush, 1);
        bridgeCapacity = MoreObjects.firstNonNull(bridgeCapacity, 1024);

        Assert.nonNull(pullExecute, "pull.execute.is.null");
        Assert.nonNull(pushExecute, "push.execute.is.null");
        Assert.nonNull(taskCallback, "task.callback.is.null");
    }

    public ScheduleTask<P> build() {
        paramCheck();
        return new DefaultScheduleTask<P, T>(context, impulse, nPush, nPull, pushExecute, pullExecute, taskCallback,
                bridgeCapacity);
    }

    public ScheduleTaskBuilder<P, T> context(P context) {
        this.context = context;
        return this;
    }

    public ScheduleTaskBuilder<P, T> impulse(Impulse impulse) {
        this.impulse = impulse;
        return this;
    }

    public ScheduleTaskBuilder<P, T> bridgeCapacity(int bridgeCapacity) {
        this.bridgeCapacity = bridgeCapacity;
        return this;
    }

    public ScheduleTaskBuilder<P, T> nPush(int nPush) {
        this.nPush = nPush;
        return this;
    }

    public ScheduleTaskBuilder<P, T> nPull(int nPull) {
        this.nPull = nPull;
        return this;
    }

    public ScheduleTaskBuilder<P, T> pullInitializer(Function<P, ? extends Persistent<T>> pullExecute) {
        this.pullExecute = pullExecute;
        return this;
    }

    public ScheduleTaskBuilder<P, T> pushInitializer(Function<P, ? extends RangedScanner<T>> pushExecute) {
        this.pushExecute = pushExecute;
        return this;
    }

    public ScheduleTaskBuilder<P, T> taskCallback(TaskCallback<P> taskCallback) {
        this.taskCallback = taskCallback;
        return this;
    }
}

package io.terminus.parana.item.open.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import io.terminus.parana.item.open.dao.SdItemDeptProjectBindDao;
import io.terminus.parana.item.open.model.SdItemDeptProjectBindBO;
import org.springframework.stereotype.Component;


import io.terminus.common.model.Paging;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class SdItemDeptProjectBindReadService {

	private final SdItemDeptProjectBindDao sdItemDeptProjectBindDao;
	/**
	 * 查询整购区运营跟sd平台项目绑定关系表
	 */
	public SdItemDeptProjectBindBO view(SdItemDeptProjectBindBO model) {
		return sdItemDeptProjectBindDao.queryOne(model);
	}
	/**
	 * 查询整购区运营跟sd平台项目绑定关系表列表
	 */
	public List<SdItemDeptProjectBindBO> list(SdItemDeptProjectBindBO model) {
		return sdItemDeptProjectBindDao.listByModel(model);
	}
	/**
	 * 分页查询整购区运营跟sd平台项目绑定关系表列表
	 */
	public Paging<SdItemDeptProjectBindBO> page(Map<String, Object> params, Integer offset, Integer limit) {
		return sdItemDeptProjectBindDao.page(params, offset, limit);
	}

	public List<SdItemDeptProjectBindBO> batchQueryDeptBindInfo(Set<Long> operatorIds) {
		return sdItemDeptProjectBindDao.batchQueryDeptBindInfo(operatorIds);
	}
}

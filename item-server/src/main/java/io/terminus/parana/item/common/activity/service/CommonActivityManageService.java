package io.terminus.parana.item.common.activity.service;

import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.common.activity.model.CommonActivityManage;
import io.terminus.parana.item.common.activity.repository.CommonActivityChannelBindingDao;
import io.terminus.parana.item.common.activity.repository.CommonActivityManageDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommonActivityManageService {

    private final CommonActivityManageDao commonActivityManageDao;
    private final CommonActivityChannelBindingDao commonActivityChannelBindingDao;

    /**
     * 新增用户常用商品清单
     *
     * @param commonActivityManage 新增入参对象
     * @return 新增结果
     */
    public Boolean create(CommonActivityManage commonActivityManage) {
        try {
            return commonActivityManageDao.create(commonActivityManage);
        } catch (Exception e) {
            log.error("[CommonActivityManageService] create failed, param:{} cause:{}", commonActivityManage, e);
            throw new ServiceException("create.common.activity.manage.failed");
        }
    }

    /**
     * 根据该时间范围内的已存在的活动ids
     *
     * @param commonType 活动类型
     * @param operatorId 区域运营ID
     * @param startAt 开始时间
     * @param expiredAt 截止时间
     * @return 活动ids
     */
    public List<Long> checkTimeRangeValidActive(String commonType, Long operatorId, Date startAt, Date expiredAt, Long excludeId) {
        try {
            return commonActivityManageDao.checkTimeRangeValidActive(commonType, operatorId, startAt, expiredAt, excludeId);
        } catch (Exception e) {
            log.error("[CommonActivityManageService] checkTimeRangeValidActive failed, " +
                            "commonType:{}, operatorId:{}, startAt:{}, expiredAt:{} cause:{}",
                    commonType, operatorId, startAt, expiredAt, e);
            throw new ServiceException("check.time.range.valid.active.failed");
        }
    }

    /**
     * 常用活动详情信息
     *
     * @param id 常用活动ID
     * @return 常用活动信息
     */
    public CommonActivityManage findById(Long id) {
        CommonActivityManage commonActivityManage = commonActivityManageDao.findById(id);
        if (commonActivityManage == null) {
            throw new ServiceException(String.format("查询常用活动详情失败：id{%d}不存在", id));
        }
        return commonActivityManage;
    }

    /**
     * 根据条件分页查询常用活动列表
     * @param channels 渠道IDs
     * @param commonName 常用活动名称
     * @param commonType 常用活动类型
     * @param status 状态
     * @param startAt 开始时间
     * @param expiredAt 结束时间
     * @return 活动列表
     */
    public Paging<CommonActivityManage> paging(List<Long> channels, String commonName, String commonType, Integer status,
                                               String startAt, String expiredAt, Long operatorId, Integer pageNo, Integer pageSize, Integer flowingStatus) {
        List<Long> ids = new ArrayList<>(10);
        if (!CollectionUtils.isEmpty(channels)) {
            ids = commonActivityChannelBindingDao.findCommonRelationIdsByChannelIds(channels, operatorId);
            if (CollectionUtils.isEmpty(ids)) {
                return new Paging<>(0L, Collections.emptyList());
            }
        }
        PageInfo page = new PageInfo(pageNo, pageSize);
        Map<String, Object> map = new HashMap<>(16);
        map.put("ids", ids);
        map.put("commonName", commonName);
        map.put("commonType", commonType);
        map.put("status", status);
        map.put("flowingStatus", flowingStatus);
        map.put("startAt", startAt);
        map.put("expiredAt", expiredAt);
        map.put("operatorId", operatorId);
        map.put("offset", page.getOffset());
        map.put("limit", page.getLimit());
        return commonActivityManageDao.paging(map);
    }

    /**
     * 更新活动状态
     * @return
     */
    public boolean updateCommonActivityStatus(CommonActivityManage manage) {
        return commonActivityManageDao.update(manage);
    }

    /**
     * 根据条件分页查询常用活动列表
     */
    public List<CommonActivityManage> queryNormalCommonActivityManageList(List<Long> activityIds, String commonType, Long operatorId) {
        return commonActivityManageDao.queryNormalCommonActivityManageList(activityIds, commonType, operatorId);
    }

    /**
     * 根据活动类型和运营id查询出常用活动列表
     * @param commonType
     * @param operatorId
     * @return
     */
    public List<Long> queryCommonActivityManage(String commonType,Long operatorId){
        return commonActivityManageDao.queryCommonActivityManage(commonType,operatorId);
    }
}

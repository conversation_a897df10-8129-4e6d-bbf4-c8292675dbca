package io.terminus.parana.item.common.schedule.scan;

import io.terminus.parana.item.common.schedule.scan.impl.AbstractImpulse;
import redis.clients.jedis.Jedis;
import redis.clients.util.Pool;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-09-02
 */
public class RedisBasedTagInjectImpulse extends AbstractImpulse {

    public RedisBasedTagInjectImpulse(String redisKey, Pool<Jedis> jedisPool) {
        super(200);

        Long length = 0L;
        try (Jedis jedis = jedisPool.getResource()) {
            length = jedis.llen(redisKey);
        }

        super.init(0, length - 1);
    }
}

package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.model.Paging;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.inventory.api.bean.response.info.inventory.InventoryEntityResponseInfo;
import io.terminus.parana.ipm.api.bean.request.inventory.InventoryQueryByEntityAndVendorIdRequest;
import io.terminus.parana.ipm.api.facade.IpmInventoryReadFacade;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.export.dto.SkuListExcelTemplate;
import io.terminus.parana.item.export.param.SkuSearchParam;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.item.api.bean.request.sku.SkuQueryByIdRequest;
import io.terminus.parana.item.item.api.bean.response.sku.SkuAttributeInfo;
import io.terminus.parana.item.item.api.facade.SkuReadFacade;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.relation.model.BaseItem;
import io.terminus.parana.item.search.docobject.SkuDO;
import io.terminus.parana.item.search.facade.SkuSearchFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class SkuListExcelExportOssStrategyImpl implements ExcelExportStrategy {
    @Autowired
    private ExcelExportHelper excelExportHelper;

    @Autowired
    private ThirdPartyRegistry registry;

    @Autowired
    private ItemReadDomainService itemReadDomainService;


    @Override
    public String getType() {
        return ExcelExportType.VENDOR_SKU_LIST_EXPORT.getReportType();
    }

    @Autowired
    private IpmInventoryReadFacade ipmInventoryReadFacade;
    @Autowired
    private SkuSearchFacade skuSearchFacade;
    @Qualifier("skuReadFacadeImpl")
    @Autowired
    private SkuReadFacade skuReadFacade;


    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        SkuSearchParam skuSearchRequest = JSON.parseObject(requestJson, SkuSearchParam.class);
        log.info("item.export,skuSearchRequest:{}", skuSearchRequest);
        //组装完的表格数据
       // List<ItemSearchShopExcelTemplate> excelDataList = excelDataList(skuSearchRequest);
        List<SkuListExcelTemplate> excelDataList = excelDataList(skuSearchRequest,skuSearchRequest.getShopId());
        log.info("组装数据结果集：" + excelDataList);
        log.info("item.export,导出结果条数：" + excelDataList.size());
        Long endTime = System.currentTimeMillis();
        log.info("item.export.data.build,time:{}", endTime - beginTime);

        String filePath = null;
        InputStream is = null;
        try {
            ByteArrayOutputStream outputStream = null;
            if (excelDataList.isEmpty()) {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(SkuListExcelTemplate.class);
            } else {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(excelDataList, SkuListExcelTemplate.class);

            }
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            log.info("filePath：{}", filePath);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("item.export, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("item.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("item.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }


    public List<SkuListExcelTemplate> excelDataList(SkuSearchParam param, String vendorId) {
        param.setShopId(vendorId);
        List<SkuListExcelTemplate> resultList = new ArrayList<>();
        int operatorIdPageNo = 1;
        int operatorIdPageSize = 200;
        while (true) {
            param.setPageNo(operatorIdPageNo);
            param.setPageSize(operatorIdPageSize);
            Paging<SkuDO> paging = search(param);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(paging.getData())) {
                break;
            }
            log.info("operatorIdPageNo:"+operatorIdPageNo+","+paging.getData().size());
            List<SkuDO> list = paging.getData();
            // 库存信息
            Map<Long, InventoryEntityResponseInfo> inventoryMap = getVendorInventory(list, Long.valueOf(vendorId));
            log.info("inventoryMap :"+inventoryMap);

            Set<Long> skuIdSet = AssembleDataUtils.list2set(list, SkuDO::getId);
            SkuQueryByIdRequest request = new SkuQueryByIdRequest();
            request.setIdSet(skuIdSet);
            request.setTenantId(1);

            Set<Long> itemIds = list.stream().map(SkuDO::getItemId).collect(Collectors.toSet());
            Map<Long, Item> itemMap = Maps.newHashMap();
            List<Item> itemList = itemReadDomainService.findByIdSet(itemIds, 1, null, null);
            if(CollectionUtil.isNotEmpty(itemList)){
                itemMap = itemList.stream().collect(Collectors.toMap(BaseItem::getId, Function.identity(), (k1, k2) -> k1));
            }
            // 模型组装
            List<SkuListExcelTemplate> models = Lists.newArrayListWithCapacity(list.size());
            for (SkuDO skuDO : list) {
                SkuListExcelTemplate skuListExcelTemplate = converter(skuDO);
                log.info("skuDO.getId():"+skuDO.getId());
                InventoryEntityResponseInfo info = inventoryMap.get(skuDO.getId());
                if (null != info) {
                    skuListExcelTemplate.setRealQuantity(info.getRealQuantity());
                    skuListExcelTemplate.setSellableQuantity(info.getSellableQuantity());
                }
                if (StringUtils.isNotBlank(skuListExcelTemplate.getStatus())) {
                    // -1 正常  -2 停止销售
                    if ("-1".equals(skuListExcelTemplate.getStatus())) {
                        skuListExcelTemplate.setStatus("正常");
                    } else if ("-2".equals(skuListExcelTemplate.getStatus())) {
                        skuListExcelTemplate.setStatus("停止销售");
                    }
                }
                Item item = itemMap.get(skuDO.getItemId());
                if(item != null){
                    skuListExcelTemplate.setBrandName(item.getBrandName());
                    skuListExcelTemplate.setTaxCode(item.getTaxcode());
                    skuListExcelTemplate.setTaxName(item.getTaxname());
                    skuListExcelTemplate.setVatrate(item.getVatrate() + "");
                    skuListExcelTemplate.setZqbjurl1(item.getZqbjurl1());
                    skuListExcelTemplate.setZqbjurl2(item.getZqbjurl2());
                    skuListExcelTemplate.setZqbjurl3(item.getZqbjurl3());
                }
                models.add(skuListExcelTemplate);
            }
            resultList.addAll(models);
            operatorIdPageNo++;
        }
        return resultList;
    }
    public <T extends SkuDO> Paging<T> search(SkuSearchParam param) {
        param.setTenantId(RequestContext.getTenantId());
        handle(param);
        return Assert.take(skuSearchFacade.search(param));
    }
    public void handle(SkuSearchParam param) {
        // 兼容参数处理
        if (null != param.getLowPriceYuan()) {
            param.setLowPrice(BigDecimal.valueOf(param.getLowPriceYuan()).multiply(BigDecimal.valueOf(100)).longValue());
        }
        if (null != param.getHighPriceYuan()) {
            param.setHighPrice(BigDecimal.valueOf(param.getHighPriceYuan()).multiply(BigDecimal.valueOf(100)).longValue());
        }
    }
    public Map<Long, InventoryEntityResponseInfo> getVendorInventory(List<SkuDO> models, Long vendorId) {
        InventoryQueryByEntityAndVendorIdRequest req = new InventoryQueryByEntityAndVendorIdRequest();
        req.setVendorId(vendorId);
        req.setSkuIdSet(AssembleDataUtils.list2set(models, SkuDO::getId));
        List<InventoryEntityResponseInfo> list = Assert.take(ipmInventoryReadFacade.queryByEntityAndVendorId(req));

        if (!CollectionUtils.isEmpty(list)) {
            return list.stream().collect(Collectors.toMap(p -> Long.valueOf(p.getEntityId()), p -> p));
        } else {
            return Collections.emptyMap();
        }
    }


    private SkuListExcelTemplate converter(SkuDO model){
        SkuListExcelTemplate skuListExcelTemplate = new SkuListExcelTemplate();
        skuListExcelTemplate.setItemId(model.getItemId());
        skuListExcelTemplate.setName(model.getName());
        skuListExcelTemplate.setSkuId(model.getId());
        skuListExcelTemplate.setSkuCode(model.getSkuCode());
        List<SkuAttributeInfo> attributesList = model.getAttributes();
        if(CollectionUtil.isNotEmpty(attributesList)){
           String key =  attributesList.get(0).getAttrKey();
           String value  =  attributesList.get(0).getAttrVal();
            skuListExcelTemplate.setAttrVal(key+":"+value);
        }else{
            skuListExcelTemplate.setAttrVal("");
        }
        if(null!=model.getShopName()){
            skuListExcelTemplate.setShopName(model.getShopName());
        }else{
            skuListExcelTemplate.setShopName("");
        }
        //skuListExcelTemplate.setStatus(String.valueOf(model.getStatus()));
        if(null!=model.getStatus()){
            skuListExcelTemplate.setStatus(String.valueOf(model.getStatus()));
        }else{
            skuListExcelTemplate.setStatus(null);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if(null!=model.getUpdatedAt()){
            skuListExcelTemplate.setUpdatedAt(sdf.format(model.getUpdatedAt()));
        }
//        skuListExcelTemplate.setUpdatedBy("");
        return skuListExcelTemplate;

    }
}

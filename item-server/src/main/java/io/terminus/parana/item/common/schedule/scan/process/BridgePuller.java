package io.terminus.parana.item.common.schedule.scan.process;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-29
 */
@FunctionalInterface
public interface BridgePuller<T> {

    /**
     * 从桥接区拉取数据<br>
     * <p>
     * 考虑到性能，交换区除非任务都已经结束，否则如果未达到期望的大小，不会返回数据
     * </p>
     *
     * @param expectSize 期望的数据大小
     * @return 交换数据
     */
    BridgeExchangeData<T> pull(int expectSize);
}

package io.terminus.parana.item.common.schedule.scan.impl;


import com.google.common.base.Throwables;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.schedule.scan.process.*;
import io.terminus.parana.item.common.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-30
 */
@Slf4j
public abstract class AbstractRangedScannerThread<T> extends AbstractTaskBurst implements RangedScanner<T> {

    private BridgePusher<T> pusher;
    private Impulse impulse;
    private TaskBurstCallback threadRunCallback;
    private final WorkKind WORK_KIND = WorkKind.PUSHER;

    @Override
    public void run() {
        log.info("thread start of scan by threadId: {}", getIdentityId());

        Assert.nonNull(impulse, "impulse.is.necessary");
        Assert.nonNull(pusher, "bridge.push.is.necessary");

        RangeScanResultContainer<T> resultContainer = RangeScanResultContainer.initialize();
        ImpulseExchangeData impulseExchangeData = impulse.getNext(resultContainer.getLastProcessed());

        while (!impulseExchangeData.getDone()) {
            Long start = impulseExchangeData.getStart();
            Long end = impulseExchangeData.getEnd();

            try {
                resultContainer = process(start, end, resultContainer);
                List<T> elements = resultContainer.getElements();

                if (!CollectionUtils.isEmpty(elements)) {
                    pusher.push(elements, elements.size());
                }
            } catch (Exception e) {
                log.error("fail to execute scan, cause: {}", Throwables.getStackTraceAsString(e));

                if (threadRunCallback != null) {
                    threadRunCallback.onError(getIdentityId(), WORK_KIND, new ServiceException("scan.thread.error"));
                }
            }

            impulseExchangeData = impulse.getNext(resultContainer.getLastProcessed());
        }

        if (threadRunCallback != null) {
            threadRunCallback.onDone(getIdentityId(), WORK_KIND);
        }

        log.info("thread done of scan by threadId: {}", getIdentityId());
    }

    @Override
    public void registerBridgePusher(BridgePusher<T> pusher) {
        this.pusher = pusher;
    }

    @Override
    public void registerImpulse(Impulse impulse) {
        this.impulse = impulse;
    }

    @Override
    public void registerCallback(TaskBurstCallback callback) {
        this.threadRunCallback = callback;
    }
}

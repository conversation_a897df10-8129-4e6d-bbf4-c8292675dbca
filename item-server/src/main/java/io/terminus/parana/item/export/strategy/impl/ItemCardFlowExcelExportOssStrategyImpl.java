package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.card.api.bean.request.ParanaItemCardFlowPageRequest;
import io.terminus.parana.item.card.model.ParanaItemCardFlowModel;
import io.terminus.parana.item.card.model.ParanaItemCardModel;
import io.terminus.parana.item.card.service.ParanaItemCardFlowReadService;
import io.terminus.parana.item.card.service.ParanaItemCardReadService;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.export.dto.ItemCardFlowExcelTemplate;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ItemCardFlowExcelExportOssStrategyImpl implements ExcelExportStrategy {

    @Autowired
    private ExcelExportHelper excelExportHelper;

    @Autowired
    private ThirdPartyRegistry registry;

    @Autowired
    private ParanaItemCardFlowReadService paranaItemCardFlowReadService;

    @Autowired
    private ParanaItemCardReadService paranaItemCardReadService;

    @Override
    public String getType() {
        return ExcelExportType.ITEM_CARD_FLOW_EXPORT.getReportType();
    }


    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        ParanaItemCardFlowPageRequest request = JSON.parseObject(requestJson, ParanaItemCardFlowPageRequest.class);
        log.info("item.card.flow.export,request:{}", request);
        //组装完的表格数据
        List<ItemCardFlowExcelTemplate> excelDataList = excelDataList(request);
        log.info("组装数据结果集：" + excelDataList);
        log.info("item.card.flow.export,导出结果条数：" + excelDataList.size());
        Long endTime = System.currentTimeMillis();
        log.info("item.card.flow.export.data.build,time:{}", endTime - beginTime);

        String filePath = null;
        InputStream is = null;
        try {
            ByteArrayOutputStream outputStream = null;
            if (excelDataList.isEmpty()) {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(ItemCardFlowExcelTemplate.class);
            } else {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(excelDataList, ItemCardFlowExcelTemplate.class);
            }
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            log.info("filePath：{}", filePath);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("item.card.flow.export, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("item.card.flow.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("item.card.flow.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }

    public List<ParanaItemCardFlowModel> searchExport(ParanaItemCardFlowPageRequest request) {
        if (!ObjectUtil.isEmpty(request.getChannelId())) {
            // 渠道来查询消费记录 先查所有卡号
            List<ParanaItemCardModel> cards = paranaItemCardReadService.findCardNoByChannelId(request.getChannelId());
            if (!CollectionUtil.isEmpty(cards)) {
                request.setCardNos(cards.stream().map(ParanaItemCardModel::getCardId).collect(Collectors.toList()));
            }
        }
        List<ParanaItemCardFlowModel> dataInfoListResp = new ArrayList<>();
        int pageNo = 1;
        request.setPageNo(pageNo);
        request.setPageSize(500);
        while (true) {
            Map<String, Object> params = JSONUtil.parseObj(request);
            PageInfo pageInfo = new PageInfo(request.getPageNo(), request.getPageSize());
            Paging<ParanaItemCardFlowModel> paging = paranaItemCardFlowReadService.page(params, pageInfo.getOffset(), pageInfo.getLimit());
            if (CollectionUtils.isEmpty(paging.getData())) {
                break;
            }
            dataInfoListResp.addAll(paging.getData());
            pageNo++;
            request.setPageNo(pageNo);
        }
        return dataInfoListResp;
    }

    public List<ItemCardFlowExcelTemplate> excelDataList(ParanaItemCardFlowPageRequest request) {
        List<ParanaItemCardFlowModel> dataInfoList = searchExport(request);
        List<ItemCardFlowExcelTemplate> excelDataList = new ArrayList<>();
        for (ParanaItemCardFlowModel info : dataInfoList) {
            ItemCardFlowExcelTemplate data = new ItemCardFlowExcelTemplate();
            data.setCardId(info.getCardId());
            data.setOutCouponsId(info.getOutCouponsId());
            data.setCouponsName(info.getCouponsName());
            data.setInitPrice(new BigDecimal(info.getInitPrice()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString());
            data.setMoblie(info.getMoblie());
            data.setFlowType(info.getFlowType() == 0 ? "消费" : "退款");
            data.setPrice(new BigDecimal(info.getPrice()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString());
            data.setOrderId(ObjectUtil.isEmpty(info.getOutOrderId()) ? "" : String.valueOf(info.getOutOrderId()));
            data.setFlowTime(DateUtil.format(info.getFlowTime(), "yyyy-MM-dd HH:mm:ss"));
            excelDataList.add(data);
        }
        return excelDataList;
    }

}
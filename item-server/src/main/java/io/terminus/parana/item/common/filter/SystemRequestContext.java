package io.terminus.parana.item.common.filter;

import io.terminus.api.request.AbstractRequest;
import io.terminus.parana.item.common.agreement.SystemConfig;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-05-30
 */
public class SystemRequestContext extends AbstractRequest {
    private static final AbstractRequest INSTANCE = new SystemRequestContext();

    public SystemRequestContext() {
        setTenantId(SystemConfig.getSystemTenantId());
        setUpdatedBy(SystemConfig.getSystemUserIdentity());
    }

    public static AbstractRequest getInstance() {
        return INSTANCE;
    }
}

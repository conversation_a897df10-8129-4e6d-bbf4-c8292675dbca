package io.terminus.parana.item.common.utils;

import com.google.common.collect.Maps;
import io.terminus.parana.item.common.filter.RequestContext;

import java.util.Map;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-04-10
 */
public final class RepositoryMap {

    public static Map<String, Object> standard() {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        return appendOther(map);
    }

    public static Map<String, Object> of(String k1, Object v1) {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(3);
        map.put(k1, v1);
        return appendOther(map);
    }

    public static Map<String, Object> of(String k1, Object v1, String k2, Object v2) {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(4);
        map.put(k1, v1);
        map.put(k2, v2);
        return appendOther(map);
    }

    public static Map<String, Object> of(String k1, Object v1, String k2, Object v2, String k3, Object v3) {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(5);
        map.put(k1, v1);
        map.put(k2, v2);
        map.put(k3, v3);
        return appendOther(map);
    }

    public static Map<String, Object> of(String k1, Object v1, String k2, Object v2, String k3, Object v3, String k4, Object v4) {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(6);
        map.put(k1, v1);
        map.put(k2, v2);
        map.put(k3, v3);
        map.put(k4, v4);
        return appendOther(map);
    }

    public static Map<String, Object> appendOther(Map<String, Object> map) {
        if (map != null) {
            ObjectUtils.putIfAbsent(map, "tenantId", RequestContext::getTenantId);

            if (RequestContext.isWrite()) {
                ObjectUtils.putIfAbsent(map,"updatedBy", RequestContext::getUpdatedBy);
            }
        }

        return map;
    }
}

package io.terminus.parana.item.utils.mybatis;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.PropertyAccessorFactory;
import org.springframework.util.ObjectUtils;
import org.springframework.util.ReflectionUtils;

public class QueryWrapperUtils {
    private static final Logger log = LoggerFactory.getLogger(QueryWrapperUtils.class);
    private static List<String> ex_ = CollUtil.newArrayList("class", "pageIndex", "pageSize", "page", "sort");

    public QueryWrapperUtils() {
    }

    public static <T> List<T> list(IService service, Object object) {
        return listLogic2(service, object, service.getEntityClass());
    }

    private static <T> List<T> listLogic2(IService service, Object object, Class<T> po) {
        List<T> list = service.list(queryWrapper(object, false));
        return list;
    }

    public static <T> List<T> list(IService service, Object object, Class<T> vo) {
        List list = listLogic(service, object, vo);
        List<T> convert = DomainConverter.convert(vo, list);
        return convert;
    }

    public static <T, D> List<T> list(IService service, Object object, Class<T> vo, Functional.Callback<T> callback) {
        List list = listLogic(service, object, vo);
        if (list != null && list.size() > 0) {
            List<T> convert = DomainConverter.convert(list, vo, callback);
            return convert;
        } else {
            return Collections.emptyList();
        }
    }

    private static List listLogic(IService service, Object object, Class<?> vo) {
        QueryWrapper queryWrapper = queryWrapper(object, false);
        Field[] allFields = DataUtils.getAllFields(vo);
        List<String> collect = (List)Arrays.stream(allFields).filter((f) -> {
            TableField annotation = (TableField)f.getAnnotation(TableField.class);
            return !f.getName().equals("serialVersionUID") && (annotation == null || annotation.exist());
        }).map((p) -> {
            return GuavaUtils.humpToColumn(p.getName());
        }).collect(Collectors.toList());
        queryWrapper.select((String[])collect.toArray(new String[0]));
        List list = service.list(queryWrapper);
        return list;
    }

    public static List list(IService service, QueryWrapper queryWrapper) {
        return service.list(queryWrapper);
    }

    public static List list(IService service, LambdaQueryChainWrapper queryWrapper) {
        return service.list(queryWrapper);
    }

    public static IPage page(IService service, Object object) {
        return page(service, object, (Class)null, (Functional.Callback)null);
    }

    public static IPage page(IService service, QueryWrapper queryWrapper) {
        return page(service, queryWrapper, (Class)null, (Functional.Callback)null);
    }

    public static IPage page(IService service, LambdaQueryChainWrapper queryWrapper) {
        return page(service, queryWrapper, (Class)null, (Functional.Callback)null);
    }

    public static <T> IPage<T> page(IService service, Object object, Class<T> vo) {
        return page(service, object, vo, (Functional.Callback)null);
    }

    public static <T> IPage<T> page(IService service, Object object, Class<T> vo, Functional.Callback<T> callback) {
        Page page = new Page<>();
        Object queryWrapper;
        if (object instanceof QueryWrapper) {
            queryWrapper = (QueryWrapper)object;
        } else if (object instanceof LambdaQueryChainWrapper) {
            queryWrapper = (LambdaQueryChainWrapper)object;
        } else {
            queryWrapper = queryWrapper(object, false);
        }

        IPage<T> page1 = service.page(page, (Wrapper)queryWrapper);
        if (vo != null) {
            return callback != null ? page1.convert((o) -> {
                return DomainConverter.convert(o, vo, callback);
            }) : page1.convert((o) -> {
                return DomainConverter.convert(o, vo);
            });
        } else {
            return page1;
        }
    }

    public static QueryWrapper queryWrapper(Object object) {
        return (QueryWrapper)queryWrapper(new QueryWrapper(), object, true);
    }

    public static QueryWrapper queryWrapper(Object object, boolean logicDelete) {
        return (QueryWrapper)queryWrapper(new QueryWrapper(), object, logicDelete);
    }

    public static LambdaQueryChainWrapper queryLambdaChainWrapper(IService service, Object object) {
        return (LambdaQueryChainWrapper)queryWrapper(new LambdaQueryChainWrapper(service.getBaseMapper()), object);
    }

    private static Wrapper queryWrapper(Wrapper wrapper, Object object) {
        return queryWrapper(wrapper, object, true);
    }

    private static Wrapper queryWrapper(Wrapper wrapper, Object object, boolean logicDelete) {
        if (!ObjectUtils.isEmpty(object)) {
            Map<String, Field> fieldMap = new HashMap();
            Class<?> aClass = object.getClass();
            ReflectionUtils.doWithFields(aClass, (fieldx) -> {
                fieldMap.put(fieldx.getName(), fieldx);
            });
            BeanWrapper src = PropertyAccessorFactory.forBeanPropertyAccess(object);
            PropertyDescriptor[] pds = src.getPropertyDescriptors();
            PropertyDescriptor[] var7 = pds;
            int var8 = pds.length;

            for(int var9 = 0; var9 < var8; ++var9) {
                PropertyDescriptor pd = var7[var9];
                String name = pd.getName();
                Object srcValue = src.getPropertyValue(name);
                if (!ex_.contains(name)) {
                    Field field = (Field)fieldMap.get(name);
                    Class<?> type = field.getType();
                    if (type.isPrimitive()) {
                        Logger var10000 = log;
                        String var10001 = object.getClass().getName();
                        var10000.warn("查询条件必须为包装类型，不能用基础数据类型，请修改属性数据类型：" + var10001 + "." + name);
                    } else if (!ObjectUtils.isEmpty(srcValue)) {
                        Search search = (Search)field.getAnnotation(Search.class);
                        SearchOption option = null;
                        boolean hump = true;
                        if (search != null) {
                            String tableAlias = search.tableAlias();
                            String columnAlias = search.columnAlias();
                            hump = search.hump();
                            boolean query = search.query();
                            if (!query) {
                                continue;
                            }

                            if (StringUtils.isNotBlank(columnAlias)) {
                                name = columnAlias;
                            }

                            if (StringUtils.isNotBlank(tableAlias)) {
                                name = tableAlias + "." + name;
                            }

                            if (!SearchOption.AUTO.equals(search.option())) {
                                option = search.option();
                            }
                        }

                        if (type.isEnum()) {
                            setQuery(wrapper, SearchOption.EQ, name, hump, srcValue);
                        } else if (type.equals(String.class)) {
                            setQuery(wrapper, option != null ? option : SearchOption.CP, name, hump, srcValue);
                        } else if (Objects.equals(type, List.class)) {
                            List objects = (List)srcValue;
                            if (!SearchOption.NI.equals(option) && !SearchOption.IN.equals(option)) {
                                if (objects.size() == 2 && SearchOption.BT.equals(option)) {
                                    setQuery(wrapper, SearchOption.BT, name, hump, srcValue);
                                } else {
                                    setQuery(wrapper, SearchOption.IN, name, hump, srcValue);
                                }
                            } else {
                                setQuery(wrapper, option, name, hump, srcValue);
                            }
                        } else if (type.isArray()) {
                            Object[] objects;
                            if (!Objects.equals(type.getComponentType(), LocalDate.class) && !Objects.equals(type.getComponentType(), LocalDateTime.class) && !Objects.equals(type.getComponentType(), Date.class)) {
                                if (Objects.equals(type.getComponentType(), Integer.class) || Objects.equals(type.getComponentType(), Long.class) || Objects.equals(type.getComponentType(), BigDecimal.class)) {
                                    objects = (Object[])srcValue;
                                    if (objects.length == 2 && SearchOption.BT.equals(option)) {
                                        setQuery(wrapper, SearchOption.BT, name, hump, srcValue);
                                    } else {
                                        setQuery(wrapper, SearchOption.IN, name, hump, srcValue);
                                    }
                                }
                            } else {
                                objects = (Object[])srcValue;
                                if (objects.length == 1) {
                                    setQuery(wrapper, option != null ? option : SearchOption.EQ, name, hump, objects[0]);
                                } else if (objects.length >= 2) {
                                    setQuery(wrapper, SearchOption.BT, name, hump, srcValue);
                                }
                            }
                        } else if (!Objects.equals(type, LocalDate.class) && !Objects.equals(type, LocalDateTime.class) && !Objects.equals(type, Date.class)) {
                            if (!Objects.equals(type, Between.class)) {
                                setQuery(wrapper, option != null ? option : SearchOption.EQ, name, hump, srcValue);
                            } else {
                                Between between = (Between)srcValue;
                                if (ObjectUtils.isEmpty(search) || !StringUtils.isNotEmpty(search.start()) && !StringUtils.isNotEmpty(search.end())) {
                                    if (!ObjectUtils.isEmpty(between.getStart()) && !ObjectUtils.isEmpty(between.getEnd())) {
                                        setQuery(wrapper, SearchOption.BT, name, hump, new Object[]{between.getStart(), between.getEnd()});
                                    } else if (!ObjectUtils.isEmpty(between.getStart())) {
                                        setQuery(wrapper, SearchOption.GE, name, hump, between.getStart());
                                    } else if (!ObjectUtils.isEmpty(between.getEnd())) {
                                        setQuery(wrapper, SearchOption.LE, name, hump, between.getEnd());
                                    }
                                } else if (!ObjectUtils.isEmpty(between.getStart()) && !ObjectUtils.isEmpty(between.getEnd())) {
                                    setQuery(wrapper, SearchOption.BTP, search.start() + "," + search.end(), hump, between);
                                } else if (!ObjectUtils.isEmpty(between.getStart())) {
                                    setQuery(wrapper, SearchOption.GE, search.start(), hump, between.getStart());
                                } else if (!ObjectUtils.isEmpty(between.getEnd())) {
                                    setQuery(wrapper, SearchOption.LE, search.end(), hump, between.getEnd());
                                }
                            }
                        } else {
                            setQuery(wrapper, SearchOption.EQ, name, hump, srcValue);
                        }
                    }
                }
            }
        }

        return wrapper;
    }

    private static void setQuery(Wrapper wrapper, SearchOption op, String name, Object value) {
        setQuery(wrapper, op, name, true, value);
    }

    private static void setQuery(Wrapper wrapper, SearchOption op, String name, boolean hump, Object value) {
        if (hump) {
            name = GuavaUtils.humpToColumn(name);
        }

        if (wrapper instanceof QueryWrapper) {
            setQuery((QueryWrapper)wrapper, op, name, value);
        } else if (wrapper instanceof LambdaQueryChainWrapper) {
            setQuery((LambdaQueryChainWrapper)wrapper, op, name, value);
        }

    }

    private static void setQuery(LambdaQueryChainWrapper queryWrapper, SearchOption op, String name, Object value) {
        log.debug("" + op + "->" + name + "->" + value);
        switch (op) {
            case CP:
                queryWrapper.like(name, value);
                break;
            case EQ:
                queryWrapper.eq(name, value);
                break;
            case GE:
                queryWrapper.ge(name, value);
                break;
            case GT:
                queryWrapper.gt(name, value);
                break;
            case LE:
                queryWrapper.le(name, value);
                break;
            case NE:
                queryWrapper.ne(name, value);
                break;
            case LT:
                queryWrapper.lt(name, value);
                break;
            case IN:
                List list = (List)value;
                queryWrapper.in(name, list.toArray());
                break;
            case NI:
                List list2 = (List)value;
                queryWrapper.notIn(name, list2.toArray());
                break;
            case BT:
                Object[] objects = (Object[])value;
                queryWrapper.between(name, objects[0], objects[1]);
        }

    }

    private static void setQuery(QueryWrapper<T> queryWrapper, SearchOption op, String name, Object value) {
        log.debug("" + op + "->" + name + "->" + value);
        switch (op) {
            case CP:
                queryWrapper.like(name, value);
                break;
            case EQ:
                queryWrapper.eq(name, value);
                break;
            case GE:
                queryWrapper.ge(name, value);
                break;
            case GT:
                queryWrapper.gt(name, value);
                break;
            case LE:
                queryWrapper.le(name, value);
                break;
            case NE:
                queryWrapper.ne(name, value);
                break;
            case LT:
                queryWrapper.lt(name, value);
                break;
            case IN:
                List list = (List)value;
                queryWrapper.in(name, list.toArray());
                break;
            case NI:
                List list2 = (List)value;
                queryWrapper.notIn(name, list2.toArray());
                break;
            case BT:
                if (value instanceof List) {
                    List objects = (List)value;
                    queryWrapper.between(name, objects.get(0), objects.get(1));
                } else {
                    Object[] objects = (Object[])value;
                    queryWrapper.between(name, objects[0], objects[1]);
                }
                break;
            case BTP:
                String[] split = name.split(",");
                Between between = (Between)value;
                queryWrapper.and((wrapper) -> {
                    ((QueryWrapper)((QueryWrapper)wrapper.between(split[0], between.getStart(), between.getEnd())).or()).between(split[1], between.getStart(), between.getEnd());
                });
        }

    }
}


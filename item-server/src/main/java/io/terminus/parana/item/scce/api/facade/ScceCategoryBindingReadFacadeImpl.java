package io.terminus.parana.item.scce.api.facade;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Throwables;
import com.google.gson.JsonObject;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.attribute.manager.CategoryAttributeManager;
import io.terminus.parana.item.attribute.model.CategoryAttributeBinding;
import io.terminus.parana.item.attribute.model.CategoryAttributeGroup;
import io.terminus.parana.item.attribute.service.CategoryAttributeReadDomainService;
import io.terminus.parana.item.attribute.service.CategoryAttributeWriteDomainService;
import io.terminus.parana.item.category.model.CategoryBinding;
import io.terminus.parana.item.category.model.OperatorCategory;
import io.terminus.parana.item.category.model.OperatorCategoryBinding;
import io.terminus.parana.item.category.model.SaleAttributeApprovalModel;
import io.terminus.parana.item.category.service.OperatorCategoryReadDomainService;
import io.terminus.parana.item.category.service.OperatorCategoryWriteDomainService;
import io.terminus.parana.item.category.service.SaleAttributeApprovalReadService;
import io.terminus.parana.item.category.service.SaleAttributeApprovalWriteService;
import io.terminus.parana.item.choicelot.api.facade.ChoiceLotLibItemReadFacade;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibItemModel;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibItemReadService;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibItemWriteService;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibWriteDomainService;
import io.terminus.parana.item.common.base.JsonSupport;
import io.terminus.parana.item.common.utils.CollectionObjectUtil;
import io.terminus.parana.item.item.manager.ItemManager;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.item.service.ItemWriteDomainService;
import io.terminus.parana.item.scce.api.bean.request.ScceCategoryBindingQueryRequest;
import io.terminus.parana.item.scce.api.bean.response.ScceCategoryBindingInfoResponse;
import io.terminus.parana.item.scce.api.converter.ScceCategoryBindingApiConverter;
import io.terminus.parana.item.scce.model.ScceCategoryBindingModel;
import io.terminus.parana.item.scce.service.ScceCategoryBindingReadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class ScceCategoryBindingReadFacadeImpl implements ScceCategoryBindingReadFacade {

	private final ScceCategoryBindingReadService scceCategoryBindingReadService;
	private final ScceCategoryBindingApiConverter scceCategoryBindingApiConverter;
	private final OperatorCategoryReadDomainService operatorCategoryReadDomainService;
	private final OperatorCategoryWriteDomainService operatorCategoryWriteDomainService;
	private final CategoryAttributeReadDomainService categoryAttributeReadDomainService;
	private final CategoryAttributeWriteDomainService categoryAttributeWriteDomainService;
	private final CategoryAttributeManager categoryAttributeManager;
	private final ItemReadDomainService itemReadDomainService;
	private final ItemWriteDomainService itemWriteDomainService;
	private final ItemManager itemManager;
	private final SaleAttributeApprovalReadService saleAttributeApprovalReadService;
	private final SaleAttributeApprovalWriteService saleAttributeApprovalWriteService;
	private final ChoiceLotLibItemReadService choiceLotLibItemReadService;
	private final ChoiceLotLibItemWriteService choiceLotLibItemWriteService;

	@Override
	public Response<ScceCategoryBindingInfoResponse> view(ScceCategoryBindingQueryRequest request) {
		ScceCategoryBindingModel model = scceCategoryBindingReadService.view(scceCategoryBindingApiConverter.get(request));
		if(model == null){
			model = new ScceCategoryBindingModel();
		}
		return Response.ok(scceCategoryBindingApiConverter.model2InfoResponse(model));
	}

	@Override
	public Response<List<ScceCategoryBindingInfoResponse>> list(ScceCategoryBindingQueryRequest request) {
		List<ScceCategoryBindingModel> modelList = scceCategoryBindingReadService.list(scceCategoryBindingApiConverter.get(request));
		if(modelList == null){
			modelList = new ArrayList<ScceCategoryBindingModel>();
		}
		return Response.ok(scceCategoryBindingApiConverter.modelList2InfoResponseList(modelList));
	}

    @Override
    public void category() {
		List<ScceCategoryBindingModel> modelList = scceCategoryBindingReadService.list(new ScceCategoryBindingModel());
		Map<Long, ScceCategoryBindingModel> longScceCategoryBindingModelMap = CollectionObjectUtil.toMap(modelList, ScceCategoryBindingModel::getCategoryId3);
		//修改运营类目绑定关系
		List<OperatorCategoryBinding> list = operatorCategoryReadDomainService.findByAll();
		for (OperatorCategoryBinding operatorCategoryBinding : list) {
			ScceCategoryBindingModel scceCategoryBindingModel = longScceCategoryBindingModelMap.get(operatorCategoryBinding.getBackCategoryId());
			if(scceCategoryBindingModel == null){
				continue;
			}
			operatorCategoryBinding.setBackCategoryId(scceCategoryBindingModel.getScceCategoryId3());
			operatorCategoryWriteDomainService.updateBind(operatorCategoryBinding);
		}
//		//修改前台类目绑定关系
		List<CategoryBinding> byCategoryBindAll = operatorCategoryReadDomainService.findByCategoryBindAll();
		for (CategoryBinding categoryBinding : byCategoryBindAll) {
			ScceCategoryBindingModel scceCategoryBindingModel = longScceCategoryBindingModelMap.get(categoryBinding.getBackCategoryId());
			if(scceCategoryBindingModel == null){
				continue;
			}
			categoryBinding.setBackCategoryId(scceCategoryBindingModel.getScceCategoryId3());
			operatorCategoryWriteDomainService.updateCategoryBind(categoryBinding);
		}
//		//修改类目属性绑定表
		List<CategoryAttributeBinding> categoryAttributeBindings = categoryAttributeReadDomainService.listAll();
		for (CategoryAttributeBinding categoryAttributeBinding : categoryAttributeBindings) {
			for (ScceCategoryBindingModel scceCategoryBindingModel : modelList) {
				if(categoryAttributeBinding.getCategoryId().equals(scceCategoryBindingModel.getCategoryId())){
					categoryAttributeBinding.setCategoryId(scceCategoryBindingModel.getScceCategoryId());
				}else if(categoryAttributeBinding.getCategoryId().equals(scceCategoryBindingModel.getCategoryId2())){
					categoryAttributeBinding.setCategoryId(scceCategoryBindingModel.getScceCategoryId2());
				}else if(categoryAttributeBinding.getCategoryId().equals(scceCategoryBindingModel.getCategoryId3())){
					categoryAttributeBinding.setCategoryId(scceCategoryBindingModel.getScceCategoryId3());
				}
			}
			categoryAttributeManager.update(categoryAttributeBinding);

		}
		SaleAttributeApprovalModel saleAttributeApprovalModel = new SaleAttributeApprovalModel();
		List<SaleAttributeApprovalModel> models = saleAttributeApprovalReadService.list(saleAttributeApprovalModel);
		for (SaleAttributeApprovalModel model : models) {
			ScceCategoryBindingModel scceCategoryBindingModel = longScceCategoryBindingModelMap.get(Long.valueOf(model.getClassification()));
			if(scceCategoryBindingModel == null){
				continue;
			}
			model.setClassification(String.valueOf(scceCategoryBindingModel.getScceCategoryId3()));
			saleAttributeApprovalWriteService.updateSaleAttributeApprovalModel(model);
		}
		//修改商品类目数据
		Integer pageNo = 0;
		while (true){
			PageInfo pageInfo = new PageInfo(pageNo, 1000);
			Paging<Item> page = itemReadDomainService.page(new HashMap<>(), pageInfo.getOffset(), pageInfo.getLimit());
			if (page.getData().size() == 0){
				break;
			}
			List<Item> data = page.getData();
			for (Item datum : data) {
				ScceCategoryBindingModel scceCategoryBindingModel = longScceCategoryBindingModelMap.get(datum.getCategoryId());
				if (scceCategoryBindingModel == null){
					continue;
				}
				log.info("类目信息:{}",scceCategoryBindingModel);
				datum.setCategoryId(scceCategoryBindingModel.getScceCategoryId3());
				String extraJson = datum.getExtraJson();
				if(StringUtils.isBlank(extraJson)){
					continue;
				}
				Map<String,String> map = JSON.parseObject(extraJson, Map.class);
				List<Long> asList = Arrays.asList(scceCategoryBindingModel.getScceCategoryId(), scceCategoryBindingModel.getScceCategoryId2(), scceCategoryBindingModel.getScceCategoryId3());
				log.info("星链类目:{}",asList);
				map.put("categoryList",asList.toString());
				try {
					datum.setExtraJson(JsonSupport.JSON.objectMapper.writeValueAsString(map));
				} catch (JsonProcessingException e) {
					log.error(Throwables.getStackTraceAsString(e));
				}
				itemWriteDomainService.updateItemCategoryId(datum);
			}
			pageNo++;
		}

		List<ChoiceLotLibItemModel> choiceLotLibItemModels = choiceLotLibItemReadService.listAll();
		choiceLotLibItemWriteService.updateBatch(choiceLotLibItemModels);
	}



}

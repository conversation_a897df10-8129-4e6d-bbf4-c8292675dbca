package io.terminus.parana.item.task.api.facade;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import io.terminus.parana.item.task.api.bean.TaskExecutionControlRequest;
import io.terminus.parana.item.task.service.TaskExecutionControlWriteDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ItemTaskExecutionControlWriteFacadeImpl implements ItemTaskExecutionControlWriteFacade {

    private final TaskExecutionControlWriteDomainService taskExecutionControlWriteDomainService;

    @Override
    public Response<Boolean> get(TaskExecutionControlRequest request) {
        log.info("TaskExecutionControlRequest:::::::{}", JSON.toJSONString(request));
        return Response.ok(taskExecutionControlWriteDomainService.get(request.getCenterName(), request.getTaskName(), request.getSource(),request.getJobTimeOut()));
    }

    @Override
    public Response<Boolean> release(TaskExecutionControlRequest request) {
        log.info("TaskExecutionControlRequest:::::::{}", JSON.toJSONString(request));
        return Response.ok(taskExecutionControlWriteDomainService.release(request.getCenterName(), request.getTaskName(), request.getSource()));
    }
}

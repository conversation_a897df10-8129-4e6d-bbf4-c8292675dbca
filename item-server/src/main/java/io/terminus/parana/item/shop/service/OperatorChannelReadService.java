package io.terminus.parana.item.shop.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.poi.api.bean.request.QueryOperatorChannelRequest;
import io.terminus.parana.item.poi.api.bean.response.QueryOperatorChannelResponse;
import io.terminus.parana.item.shop.model.OperatorChannel;
import io.terminus.parana.item.shop.repository.OperatorChannelDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 渠道
 *
 * <AUTHOR>
 * @since 2018-08-15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperatorChannelReadService extends AbsServiceBase {

    private final OperatorChannelDao operatorChannelDao;

    /**
     * 查询渠道类型
     * @return
     */
    public List<Map> findChannelTypes() {
        return operatorChannelDao.findChannelTypes();
    }

    /**
     * 查询渠道城市
     * @return
     */
    public List<Map> findCities(){
        return operatorChannelDao.findCities();
    }

    /**
     * 分页查询
     * @param request
     * @return
     */
    public Paging<QueryOperatorChannelResponse> paging(QueryOperatorChannelRequest request) {
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> criteria = objectMapper.convertValue(request, Map.class);
//        criteria.put("offset", request.getOffset());
//        criteria.put("limit", request.getLimit());
        log.info("OperatorChannelReadService paging param {}", criteria);
        Paging<OperatorChannel> paging = operatorChannelDao.paging(criteria);
        Paging<QueryOperatorChannelResponse> pagingResp = new Paging<>();
        if (paging.getTotal() == 0L) {
            pagingResp.setTotal(paging.getTotal());
            pagingResp.setData(new ArrayList<>());
            return pagingResp;
        }
        pagingResp.setTotal(paging.getTotal());
        pagingResp.setData(convertToOperatorChannelResponse(paging.getData()));
        return pagingResp;
    }

    private List<QueryOperatorChannelResponse> convertToOperatorChannelResponse(List<OperatorChannel> operatorChannelList) {
        List<QueryOperatorChannelResponse> responses = new ArrayList<>();
        if(null != operatorChannelList && !operatorChannelList.isEmpty()) {
            operatorChannelList.forEach(operatorChannel ->  {
                QueryOperatorChannelResponse response = new QueryOperatorChannelResponse();
                BeanUtils.copyProperties(operatorChannel, response);
                responses.add(response);
            });
        }
        return responses;
    }

    public Long count(OperatorChannel operatorChannel){
        Map<String, Object> param = new HashMap();
        param.put("id", operatorChannel.getId());
        param.put("channelType", operatorChannel.getChannelType());
        param.put("operatorId", operatorChannel.getOperatorId());
        param.put("channelName", operatorChannel.getChannelName());
        param.put("channelIntroduction", operatorChannel.getChannelIntroduction());
        param.put("mainImage", operatorChannel.getMainImage());
        param.put("entryFee", operatorChannel.getEntryFee());
        param.put("registeredShopNum", operatorChannel.getRegisteredShopNum());
        param.put("serviceDetail", operatorChannel.getServiceDetail());
        param.put("city", operatorChannel.getCity());
        param.put("cityId", operatorChannel.getCityId());
        param.put("createdAt", operatorChannel.getCreatedAt());
        param.put("updatedAt", operatorChannel.getUpdatedAt());
        return operatorChannelDao.count(param);
    }

    public OperatorChannel findById(Long id){
        return operatorChannelDao.findById(id);
    }

    public Paging<OperatorChannel> paging(Map<String, Object> params) {
        try {
            Integer pageNo = (Integer) params.get("pageNo");
            Integer pageSize = (Integer) params.get("pageSize");
            PageInfo page = new PageInfo(pageNo, pageSize);
            Paging<OperatorChannel> paging = operatorChannelDao.paging(page.getOffset(), page.getLimit(), params);

            return paging;
        } catch (Exception e) {
            log.error("paging operator channel failed, params={}, cause:{}",
                    params, Throwables.getStackTraceAsString(e));
            throw new ServiceException("paging.operator.channel.failed");
        }
    }

}

package io.terminus.parana.item.common.activity.service;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.activity.model.CommonActivityGoodsBinding;
import io.terminus.parana.item.common.activity.repository.CommonActivityChannelBindingDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommonActivityChannelBindingService {

    private final CommonActivityChannelBindingDao commonActivityChannelBindingDao;

    /**
     * 根据常用活动ID查询所关联渠道IDs
     * @param commonRelationId 常用活动ID
     * @param operatorId 区域运营ID
     * @return 所关联渠道IDs
     */
    public List<Long> findChannelIdsByCommonRelationId(Long commonRelationId, Long operatorId) {
        try {
            List<Long> channelIds = commonActivityChannelBindingDao.findChannelIdsByCommonRelationId(commonRelationId, operatorId);
            return CollectionUtils.isEmpty(channelIds) ? Collections.emptyList() : channelIds;
        } catch (Exception e) {
            log.error("[CommonActivityChannelBindingService] findChannelIdsByCommonRelationIds fail, " +
                    "commonRelationId:{} operatorId:{} cause:{}", commonRelationId, operatorId, e);
            throw new ServiceException("find.common.relation.channel.ids.failed");
        }
    }

    /**
     * 根据渠道IDs查询所关联常用活动IDs
     * @param channelIds 渠道IDs
     * @param operatorId 区域运营ID
     * @return 所关联常用活动IDs
     */
    public List<Long> findCommonRelationIdsByChannelIds(List<Long> channelIds, Long operatorId) {
        return commonActivityChannelBindingDao.findCommonRelationIdsByChannelIds(channelIds, operatorId);
    }

    /**
     * 查询已关联的渠道IDs
     * @param operatorId 区域运营ID
     * @return 渠道IDs
     */
    public List<Long> findCommonChannelList(Long operatorId,List<Long> commonRelationIds) {
        try {
            return commonActivityChannelBindingDao.findCommonChannelList(operatorId,commonRelationIds);
        } catch (Exception e) {
            log.error("[CommonActivityChannelBindingService] findCommonChannelList fail, operatorId:{} cause:{}", operatorId, e);
            throw new ServiceException("find.common.relation.channel.ids.failed");
        }
    }
}

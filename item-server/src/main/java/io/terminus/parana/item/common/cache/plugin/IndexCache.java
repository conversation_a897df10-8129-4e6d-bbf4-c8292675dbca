package io.terminus.parana.item.common.cache.plugin;

import io.terminus.parana.item.common.utils.AssembleDataUtils;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Jedis;
import redis.clients.util.Pool;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 索引缓存
 * <p>
 * 索引缓存旨在维护key到结果的一对多映射，用于配合{@link io.terminus.parana.item.common.cache.AbstractMultiCache}实现级联缓存
 * 的自动记录映射，便于缓存清除时的自动化级联缓存清除。
 * </p>
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-15
 */
public interface IndexCache<TI, TR> {

    /**
     * 生成索引的缓存key
     *
     * @param parent 父对象id
     * @return 缓存key
     */
    String generateIndexCacheKey(TI parent);

    /**
     * 添加成员
     *
     * @param parent 父对象id
     * @param member 成员id
     */
    default void addMember(TI parent, TR member) {
        if (member == null) {
            throw new IllegalArgumentException("成员对象不可为空");
        }

        if (member instanceof Collection || member instanceof Map) {
            throw new RuntimeException("不支持成员对象为集合或者Map对象");
        }

        String key = generateIndexCacheKey(parent);
        try (Jedis jedis = getJedisPool().getResource()) {
            jedis.sadd(key, member.toString());
            jedis.expire(key, getCacheTimeout());
        }
    }

    /**
     * 添加成员集合
     *
     * @param parent    父对象id
     * @param memberSet 成员集合
     */
    default void addMember(TI parent, Set<TR> memberSet) {
        if (CollectionUtils.isEmpty(memberSet)) {
            throw new IllegalArgumentException("成员对象集合不可为空");
        }

        // 集合中的TR类型判断成本较高，不处理了。但是聚合使用集合或者Map!
        String[] memberArray = AssembleDataUtils.set2set(memberSet, Objects::toString).toArray(new String[memberSet.size()]);

        String key = generateIndexCacheKey(parent);
        try (Jedis jedis = getJedisPool().getResource()) {
            jedis.sadd(key, memberArray);
            jedis.expire(key, getCacheTimeout());
        }
    }

    /**
     * 获取所有成员
     *
     * @param parent 父对象id
     * @return 成员id集合
     */
    default Set<TR> getChildren(TI parent) {
        try (Jedis jedis = getJedisPool().getResource()) {
            return transferChildrenType(jedis.smembers(generateIndexCacheKey(parent)));
        }
    }

    /**
     * 移除索引缓存
     *
     * @param parent 父对象id
     */
    default void releaseIndexCache(TI parent) {
        try (Jedis jedis = getJedisPool().getResource()) {
            jedis.del(generateIndexCacheKey(parent));
        }
    }

    /**
     * 获取缓存的超时时间
     * <p>
     * 缓存时间必须大于parent对象的缓存时间，默认时间为1小时。
     * </p>
     *
     * @return 超时时间
     */
    default Integer getCacheTimeout() {
        return 60 * 60;
    }

    /**
     * 转换成员类型
     *
     * @param cacheResult 索引缓存结果
     * @return 成员id集合
     */
    Set<TR> transferChildrenType(Set<String> cacheResult);

    /**
     * 获取redis连接池实例
     *
     * @return redis连接池
     */
    Pool<Jedis> getJedisPool();
}

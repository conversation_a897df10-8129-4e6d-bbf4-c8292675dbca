package io.terminus.parana.item.shop.api.facade;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.common.converter.GeneralConverter;
import io.terminus.parana.item.poi.api.bean.request.QueryOperatorChannelRequest;
import io.terminus.parana.item.poi.api.bean.response.QueryOperatorChannelResponse;
import io.terminus.parana.item.shop.api.bean.request.OperatorChannelQueryRequest;
import io.terminus.parana.item.shop.api.bean.response.OperatorChannelInfo;
import io.terminus.parana.item.shop.api.converter.OperatorChannelApiConverter;
import io.terminus.parana.item.shop.model.OperatorChannel;
import io.terminus.parana.item.shop.service.OperatorChannelReadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-02-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperatorChannelReadFacadeImpl implements OperatorChannelReadFacade {

    private final OperatorChannelReadService operatorChannelReadService;

    private final OperatorChannelApiConverter operatorChannelApiConverter;

    /**
     * 查询渠道类型
     * @return
     */
    @Override
    public Response<List<Map>> findChannelTypes() {
        return Response.ok(operatorChannelReadService.findChannelTypes());
    }

    /**
     * 查询渠道城市
     * @return
     */
    @Override
    public Response<List<Map>> findCities(){
        return Response.ok(operatorChannelReadService.findCities());
    }

    /**
     * 查询渠道服务
     * @return
     */
    @Override
    public Response<Paging<QueryOperatorChannelResponse>> paging(QueryOperatorChannelRequest bo) {
        log.info("OperatorChannelReadFacade paging param {} ", bo);
        return Response.ok(operatorChannelReadService.paging(bo));
    }

    /**
     * 查询渠道配置详情
     * @param id
     * @return
     */
    @Override
    public Response<OperatorChannelInfo> findDetailById(Long id) {
        OperatorChannel operatorChannel = operatorChannelReadService.findById(id);
        return Response.ok(operatorChannelApiConverter.domain2info(operatorChannel));
    }

    /**
     * Admin后台分页查询配置渠道
     * @param request
     * @return
     */
    @Override
    public Response<Paging<OperatorChannelInfo>> findAdminOperatorChannelPage(OperatorChannelQueryRequest request) {
        Map<String, Object> param = new HashMap<>();
        param.put("channelName", request.getChannelName());
        param.put("channelType", request.getChannelType());
        param.put("cityId", request.getCityId());
        param.put("pageNo", request.getPageNo());
        param.put("pageSize", request.getPageSize());
        Paging<OperatorChannel> paging = operatorChannelReadService.paging(param);
        return Response.ok(GeneralConverter.batchConvert(paging, operatorChannelApiConverter::domain2info));
    }

}
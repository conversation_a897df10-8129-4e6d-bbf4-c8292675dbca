package io.terminus.parana.item.export.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.area.enums.AreaItemStatus;
import io.terminus.parana.item.area.enums.ItemAuditType;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.model.ItemAudit;
import io.terminus.parana.item.area.repository.AreaItemDao;
import io.terminus.parana.item.area.repository.AreaSkuDao;
import io.terminus.parana.item.brand.model.Brand;
import io.terminus.parana.item.brand.repository.BrandDAO;
import io.terminus.parana.item.choicelot.model.DistributorItemLibModel;
import io.terminus.parana.item.choicelot.repository.DistributorItemLibDao;
import io.terminus.parana.item.common.filter.RequestContext;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.delivery.model.DeliveryFeeTemplate;
import io.terminus.parana.item.delivery.repository.DeliveryFeeTemplateDao;
import io.terminus.parana.item.item.cache.CacheItemById;
import io.terminus.parana.item.item.cache.CacheItemDetailById;
import io.terminus.parana.item.item.cache.CacheSkuById;
import io.terminus.parana.item.item.cache.CacheSkuIdByItemId;
import io.terminus.parana.item.item.enums.ItemAuditStatusEnum;
import io.terminus.parana.item.item.enums.YYTItemConstant;
import io.terminus.parana.item.item.manager.ItemManager;
import io.terminus.parana.item.item.model.*;
import io.terminus.parana.item.item.repository.ItemDao;
import io.terminus.parana.item.item.repository.ItemDetailDao;
import io.terminus.parana.item.item.repository.SkuDao;
import io.terminus.parana.item.partnership.repository.VendorPartnershipDao;
import io.terminus.parana.item.plugin.third.api.misc.api.ExcelReportWriteApi;
import io.terminus.parana.item.relation.model.BaseSku;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.repository.ShopDao;
import io.terminus.parana.item.shop.util.AliyunOssFactory;
import io.terminus.parana.item.third.param.ThirdUploadReportCreateRequest;
import io.terminus.parana.item.third.param.ThirdUploadReportDetailRequst;
import io.terminus.parana.item.util.ExcelAnnotation;
import io.terminus.parana.item.web.excel.ProcessResult;
import io.terminus.parana.misc.itemorder.api.api.bean.request.ParanaThirdMessageBean;
import io.terminus.parana.misc.itemorder.api.api.bean.request.ParanaThirdMessageCreateRequest;
import io.terminus.parana.misc.itemorder.api.api.facade.ParanaThirdMessageWriteFacade;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ItemBatchUpdateImportManager {

    @Autowired
    private ExcelReportWriteApi excelReportWriteApi;

    @Autowired
    private ItemBatchUpdateImportTranscriptProcessor importTranscriptProcessor;

    @Autowired
    private AreaItemDao areaItemDao;

    @Autowired
    private AreaSkuDao areaSkuDao;

    @Autowired
    private ItemDao itemDao;

    @Autowired
    private SkuDao skuDao;

    @Autowired
    private BrandDAO brandDAO;

    @Autowired
    private DeliveryFeeTemplateDao deliveryFeeTemplateDao;

    @Autowired
    private ItemDetailDao itemDetailDao;

    @Autowired
    private AliyunOssFactory aliyunOssFactory;

    @Autowired
    private DistributorItemLibDao distributorItemLibDao;

    @Autowired
    private ParanaThirdMessageWriteFacade paranaThirdMessageWriteFacade;

    @Autowired
    private VendorPartnershipDao vendorPartnershipDao;

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private ItemManager itemManager;

    @Autowired
    private CacheSkuIdByItemId cacheSkuIdByItemId;

    @Autowired
    private CacheItemDetailById cacheItemDetailById;

    @Autowired
    private CacheItemById cacheItemById;

    @Autowired
    private CacheSkuById cacheSkuById;
    @Data
    static class Rate{
        private String code;
        private String name;
    }

    private static Map<String,String> rateMap = null;

    static {
        //读取税收分类信息
        InputStream stream = ResourceUtil.getStream("classpath:rate.json");
        String json = IoUtil.read(stream, StandardCharsets.UTF_8);
        IoUtil.close(stream);
        rateMap = JSONArray.parseArray(json, Rate.class).stream().collect(Collectors.toMap(Rate::getCode, Rate::getName));

    }

    @SneakyThrows
    public void execute() {
        log.info("商品批量编辑导入启动初始化开始=====");
        Long startTime = System.currentTimeMillis();
        //删除超过一周的数据
        excelReportWriteApi.deleteWeekAgo("item-upload");
        int pageNo = 1;
        int pageSize = 20;
        ThirdUploadReportCreateRequest uploadRequest = new ThirdUploadReportCreateRequest();
        uploadRequest.setReportType("item_batch_update_import");
        uploadRequest.setUploadStauts(0);
        uploadRequest.setCenter("item-upload");
        uploadRequest.setPageNo(pageNo);
        uploadRequest.setPageSize(pageSize);
        List<ThirdUploadReportCreateRequest> result = excelReportWriteApi.pageRead(uploadRequest);
        log.info("ItemBatchUpdateImportExcelJob excelReportWriteApi result::{}", result.size());
        if (CollectionUtils.isNotEmpty(result)) {
            for (ThirdUploadReportCreateRequest reportInfoResponse : result) {
                try {
                    // 更新导入记录状态
                    updateReport(reportInfoResponse.getId(), 1); // 处理中

                    // 从OOS上下载文件
                    InputStream inputStream = new URL(reportInfoResponse.getFileUrl()).openStream();
                    MultipartFile multipartFile = new MockMultipartFile(reportInfoResponse.getFileName(), reportInfoResponse.getFileName() + ".xlsx", "", inputStream);
                    // 开始解析文件，所有商品信息
                    ProcessResult<ItemBatchUpdateExcelImportBo> process = importTranscriptProcessor.process(multipartFile);
                    List<ItemBatchUpdateExcelImportBo> excelData = process.getData();
                    log.info("导入商品批量编辑信息:{}", JSON.toJSONString(excelData));
                    String createdBy = reportInfoResponse.getUserId() + "";
                    JSONObject requestMap = JSON.parseObject(reportInfoResponse.getExtendJson());
                    Long shopId = requestMap.getLong("shopId");
                    String updateBy = requestMap.getString("userName");
                    //查询供应商合作的运营商id
                    List<Long> operatorIds = vendorPartnershipDao.queryOperatorByVendor(shopId);
                    Map<Long, Shop> shopMap = Maps.newHashMap();
                    if(CollectionUtil.isNotEmpty(operatorIds)){
                        List<Shop> shopList = shopDao.findByIds(operatorIds, 1);
                        if(CollectionUtil.isNotEmpty(shopList)){
                            shopMap = shopList.stream().collect(Collectors.toMap(Shop::getId, Function.identity()));
                        }
                    }
                    List<ItemBatchUpdateExcelImportBo> results = Lists.newArrayList();
                    int i =0;//行
                    for (ItemBatchUpdateExcelImportBo importBo : excelData) {
                        i++;
                        int cell = 0;
                        try {
                            if(StringUtils.isEmpty(importBo.getItemId()) || !NumberUtil.isLong(importBo.getItemId())){
                                cell = 1;
                                throw new ServiceException("商品ID不能为空或者不是数字类型！");
                            }
                            if(StringUtils.isNotEmpty(importBo.getVatrate()) && !NumberUtil.isDouble(importBo.getVatrate())){
                                cell = 7;
                                throw new ServiceException("税率填写有误，只能为0、0.06、0.09、0.11、0.13、0.16、0.17！");
                            }
                            log.info("商品导入编辑对象信息： {}",JSON.toJSONString(importBo));
                            if((StringUtils.isNotEmpty(importBo.getSkuCode()) || StringUtils.isNotEmpty(importBo.getMinQuantity())
                                    || StringUtils.isNotEmpty(importBo.getBarcode()) || StringUtils.isNotEmpty(importBo.getCustSkuCode())
                                    || StringUtils.isNotEmpty(importBo.getStatus())) && StringUtils.isEmpty(importBo.getSkuId())){
                                cell = 12;
                                throw new ServiceException("规格ID不能为空！");
                            }
                            if(StringUtils.isNotEmpty(importBo.getSkuId()) && !NumberUtil.isLong(importBo.getSkuId())){
                                cell = 12;
                                throw new ServiceException("规格ID不是数字类型！");
                            }
                            if(StringUtils.isNotEmpty(importBo.getMinQuantity()) && !NumberUtil.isLong(importBo.getMinQuantity())){
                                cell = 16;
                                throw new ServiceException("起售数量不是数字类型！");
                            }
                            if(StringUtils.isNotEmpty(importBo.getStatus())){
                                cell = 17;
                                if(!NumberUtil.isInteger(importBo.getStatus())){
                                    throw new ServiceException("是否启用不是数字类型！");
                                }
                                if(!importBo.getStatus().equals("-1") && !importBo.getStatus().equals("0")){
                                    throw new ServiceException("请填写正确的是否启用值 -1表示启用 0表示禁用！");
                                }
                            }
                        } catch (ServiceException e) {
                            importBo.setResults("失败");
                            importBo.setReason(e.getMessage());
                            insertErrorExcel(reportInfoResponse,  cell, e.getMessage(), i , e.getMessage());
                            results.add(importBo);
                            continue;
                        } catch (Exception e) {
                            importBo.setResults("失败");
                            importBo.setReason("导入失败！");
                            log.error("商品编辑导入失败 cause: {}", Throwables.getStackTraceAsString(e));
                            results.add(importBo);
                            insertErrorExcel(reportInfoResponse,  cell, e.getMessage(), i , e.getMessage());
                            continue;
                        }
                        importBo.setResults("成功");
                        importBo.setReason("");
                        results.add(importBo);
                    }
                    //批量查询商品信息
                    Set<Long> itemIds = excelData.stream().filter(f -> "成功".equals(f.getResults())).map(m -> Long.parseLong(m.getItemId())).collect(Collectors.toSet());
                    Map<Long, Item> itemMap = Maps.newHashMap();
                    Map<Long, List<AreaItem>> areaItemMap = Maps.newHashMap();
                    Map<String, AreaSku> areaSkuMap = Maps.newHashMap();
                    if(CollectionUtil.isNotEmpty(itemIds)){
                        List<Item> itemList = itemDao.findByIdSet(itemIds, 1);
                        if(CollectionUtil.isNotEmpty(itemList)){
                            itemMap = itemList.stream().collect(Collectors.toMap(Item::getId, Function.identity(), (v1, v2) -> v1));
                        }
                        List<AreaItem> areaItemList = areaItemDao.findByOperatorIdsAndItemIds(new HashSet<>(operatorIds), itemIds);
                        if(CollectionUtil.isNotEmpty(areaItemList)){
                            areaItemMap = areaItemList.stream().collect(Collectors.groupingBy(AreaItem::getItemId));
                        }
                        List<AreaSku> areaSkuList = areaSkuDao.findByOperatorIdsAndItemIds(operatorIds, new ArrayList<>(itemIds));
                        if(CollectionUtil.isNotEmpty(areaSkuList)){
                            areaSkuMap = areaSkuList.stream().collect(Collectors.toMap(m -> m.getOperatorId() + "_" + m.getSkuId(), Function.identity(), (v1, v2) -> v1));
                        }
                    }
                    log.info("商品导入编辑 "+JSON.toJSONString(areaSkuMap));
                    //批量查询规格信息
                    Set<Long> skuIds = excelData.stream().filter(f -> "成功".equals(f.getResults()) && StringUtils.isNotEmpty(f.getSkuId())).map(m -> Long.parseLong(m.getSkuId())).collect(Collectors.toSet());
                    Map<Long, Sku> skuMap = Maps.newHashMap();
                    if(CollectionUtil.isNotEmpty(skuIds)){
                        List<Sku> skuList = skuDao.findByIdSet(skuIds, 1);
                        if(CollectionUtil.isNotEmpty(skuList)){
                            skuMap = skuList.stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (v1, v2) -> v1));
                        }
                    }

                    //查询品牌信息
                    Set<String> brandNames = excelData.stream().filter(f -> "成功".equals(f.getResults()) && StringUtils.isNotEmpty(f.getBrandName())).map(ItemBatchUpdateExcelImportBo::getBrandName).collect(Collectors.toSet());
                    Map<String, Brand> brandMap = Maps.newHashMap();
                    if(CollectionUtil.isNotEmpty(brandNames)){
                        List<Brand> brandList = brandDAO.findByNames(new ArrayList<>(brandNames));
                        if(CollectionUtil.isNotEmpty(brandList)){
                            brandMap = brandList.stream().collect(Collectors.toMap(Brand::getName, Function.identity(), (v1, v2) -> v1));
                        }
                    }

                    //查询运费模板信息
                    Set<String> tempNames = excelData.stream().filter(f -> "成功".equals(f.getResults()) && StringUtils.isNotEmpty(f.getDeliveryFeeTempName())).map(ItemBatchUpdateExcelImportBo::getDeliveryFeeTempName).collect(Collectors.toSet());
                    Map<String, DeliveryFeeTemplate> tempMap = Maps.newHashMap();
                    if(CollectionUtil.isNotEmpty(tempNames)){
                        List<DeliveryFeeTemplate> tempList = deliveryFeeTemplateDao.findByShopId(shopId);
                        if(CollectionUtil.isNotEmpty(tempList)){
                            tempMap = tempList.stream().collect(Collectors.toMap(DeliveryFeeTemplate::getName, Function.identity(), (v1, v2) -> v1));
                        }
                    }



                    i =0;//行
                    for (ItemBatchUpdateExcelImportBo importBo : excelData) {
                        FullItemOperateBO dbBo = new FullItemOperateBO();
                        i++;
                        int cell = 0;
                        if("失败".equals(importBo.getResults())){
                            continue;
                        }
                        try {
                            Item item = itemMap.get(Long.parseLong(importBo.getItemId()));
                            if(item == null){
                                cell = 1;
                                throw new ServiceException("商品不存在！");
                            }
                            //是否需要审核 判断商品名称、是否可退货、是否启用是否有变更
                            boolean ifAudit = false;
                            if(StringUtils.isNotEmpty(importBo.getItemName()) && !item.getName().equals(importBo.getItemName())){
                                ifAudit = true;
                            }
                            if(StringUtils.isNotEmpty(importBo.getSupportReturn()) && !importBo.getSupportReturn().equals(item.getExtra().get("supportReturn"))){
                                ifAudit = true;
                            }
                            if(StringUtils.isNotEmpty(importBo.getStatus())){
                                Sku sku = skuMap.get(Long.parseLong(importBo.getSkuId()));
                                if(sku == null){
                                    cell = 12;
                                    throw new ServiceException("规格不存在！");
                                }
                                if(Integer.parseInt(importBo.getStatus()) != sku.getStatus()){
                                    ifAudit = true;
                                }
                            }
                            //需要审核  记录log
                            if(ifAudit){
                                ItemDetail itemDetail = itemDetailDao.findByItemId(item.getId(), item.getTenantId());
                                ItemUpdateLog updateLog = new ItemUpdateLog();
                                updateLog.setId(idGenerator.nextValue(ItemUpdateLog.class));
                                updateLog.setType(ItemAuditType.EDIT.getValue());
                                updateLog.setItemId(item.getId());
                                updateLog.setName(item.getName());
                                updateLog.setBrandId(item.getBrandId());
                                updateLog.setBrandName(item.getBrandName());
                                updateLog.setAdvertise(item.getAdvertise());
                                updateLog.setKeyword(item.getExtra().get("keyword"));
                                updateLog.setMainImage(item.getMainImage());
                                updateLog.setVideoUrl(item.getVideoUrl());
                                updateLog.setCreatedBy(RequestContext.getUpdatedBy());
                                Map<String, String> extra = Maps.newHashMap();
                                if(itemDetail != null){
                                    updateLog.setImageJson(itemDetail.getImageJson());
                                    extra.put("itemDetail", JSONUtil.toJsonStr(itemDetail));
                                }
                                List<Sku> skuList = skuDao.findByItemId(item.getId(), item.getTenantId());
                                if(CollectionUtil.isNotEmpty(skuList)){
                                    extra.put("skuList", JSONUtil.toJsonStr(skuList));
                                }
                                extra.put("salesArea", item.getSalesArea());
                                extra.put("channlInfos", "[]");
                                extra.put("taxSort", item.getTaxSort());
                                extra.put("universalName", item.getUniversalName());
                                extra.put("zqbjurl1", item.getZqbjurl1());
                                if (!ObjectUtil.isEmpty(item.getExtra()) && !ObjectUtil.isEmpty(item.getExtra().get("supportReturn"))) {
                                    extra.put("supportReturn", item.getExtra().get("supportReturn"));
                                }
                                if (item.getDeliveryFeeTempId() != null) {
                                    extra.put("deliveryFeeTempId", item.getDeliveryFeeTempId().toString());
                                }
                                if (!ObjectUtil.isEmpty(item.getIsCrossBorder())) {
                                    extra.put("isCrossBorder", String.valueOf(item.getIsCrossBorder()));
                                }
                                updateLog.setUnit(item.getUnit());
                                updateLog.setTaxcode(item.getTaxcode());
                                updateLog.setTaxname(item.getTaxname());
                                updateLog.setVatrate(item.getVatrate());
                                updateLog.setSkuAttributes(item.getSkuAttributes());
                                updateLog.setOtherAttributes(item.getOtherAttributes());
                                updateLog.setItemExtra(extra);
                                dbBo.setItemUpdateLog(updateLog);
                            }
                            if(StringUtils.isNotEmpty(importBo.getItemName())){
                                item.setName(importBo.getItemName());
                            }
                            if(StringUtils.isNotEmpty(importBo.getAdvertise())){
                                item.setAdvertise(importBo.getAdvertise());
                            }
                            if(StringUtils.isNotEmpty(importBo.getKeyword())){
                                item.getExtra().put("keyword", importBo.getKeyword());
                            }
                            if(StringUtils.isNotEmpty(importBo.getUniversalName())){
                                item.setUniversalName(importBo.getUniversalName());
                            }
                            if(StringUtils.isNotEmpty(importBo.getTaxCode())){
                                if(rateMap != null && rateMap.get(importBo.getTaxCode()) != null){
                                    item.setTaxname(rateMap.get(importBo.getTaxCode()));
                                    item.setTaxSort(rateMap.get(importBo.getTaxCode()));
                                }else{
                                    cell = 5;
                                    throw new ServiceException("税收编码未找到！");
                                }
                                item.setTaxcode(importBo.getTaxCode());
                            }
                            if(StringUtils.isNotEmpty(importBo.getVatrate())){
                                item.setVatrate(new BigDecimal(importBo.getVatrate()));
                            }
                            if(StringUtils.isNotEmpty(importBo.getBrandName())){
                                Brand brand = brandMap.get(importBo.getBrandName());
                                if(brand == null){
                                    cell = 8;
                                    throw new ServiceException("品牌不存在！");
                                }
                                item.setBrandName(importBo.getBrandName());
                                item.setBrandId(brand.getId());
                            }
                            if(StringUtils.isNotEmpty(importBo.getUnit())){
                                item.setUnit(importBo.getUnit());
                            }
                            if(StringUtils.isNotEmpty(importBo.getZqbjurl1())){
                                item.setZqbjurl1(importBo.getZqbjurl1());
                            }
                            if(StringUtils.isNotEmpty(importBo.getDeliveryFeeTempName())){
                                DeliveryFeeTemplate deliveryFeeTemplate = tempMap.get(importBo.getDeliveryFeeTempName());
                                if(deliveryFeeTemplate == null){
                                    cell = 11;
                                    throw new ServiceException("运费模板不存在！");
                                }
                                item.setDeliveryFeeTempName(importBo.getDeliveryFeeTempName());
                                item.setDeliveryFeeTempId(deliveryFeeTemplate.getId());
                            }
                            if(StringUtils.isNotEmpty(importBo.getSupportReturn())){
                                item.getExtra().put("supportReturn", importBo.getSupportReturn());
                                item.setExtra(item.getExtra());
                            }
                            item.setUpdatedBy(updateBy);
                            dbBo.setItem(item);
                            if(StringUtils.isNotEmpty(importBo.getSkuId())){
                                Sku sku = skuMap.get(Long.parseLong(importBo.getSkuId()));
                                if(sku == null){
                                    cell = 12;
                                    throw new ServiceException("规格不存在！");
                                }
                                if(!sku.getItemId().equals(item.getId())){
                                    cell = 12;
                                    throw new ServiceException("规格id不是当前商品下的规格！");
                                }
                                if(StringUtils.isNotEmpty(importBo.getSkuCode())){
                                    sku.setSkuCode(importBo.getSkuCode());
                                }
                                if(StringUtils.isNotEmpty(importBo.getMinQuantity())){
                                    sku.getExtra().put("minQuantity",importBo.getMinQuantity());
                                    sku.setExtra(sku.getExtra());
                                }
                                if(StringUtils.isNotEmpty(importBo.getBarcode())){
                                    sku.setBarcode(importBo.getBarcode());
                                }
                                if(StringUtils.isNotEmpty(importBo.getCustSkuCode())){
                                    sku.setCustSkuCode(importBo.getCustSkuCode());
                                }
                                if(StringUtils.isNotEmpty(importBo.getStatus())){
                                    sku.setStatus(Integer.parseInt(importBo.getStatus()));
                                }
                                sku.setUpdatedBy(updateBy);
                                dbBo.setToUpdateSkuList(Collections.singletonList(sku));
                            }
                            List<AreaItem> updateAreaItemList = new ArrayList<>();
                            List<AreaSku> updateAreaSkuList = new ArrayList<>();
                            List<AreaItem> areaItemList = areaItemMap.get(item.getId());
                            if(CollectionUtil.isNotEmpty(areaItemList)){
                                for (AreaItem areaItem : areaItemList) {
                                    areaItem.setName(item.getName());
                                    areaItem.setDeliveryFeeTempId(item.getDeliveryFeeTempId());
                                    areaItem.setSupportReturn(YYTItemConstant.SUPPORT_RETURN.getBoolean(item.getExtra()));
                                    if(ifAudit){
                                        if(areaItem.getStatus() == AreaItemStatus.AUDITING.getValue()){
                                            throw new ServiceException("区域商品存在审核 请联系运营审核后再修改！" + areaItem.getItemId());
                                        }
                                        areaItem.setStatus(AreaItemStatus.AUDITING.getValue());
                                    }
                                    areaItem.setUpdatedBy(updateBy);
                                    updateAreaItemList.add(areaItem);
                                    if(StringUtils.isNotEmpty(importBo.getSkuId())){
                                        log.info("商品导入编辑 "+areaItem.getOperatorId() + "_" + importBo.getSkuId());
                                        AreaSku areaSku = areaSkuMap.get(areaItem.getOperatorId() + "_" + importBo.getSkuId());
                                        if(areaSku == null){
                                            cell = 12;
                                            throw new ServiceException("区域商品规格不存在！" + areaItem.getOperatorId());
                                        }
                                        areaSku.setName(item.getName());
                                        if(StringUtils.isNotEmpty(importBo.getSkuCode())){
                                            areaSku.setSkuCode(importBo.getSkuCode());
                                        }
                                        if(StringUtils.isNotEmpty(importBo.getMinQuantity())){
                                            areaSku.setMinQuantity(Long.parseLong(importBo.getMinQuantity()));
                                        }
                                        areaSku.setStatus(areaItem.getStatus());
                                        areaSku.setUpdatedBy(updateBy);
                                        updateAreaSkuList.add(areaSku);
                                    }
                                }
                            }
                            dbBo.setUpdateAreaItemList(updateAreaItemList);
                            dbBo.setUpdateAreaSkuList(updateAreaSkuList);
                            //如果需要审核 走审核逻辑
                            if(ifAudit){
                                if(CollectionUtil.isNotEmpty(dbBo.getUpdateAreaItemList())){
                                    List<ItemAudit> itemAudits = new ArrayList<>();
                                    for (AreaItem areaItem : dbBo.getUpdateAreaItemList()) {
                                        ItemAudit itemAudit = new ItemAudit();
                                        itemAudit.setId(idGenerator.nextValue(ItemAudit.class));
                                        itemAudit.setType(ItemAuditType.EDIT.getValue());
                                        itemAudit.setItemId(item.getId());
                                        itemAudit.setCategoryId(item.getCategoryId());
                                        itemAudit.setOperatorId(areaItem.getOperatorId());
                                        Shop shop = shopMap.get(areaItem.getOperatorId());
                                        if(shop != null){
                                            itemAudit.setOperatorName(shop.getName());
                                            itemAudit.setActuallyOperatorName(shop.getName());
                                        }
                                        itemAudit.setName(item.getName());
                                        itemAudit.setVendorId(item.getShopId());
                                        itemAudit.setVendorName(item.getShopName());
                                        itemAudit.setStatus(ItemAuditStatusEnum.UNDER_REVIEW.getValue());
                                        if (1L == areaItem.getOperatorId()) {
                                            itemAudit.setStatus(ItemAuditStatusEnum.REVIEW.getValue());
                                        }
                                        itemAudit.setCreatedBy(updateBy);
                                        itemAudit.setCooperationMode(areaItem.getCooperationMode());
                                        itemAudit.setLogisticsMode(areaItem.getLogisticsMode());
                                        itemAudit.setTimeStamp(new Date().getTime());
                                        itemAudit.setBrandId(item.getBrandId());
                                        if (!ObjectUtils.isEmpty(item.getExtra())) {
                                            itemAudit.setSupportReturn(Integer.valueOf(item.getExtra().get("supportReturn")));
                                        }
                                        itemAudit.setRemarks("商品导入修改，重新合规性审核");
                                        itemAudits.add(itemAudit);
                                    }
                                    dbBo.setBaseAuditAdoptList(itemAudits);
                                }
                            }
                            boolean isOk = itemManager.batchImportUpdateItem(dbBo);
                            try {
                                cacheItemById.remove(item.getId());
                                cacheSkuIdByItemId.remove(item.getId());
                                cacheSkuById.remove(AssembleDataUtils.list2set(dbBo.getToUpdateSkuList(), BaseSku::getId));
                                cacheItemDetailById.remove(item.getId());
                            }catch (Exception e){
                                log.error("商品导入编辑 缓存删除失败 {}", Throwables.getStackTraceAsString(e));
                            }
                            //如果保存成功并且不需要走审核 消息通知渠道客户
                            if(isOk && !ifAudit){
                                List<DistributorItemLibModel> distributorItemLibList = distributorItemLibDao.findByDistributorIdAndItemIds(null, Collections.singletonList(item.getId()));
                                if(CollectionUtil.isNotEmpty(distributorItemLibList)){
                                    List<ParanaThirdMessageCreateRequest> thirdParanaThirdMessageCreateRequests = new ArrayList<>();
                                    for (DistributorItemLibModel distributorItem : distributorItemLibList) {
                                        //审核通过发送消息
                                        ParanaThirdMessageCreateRequest messageCreateRequest = new ParanaThirdMessageCreateRequest();
                                        messageCreateRequest.setAuthId(distributorItem.getDistributorId());
                                        messageCreateRequest.setCreatedBy(createdBy);
                                        messageCreateRequest.setUpdatedBy(createdBy);
                                        messageCreateRequest.setOperatorId(distributorItem.getOperatorId());
                                        messageCreateRequest.setCreatedAt(new Date());
                                        messageCreateRequest.setUpdatedAt(new Date());
                                        messageCreateRequest.setMessageType(4);
                                        ParanaThirdMessageBean thirdParanaThirdMessageBean = new ParanaThirdMessageBean();
                                        thirdParanaThirdMessageBean.setId(distributorItem.getItemId());
                                        messageCreateRequest.setBean(thirdParanaThirdMessageBean);
                                        thirdParanaThirdMessageCreateRequests.add(messageCreateRequest);
                                    }
                                    paranaThirdMessageWriteFacade.batchCreate(thirdParanaThirdMessageCreateRequests);
                                }
                            }
                        } catch (ServiceException e) {
                            importBo.setResults("失败");
                            importBo.setReason(e.getMessage());
                            insertErrorExcel(reportInfoResponse,  cell, e.getMessage(), i , e.getMessage());
                            results.add(importBo);
                            continue;
                        } catch (Exception e) {
                            importBo.setResults("失败");
                            importBo.setReason("导入失败！");
                            log.error("商品编辑导入失败 error: {}", Throwables.getStackTraceAsString(e));
                            results.add(importBo);
                            insertErrorExcel(reportInfoResponse,  cell, e.getMessage(), i , e.getMessage());
                            continue;
                        }
                        importBo.setResults("成功");
                        importBo.setReason("");
                        results.add(importBo);
                    }
                    ItemBatchUpdateExcelImportBo importBo = results.stream().filter(e -> "失败".equals(e.getResults())).findFirst().orElse(null);
                    if (importBo == null){
                        updateReport(reportInfoResponse.getId(), 2); // 处理成功
                    } else {
                        String url = exportedExcelErrorList(results, reportInfoResponse.getId());
                        updateReport(reportInfoResponse, -1, url);
                    }

                } catch (Exception e) {
                    log.error("商品导入编辑失败 error:{}", Throwables.getStackTraceAsString(e));
                    ThirdUploadReportDetailRequst thirdUploadReportDetailRequst = new ThirdUploadReportDetailRequst();
                    thirdUploadReportDetailRequst.setCol(0);
                    thirdUploadReportDetailRequst.setRow(0);
                    thirdUploadReportDetailRequst.setUploadReportId(reportInfoResponse.getId());
                    thirdUploadReportDetailRequst.setReason("导入失败系统错误");
                    thirdUploadReportDetailRequst.setSuggest("导入失败系统错误");
                    thirdUploadReportDetailRequst.setCreateAt(new Date());
                    thirdUploadReportDetailRequst.setUpdatedAt(new Date());
                    excelReportWriteApi.detailCreate(thirdUploadReportDetailRequst);
                    updateReport(reportInfoResponse.getId(),-1);

                }
            }

        }

        log.info("[ITEM JOB:卡券导入定时导入]=====处理完成，文件个数{}===== 耗时：{}ms", result.size(), System.currentTimeMillis() - startTime);
    }



    /**
     * 更新导入记录状态
     *
     * @param id
     * @param status
     */
    private void updateReport(Long id, int status) {
        // 更新导入记录状态
        ThirdUploadReportCreateRequest uploadReportUpdateRequest = new ThirdUploadReportCreateRequest();
        uploadReportUpdateRequest.setUploadStauts(status);
        uploadReportUpdateRequest.setId(id);
        uploadReportUpdateRequest.setUpdatedAt(new Date());
        excelReportWriteApi.update(uploadReportUpdateRequest);
    }

    private void updateReport(ThirdUploadReportCreateRequest reportInfoResponse, int status, String url) {
        // 更新导入记录状态
        ThirdUploadReportCreateRequest uploadReportUpdateRequest = new ThirdUploadReportCreateRequest();
        uploadReportUpdateRequest.setUploadStauts(status);
        uploadReportUpdateRequest.setId(reportInfoResponse.getId());
        uploadReportUpdateRequest.setUpdatedAt(new Date());
        Map<String, Object> map = Maps.newHashMap();
        map.put("url",url);
        uploadReportUpdateRequest.setExtendJson(JSONUtil.toJsonStr(map));
        excelReportWriteApi.update(uploadReportUpdateRequest);
    }

    private String exportedExcelErrorList(List<ItemBatchUpdateExcelImportBo> importErrorExportTemplates, Long reportId){
        try {
            if (org.apache.commons.collections.CollectionUtils.isEmpty(importErrorExportTemplates)){
                return null;
            }
            String fileName = "item_batch_update_import_results" + DateUtil.now() + ".xlsx";
            ByteArrayOutputStream bao = new ByteArrayOutputStream();
            itemDownloadTemplate(importErrorExportTemplates, ItemBatchUpdateExcelImportBo.class, bao);
            byte[] bytes = bao.toByteArray();

            return aliyunOssFactory.uploadFile(fileName, new ByteArrayInputStream(bytes));
        } catch (IllegalAccessException e) {
            log.error("exportedExcelErrorList upload error::{}",e.getMessage());
            return null;
        }
    }

    public void itemDownloadTemplate(List list, Class<?> clazz, OutputStream outputStream) throws IllegalAccessException {

        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet xssfSheet = wb.createSheet(clazz.getSimpleName());

        XSSFCellStyle titleStyle = (XSSFCellStyle) wb.createCellStyle();
        titleStyle.setLocked(true);

        XSSFCellStyle cellStyle = (XSSFCellStyle) wb.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());

        int i = 0;
        //标题
        Row titleRow = xssfSheet.createRow(i);
        for (Field field : clazz.getDeclaredFields()) {
            ExcelAnnotation excelAnnotation = field.getAnnotation(ExcelAnnotation.class);
            if (excelAnnotation != null) {
                int columnIndex = excelAnnotation.columnIndex();
                Cell cell = titleRow.createCell(columnIndex);
                cell.setCellValue(excelAnnotation.columnName());
            }
        }
        for (Object obj : list) {
            i++;
            Row row = xssfSheet.createRow(i);
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                ExcelAnnotation excelAnnotation = field.getAnnotation(ExcelAnnotation.class);
                if (excelAnnotation != null) {
                    int columnIndex = excelAnnotation.columnIndex();
                    Cell cell = row.createCell(columnIndex);
//                    log.info("rownum:::::::::::"+columnIndex);
//                    log.info("row:::::::::::"+field.getName()+":"+field.get(obj));
                    if (null != field.get(obj)) {
                        cell.setCellValue(field.get(obj).toString());
                    } else {
                        cell.setCellValue("");
                    }
                    if (excelAnnotation.isLock()) {
                        cell.setCellStyle(cellStyle);
                    }
                }
            }
        }
        try {
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("fail to download excel template:{}, cause:{}",
                    clazz.getSimpleName(), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 新增导入记录详情,生成错误记录
     *
     * @param request
     * @param col
     * @param reason
     * @param row
     * @param suggest
     */
    private void insertErrorExcel(ThirdUploadReportCreateRequest request, Integer col, String reason, Integer row, String suggest) {
        ThirdUploadReportDetailRequst thirdUploadReportDetailRequst = new ThirdUploadReportDetailRequst();
        thirdUploadReportDetailRequst.setCreateAt(new Date());
        thirdUploadReportDetailRequst.setUpdatedAt(new Date());
        thirdUploadReportDetailRequst.setUploadReportId(request.getId());
        thirdUploadReportDetailRequst.setCol(col);
        thirdUploadReportDetailRequst.setReason(reason);
        thirdUploadReportDetailRequst.setRow(row);
        thirdUploadReportDetailRequst.setSuggest(suggest);
        excelReportWriteApi.detailCreate(thirdUploadReportDetailRequst);
    }
}

package io.terminus.parana.item.scce.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.scce.api.bean.request.ScceCategoryBindingCreateRequest;
import io.terminus.parana.item.scce.api.bean.request.ScceCategoryBindingUpdateRequest;
import io.terminus.parana.item.scce.api.converter.ScceCategoryBindingApiConverter;
import io.terminus.parana.item.scce.model.ScceCategoryBindingModel;
import io.terminus.parana.item.scce.service.ScceCategoryBindingWriteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ScceCategoryBindingWriteFacadeImpl implements ScceCategoryBindingWriteFacade {

	private final ScceCategoryBindingWriteService scceCategoryBindingWriteService;
	private final ScceCategoryBindingApiConverter scceCategoryBindingApiConverter;

	@Override
	public Response<Boolean> create(ScceCategoryBindingCreateRequest request) {
		ScceCategoryBindingModel model = scceCategoryBindingApiConverter.get(request);
		Boolean isSuccess = scceCategoryBindingWriteService.create(model);
		if(isSuccess) {
			return Response.ok(isSuccess);
		}
		else {
			return Response.fail("创建失败。");
		}
	}

	@Override
	public Response<Boolean> update(ScceCategoryBindingUpdateRequest request) {
		ScceCategoryBindingModel model = scceCategoryBindingApiConverter.get(request);
		Boolean isSuccess = scceCategoryBindingWriteService.update(model);
		if(isSuccess) {
			return Response.ok(isSuccess);
		}
		else {
			return Response.fail("修改失败。");
		}
	}


}

package io.terminus.parana.item.common.activity.repository;

import io.terminus.parana.item.common.activity.model.CommonActivityManage;
import io.terminus.parana.item.common.base.AbstractMybatisDao;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class CommonActivityManageDao extends AbstractMybatisDao<CommonActivityManage> {

    /**
     * 根据该时间范围内的已存在的活动ids
     * @param commonType 活动类型
     * @param operatorId 区域运营ID
     * @param startAt 开始时间
     * @param expiredAt 截止时间
     * @return 活动ids
     */
    public List<Long> checkTimeRangeValidActive(String commonType, Long operatorId, Date startAt, Date expiredAt, Long excludeId) {
        Map<String, Object> map = new HashMap<>(8);
        map.put("startAt", startAt);
        map.put("expiredAt", expiredAt);
        map.put("commonType", commonType);
        map.put("operatorId", operatorId);
        map.put("excludeId", excludeId);
        map.put("flowingStatus", 2);
        return sqlSession.selectList(sqlId("checkTimeRangeValidActive"), map);
    }

    /**
     * 查询活动列表
     * @param commonType
     * @param operatorId
     * @return
     */
    public List<CommonActivityManage> queryNormalCommonActivityManageList(List<Long> activityIds, String commonType, Long operatorId) {
        Map<String, Object> map = new HashMap<>(8);
        map.put("commonType", commonType);
        map.put("operatorId", operatorId);
        map.put("ids", activityIds);
        map.put("status", 1);
        map.put("systemTime", new Date());
        return sqlSession.selectList(sqlId("queryCommonActivityManageList"), map);
    }

    public List<Long> queryCommonActivityManage(String commonType,Long operatorId){
        Map<String, Object> map = new HashMap<>(8);
        map.put("commonType", commonType);
        map.put("operatorId", operatorId);
        map.put("status", -2);
        return sqlSession.selectList(sqlId("queryCommonActivityManage"), map);
    }
}

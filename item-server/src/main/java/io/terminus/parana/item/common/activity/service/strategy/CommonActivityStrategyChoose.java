package io.terminus.parana.item.common.activity.service.strategy;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class CommonActivityStrategyChoose {

    private final FeaturedDealServiceImpl featuredDealCreate;
    private final RecommendSmartServiceImpl recommendSmartCreate;
    private List<AbstractCommonActivityStrategy> list = new ArrayList<>(2);

    @PostConstruct
    private void init() {
        list.add(featuredDealCreate);
        list.add(recommendSmartCreate);
    }

    public AbstractCommonActivityStrategy choose(String type) {
        if (StringUtils.isNotBlank(type)) {
            for (AbstractCommonActivityStrategy abstractCommonActivityStrategy : list) {
                if (abstractCommonActivityStrategy.isSupport(type)) {
                    return abstractCommonActivityStrategy;
                }
            }
        }
        throw new IllegalArgumentException("unknown common activity type: " + type);
    }

}

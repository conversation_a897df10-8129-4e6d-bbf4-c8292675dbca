package io.terminus.parana.item.utils;


import cn.hutool.core.util.HexUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Throwables;
import io.terminus.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Map;
import java.util.TreeMap;

@Component
@Slf4j
public class GeneratePinNuoHttpUtils {
    //  domain: http://test-alkaid.pinuc.com
    //  appId: pn2e6f4665bd0905a4
    //  appSecret: b766d7e7bf4945d7ba5f6c4f19b9f4a6
    //  h5domain: https://yyt-test.pinuc.com

    @Value(value = "${pinNuo.domain:}")
    private String domain;

    @Value(value = "${pinNuo.appId:}")
    private String appId;

    @Value(value = "${pinNuo.appSecret:}")
    private String appSecret;

    @Value(value = "${pinNuo.h5domain:}")
    private String h5domain;

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    private static final String schema = "PINUC-HMAC-SHA256";

    public String httpPost(String url, String body) {
        String httpUrl = domain + url;
        log.info("pinNuo httpPost httpUrl::{}, body::{}.", httpUrl, body);
        try {
            String authorization = getToken(url, "POST", body);
            log.info("pinNuo httpPost authorization::{}.", JSONUtil.toJsonStr(authorization));
            String response = HttpUtil.createPost(httpUrl).body(body).header("Authorization", authorization).execute().body();
            log.info("pinNuo httpPost response::{}.", response);
            JSONObject jsonObject = JSONUtil.parseObj(response);
            if (!ObjectUtils.isEmpty(jsonObject.get("code")) && "00000".equals(jsonObject.get("code"))) {
                return jsonObject.get("data").toString();
            } else {
                return StringUtils.EMPTY;
            }
        } catch (Exception e) {
            log.error("pinNuo httpPost httpUrl::{}, error::{}.", httpUrl, Throwables.getStackTraceAsString(e));
            throw new ServiceException("pinNuo httpPost error");
        }
    }

    public String httpGet(String url) {
        String httpUrl = domain + url;
        log.info("pinNuo httpGet httpUrl::{}.", httpUrl);
        try {
            String authorization = getToken(url, "GET", "");
            log.info("pinNuo httpGet authorization::{}.", JSONUtil.toJsonStr(authorization));
            String response = HttpUtil.createGet(httpUrl)
                    .header("Authorization", authorization)
                    .execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(response);
            log.info("pinNuo httpGet response::{}.", jsonObject);
            if (!ObjectUtils.isEmpty(jsonObject.get("code")) && "00000".equals(jsonObject.get("code"))) {
                return jsonObject.get("data").toString();
            } else {
                return StringUtils.EMPTY;
            }
        } catch (Exception e) {
            log.error("pinNuo httpGet httpUrl::{}, error::{}.", httpUrl, Throwables.getStackTraceAsString(e));
            throw new ServiceException("pinNuo httpGet error");
        }
    }

    public String getToken(String httpUrl, String method, String body) throws NoSuchAlgorithmException, InvalidKeyException {
        String nonce_str = generateRandomString(10);
        long timestamp = System.currentTimeMillis() / 1000;
        String message = buildMessage(method, httpUrl, timestamp, nonce_str, body);
        String signature = sign(appSecret, message);
        return schema + " " + "appid=\"" + appId + "\"," + "nonce_str=\"" + nonce_str + "\"," + "timestamp=\"" + timestamp + "\"," + "signature=\"" + signature + "\"";
    }

    public String sign(String appSecret, String message) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] bytes = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
        return HexUtil.encodeHexStr(bytes);
    }

    public String buildMessage(String method, String url, long timestamp, String nonceStr, String body) {
        return method + "\n"
                + url + "\n"
                + timestamp + "\n"
                + nonceStr + "\n"
                + body + "\n";
    }

    private final SecureRandom secureRandom = new SecureRandom();

    public String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = secureRandom.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(index));
        }
        return sb.toString();
    }

    public String signForLogin(Map<String, String> params, String appKey) throws NoSuchAlgorithmException {
        // 1. 对参数字典排序
        Map<String, String> sortedParams = new TreeMap<>(params);
        // 2. 拼接参数字符串,不需要urlencode编码
        StringBuilder paramsStr = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            paramsStr.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        // 3. 在字符串末尾添加app_key
        paramsStr.append("app_key=").append(appKey);
        // 4. ⽣成MD5签名
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] hashInBytes =
                md.digest(paramsStr.toString().getBytes(StandardCharsets.UTF_8));
        StringBuilder sb = new StringBuilder();
        for (byte b : hashInBytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    public String getUrl(String userId, String mobile) throws Exception {
        StringBuffer sb = new StringBuffer();
        Long ct = System.currentTimeMillis() / 1000;
        Map<String, String> params = new TreeMap<>();
        params.put("uid", userId);
        params.put("timestamp", String.valueOf(ct));
        params.put("ticket", "");
        params.put("redirect_uri", "");
        params.put("nickname", mobile);
        String sign = signForLogin(params, appId);
        //        1718259713 ffd0bf6d3ccdb64b30d575afbd99d7b3
        sb.append(this.h5domain).append("/#/?useUserInfo=true&nickname=").append(mobile).append("&redirect_uri=&ticket=&timestamp=").append(ct)
                .append("&uid=").append(userId).append("&sign=").append(sign);
        return sb.toString();
//        https://yyt-test.pinuc.com/#/?useUserInfo=true&nickname=13333333333&redirect_uri=&ticket=&timestamp=1718259713&uid=75&sign=ffd0bf6d3ccdb64b30d575afbd99d7b3
    }

    public static void main(String[] args) throws Exception {
//        http://xxx.pinuc.com/#/?useUserInfo=true&nickname=158****2520&redirect_uri=&ticket=&timestamp=2698118340&uid=75&sign=93e973bc3edecc78c5c5fd1e3bd7bc5
//        http://yyt-test.pinuc.com/#/?useUserInfo=true&nickname=⼤熊🐻&redirect_uri=&ticket=&timestamp=2698118340&uid=75&sign=93e973bc3edecc78c5c5fd1e3bd7bc5d
       /* String redirectUri = URLEncoder.encode("https://yyt-test.pinuc.com", "UTF-8");
        Long ct = System.currentTimeMillis()/1000;
        Map<String, String> params = new TreeMap<>();
        params.put("uid", "75");
        params.put("timestamp", String.valueOf(ct));
        params.put("ticket", "");
        params.put("redirect_uri", "");
        params.put("nickname", "13333333333");
        String sign = signForLogin(params,"pn2e6f4665bd0905a4");
        System.out.println(ct+" "+sign);*/
        //        1718259713 ffd0bf6d3ccdb64b30d575afbd99d7b3
//        https://yyt-test.pinuc.com/#/?useUserInfo=true&nickname=13333333333&redirect_uri=&ticket=&timestamp=1718259713&uid=75&sign=ffd0bf6d3ccdb64b30d575afbd99d7b3

        //        List<String> cardList = Lists.newArrayList();
//        cardList.add("82800563396");
//        Map<String, Object> params = Maps.newHashMap();
//        params.put("cardList", cardList);
//        String json = httpPost(PinNuoItemUrlConstant.BATCH_QUERY_CARD_URL, JSONUtil.toJsonStr(params));
//        System.out.println(json);
    }
}

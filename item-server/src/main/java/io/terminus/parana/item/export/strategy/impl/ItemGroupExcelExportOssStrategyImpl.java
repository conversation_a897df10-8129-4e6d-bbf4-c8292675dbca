package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.export.dto.ItemGroupExportDTO;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.item.api.bean.request.item.ParanaItemGroupPageRequest;
import io.terminus.parana.item.item.model.ParanaItemGroup;
import io.terminus.parana.item.item.service.ParanaItemGroupReadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ItemGroupExcelExportOssStrategyImpl implements ExcelExportStrategy {

    @Autowired
    private ExcelExportHelper excelExportHelper;
    @Autowired
    private ThirdPartyRegistry registry;
    @Autowired
    private ParanaItemGroupReadService itemGroupReadService;

    @Override
    public String getType() {
        return ExcelExportType.ITEM_GROUP_EXPORT.getReportType();
    }

    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        ParanaItemGroupPageRequest request = JSON.parseObject(requestJson, ParanaItemGroupPageRequest.class);
        log.info("通卡直充商品分组-分组列表导出,ParanaItemGroupPageRequest:{}", request);
        log.info("通卡直充商品分组-分组列表导出:filename{}", name);

        String filePath = null;
        InputStream is = null;
        try {
            List<ItemGroupExportDTO> dtoList = exportData(request);
            log.info("通卡直充商品分组-分组列表导出,导出结果条数：" + dtoList.size());
            Long endTime = System.currentTimeMillis();
            log.info("通卡直充商品分组-分组列表导出,time:{}", endTime - beginTime);
            ByteArrayOutputStream outputStream = excelExportHelper.generateExcel(dtoList, ItemGroupExportDTO.class);
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("通卡直充商品分组-分组列表导出, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("通卡直充商品分组-分组列表导出, et {}", Throwables.getStackTraceAsString(et));
                }
            }
            throw new RuntimeException(e.getMessage());
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("通卡直充商品分组-分组列表导出, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }

    /**
     * 组装数据导出
     *
     * @param request
     * @return
     */
    private List<ItemGroupExportDTO> exportData(ParanaItemGroupPageRequest request) {
        List<ItemGroupExportDTO> exportDTOS = new ArrayList<>();
        int pageNo = 1;
        while (true) {
            Map<String, Object> params = JSONUtil.parseObj(request);
            PageInfo pageInfo = new PageInfo(pageNo, 500);
            Paging<ParanaItemGroup> paging = itemGroupReadService.page(params, pageInfo.getOffset(), pageInfo.getLimit());
            if (paging.isEmpty()) {
                break;
            }
            List<Long> parentIds = paging.getData().stream().map(ParanaItemGroup::getParentId).filter(f -> f != 0).distinct().collect(Collectors.toList());
            Map<Long, String> map = Maps.newHashMap();
            if(CollectionUtil.isNotEmpty(parentIds)){
                ParanaItemGroup query = new ParanaItemGroup();
                query.setIds(parentIds);
                List<ParanaItemGroup> parentList = itemGroupReadService.list(query);
                if(CollectionUtil.isNotEmpty(parentList)){
                    map = parentList.stream().collect(Collectors.toMap(ParanaItemGroup::getId, ParanaItemGroup::getGroupName));
                }
            }
            for (ParanaItemGroup datum : paging.getData()) {
                ItemGroupExportDTO dto = new ItemGroupExportDTO();
                dto.setGroupId(datum.getId().toString());
                dto.setGroupName(datum.getGroupName());
                dto.setLevel(datum.getLevel() == 1 ? "一级" : "二级");
                if(datum.getParentId() != 0){
                    dto.setParentGroupName(map.get(datum.getParentId()));
                }
                exportDTOS.add(dto);
            }
            pageNo++;
        }
        return exportDTOS;
    }
}

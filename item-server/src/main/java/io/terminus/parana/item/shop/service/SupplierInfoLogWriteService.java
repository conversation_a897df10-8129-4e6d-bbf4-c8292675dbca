package io.terminus.parana.item.shop.service;

import io.terminus.parana.item.shop.model.SupplierInfoLogModel;
import io.terminus.parana.item.shop.repository.SupplierInfoLogDao;
import org.springframework.stereotype.Component;


import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class SupplierInfoLogWriteService {

	private final SupplierInfoLogDao supplierInfoLogDao;
	/**
	 * 创建供应商详情日志表
	 */
	public Boolean create(SupplierInfoLogModel model) {
		return supplierInfoLogDao.createModel(model);
	}
	/**
	 * 修改供应商详情日志表
	 */
	public Boolean update(SupplierInfoLogModel model) {
		return supplierInfoLogDao.updateModel(model);
	}
	/**
	 * 删除供应商详情日志表
	 */
	public Boolean delete(SupplierInfoLogModel model) {
		return supplierInfoLogDao.deleteById(model);
	}
}

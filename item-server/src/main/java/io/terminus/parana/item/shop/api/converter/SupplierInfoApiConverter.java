package io.terminus.parana.item.shop.api.converter;

import io.terminus.common.model.Paging;
import io.terminus.parana.item.shop.api.bean.request.*;
import io.terminus.parana.item.shop.api.bean.request.param.SupplierInfoCreateParam;
import io.terminus.parana.item.shop.api.bean.response.SupplierInfoInfoResponse;
import io.terminus.parana.item.shop.model.SupplierInfoModel;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SupplierInfoApiConverter {

	SupplierInfoModel get(SupplierInfoCreateRequest request);

	SupplierInfoModel get(SupplierInfoUpdateRequest request);

	SupplierInfoModel get(SupplierInfoUpdateCompanyRequest request);

	SupplierInfoModel get(SupplierInfoModCompanyRequest request);

	SupplierInfoModel get(SupplierInfoModSettlementRequest request);

	SupplierInfoModel get(SupplierInfoUpdateSettlementRequest request);

	SupplierInfoModel get(SupplierInfoDeleteRequest request);

	SupplierInfoModel get(SupplierInfoQueryRequest request);

	SupplierInfoModel get(SupplierInfoPageRequest request);

	SupplierInfoInfoResponse model2InfoResponse(SupplierInfoModel model);

	List<SupplierInfoInfoResponse> modelList2InfoResponseList(List<SupplierInfoModel> modelList);

	Paging<SupplierInfoInfoResponse> modePage2InfoPage(Paging<SupplierInfoModel> model);

	SupplierInfoModel param2domain(SupplierInfoCreateParam supplierInfoCreateParam);
}

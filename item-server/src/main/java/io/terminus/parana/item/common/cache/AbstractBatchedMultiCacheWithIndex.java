package io.terminus.parana.item.common.cache;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.terminus.parana.item.common.cache.plugin.BatchedMultiCache;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 基于{@link AbstractMultiCacheWithIndex}的抽象扩展，实现{@link BatchedMultiCache}接口中的部分功能。
 * <p>
 * 面向批量的双参数缓存抽象封装，提供了基于第一个抽象参数的批量缓存和获取能力。
 * </p>
 * <p>
 * 实现了{@link BatchedMultiCache#get(Object, Object...)}方法
 * </p>
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-23
 */
public abstract class AbstractBatchedMultiCacheWithIndex<TI1, TI2, TR> extends AbstractMultiCacheWithIndex<TI1, TI2, TR>
        implements BatchedMultiCache<TI1, TI2, TR> {

    private volatile boolean databaseInvoked = false;

    public AbstractBatchedMultiCacheWithIndex(String namespace) {
        super(namespace);
    }

    @Override
    public Collection<TR> get(TI1 input, Object... args) {
        if (input == null) {
            throw new IllegalArgumentException("输入参数不可为空");
        }

        return databaseInvoked
                ? queryFromCache(input)
                : queryFromDatabase(input, args);
    }

    /**
     * 从缓存中查询数据
     *
     * @param input 第一个参数
     * @return 结果集合
     */
    private Collection<TR> queryFromCache(TI1 input) {
        Set<TI2> children = getChildren(input);

        // 如果缓存中数据已失效，或者数据为空，则下次还需要走db
        if (CollectionUtils.isEmpty(children)) {
            databaseInvoked = false;
            return Collections.emptyList();
        }

        // 生成完成的缓存key集合
        Set<String> keySet = AssembleDataUtils.set2set(children, it -> generateKey(input, it));
        if (CollectionUtils.isEmpty(keySet)) {
            return Collections.emptyList();
        }

        // 从缓存中获取数据
        Map<String, TR> cacheResult = batchGet(keySet);
        if (CollectionUtils.isEmpty(cacheResult)) {
            return Collections.emptyList();
        }

        return AssembleDataUtils.predicate2set(cacheResult.values(), Objects::nonNull);
    }

    /**
     * 从数据库中获取数据
     *
     * @param input 第一个参数
     * @param args  附加参数
     * @return 结果集合
     */
    private Collection<TR> queryFromDatabase(TI1 input, Object[] args) {
        List<TR> databaseResult = databaseGet(input, args);
        databaseInvoked = true;

        // 如果从db中获取的数据为空，则不应该进入缓存结果
        if (CollectionUtils.isEmpty(databaseResult)) {
            databaseInvoked = false;
            return Collections.emptyList();
        }

        Set<TI2> memberSet = Sets.newHashSetWithExpectedSize(databaseResult.size());
        Map<String, TR> resultMap = Maps.newHashMapWithExpectedSize(databaseResult.size());
        for (TR tr : databaseResult) {
            TI2 member = ti2Parser(tr);
            memberSet.add(member);

            String key = generateKey(input, member);
            resultMap.put(key, tr);
        }

        addMember(input, memberSet);
        put(resultMap);

        return databaseResult;
    }

    @Override
    public void release(String key) {
        super.release(key);
    }

    @Override
    public void release(Set<String> keySet) {
        super.release(keySet);
    }
}

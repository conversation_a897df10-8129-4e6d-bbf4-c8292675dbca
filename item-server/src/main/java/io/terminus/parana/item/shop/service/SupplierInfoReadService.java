package io.terminus.parana.item.shop.service;

import com.google.common.collect.Maps;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.shop.api.bean.request.SupplierInfoQueryListRequest;
import io.terminus.parana.item.shop.model.SupplierInfoModel;
import io.terminus.parana.item.shop.repository.SupplierInfoDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class SupplierInfoReadService {

	private final SupplierInfoDao supplierInfoDao;
	/**
	 * 查询供应商信息表
	 */
	public SupplierInfoModel view(SupplierInfoModel model) {
		return supplierInfoDao.queryOne(model);
	}
	/**
	 * 查询供应商信息表列表
	 */
	public List<SupplierInfoModel> list(SupplierInfoQueryListRequest request) {
		Map<String, Object> criteria = Maps.newHashMap();
		criteria.put("ids",request.getIds());
		criteria.put("tenantId",request.getTenantId());
		return supplierInfoDao.queryList(criteria);
	}
	/**
	 * 分页查询供应商信息表列表
	 */
	public Paging<SupplierInfoModel> page(Map<String, Object> params, Integer offset, Integer limit) {
		return supplierInfoDao.page(params, offset, limit);
	}

	public Paging<SupplierInfoModel> selSeller(Map<String, Object> params, Integer offset, Integer limit) {
		return supplierInfoDao.selSeller(params, offset, limit);
	}

	public SupplierInfoModel queryByOperation(Long id) {
		return supplierInfoDao.queryByOperation(id);
	}

	public SupplierInfoModel queryByTenant(Long id) {
		return supplierInfoDao.queryByTenant(id);
	}

	public Long getAuditShopCount(Long operationId){
		Map<String, Object> criteria = Maps.newHashMap();
		criteria.put("operationId",operationId);
		return supplierInfoDao.getAuditShopCount(criteria);
	}

	/**
	 * 查询供应商信息表列表
	 */
	public List<SupplierInfoModel> listByRequest(SupplierInfoQueryListRequest request) {
		Map<String, Object> criteria = Maps.newHashMap();
		criteria.put("ids",request.getIds());
		criteria.put("settleTimeBefore",request.getSettleTimeBefore());
		criteria.put("settleTimeAfter",request.getSettleTimeAfter());
		criteria.put("updateTimeBefore",request.getUpdateTimeBefore());
		criteria.put("updateTimeAfter",request.getUpdateTimeAfter());
		criteria.put("enterpriseNameAbbreviation",request.getEnterpriseNameAbbreviation());
		criteria.put("enterpriseName",request.getEnterpriseName());
		criteria.put("billingCycle",request.getBillingCycle());
		criteria.put("auditStatus",request.getAuditStatus());
		criteria.put("tenantId",request.getTenantId());
		criteria.put("settleStatus",request.getSettleStatus());

		criteria.put("appliedTimeBefore",request.getAppliedTimeBefore());
		criteria.put("appliedTimeAfter",request.getAppliedTimeAfter());
		criteria.put("auditTimeBefore",request.getAuditTimeBefore());
		criteria.put("auditTimeAfter",request.getAuditTimeAfter());
		criteria.put("inType",request.getInType());
		criteria.put("syncSd",request.getSyncSd());
		criteria.put("sdNo",request.getSdNo());
		criteria.put("sdName",request.getSdName());

		return supplierInfoDao.listByRequest(criteria);
	}
}

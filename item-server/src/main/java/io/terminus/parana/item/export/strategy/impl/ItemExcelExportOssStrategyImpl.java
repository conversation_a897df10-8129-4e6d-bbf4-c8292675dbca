package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.eascs.user.enterprise.api.bean.response.EnterpriseAuthenticationInfo;
import com.eascs.user.enterprise.api.facade.EnterpriseAuthenticationReadFacade;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.inventory.api.bean.response.info.inventory.InventoryEntityResponseInfo;
import io.terminus.parana.ipm.api.bean.request.inventory.InventoryQueryByEntityAndVendorIdRequest;
import io.terminus.parana.ipm.api.facade.IpmInventoryReadFacade;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.service.AreaItemReadDomainService;
import io.terminus.parana.item.area.service.AreaSkuReadDomainService;
import io.terminus.parana.item.brand.api.bean.request.BrandGetRequest;
import io.terminus.parana.item.brand.api.bean.response.BrandInfo;
import io.terminus.parana.item.brand.api.facade.BrandReadFacade;
import io.terminus.parana.item.category.api.bean.request.BackCategoryQueryByIdsRequest;
import io.terminus.parana.item.category.api.facade.BackCategoryReadFacade;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.export.dto.ItemSearchShopExcelTemplate;
import io.terminus.parana.item.export.param.ItemSearchParam;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.item.api.bean.request.item.ItemQueryByIdRequest;
import io.terminus.parana.item.item.api.bean.request.item.VendorItemChannlRelationQueryRequest;
import io.terminus.parana.item.item.api.bean.request.sku.SkuQueryByItemRequest;
import io.terminus.parana.item.item.api.bean.response.item.FullItemWithInfo;
import io.terminus.parana.item.item.api.bean.response.item.ItemInfo;
import io.terminus.parana.item.item.api.bean.response.item.VendorItemChannlRelationInfoResponse;
import io.terminus.parana.item.item.api.bean.response.sku.SkuAttributeInfo;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.item.api.converter.output.ItemApiInfoConverter;
import io.terminus.parana.item.item.api.extension.ItemApiOutputProcessor;
import io.terminus.parana.item.item.api.facade.ItemReadFacade;
import io.terminus.parana.item.item.api.facade.SkuReadFacade;
import io.terminus.parana.item.item.api.facade.VendorItemChannlRelationReadFacade;
import io.terminus.parana.item.item.enums.ItemType;
import io.terminus.parana.item.item.repository.ItemDao;
import io.terminus.parana.item.search.adaptor.ItemComponentAdaptor;
import io.terminus.parana.item.search.docobject.ItemDO;
import io.terminus.parana.item.search.facade.ItemSearchFacade;
import io.terminus.parana.item.shop.api.bean.request.ShopQueryByIdRequest;
import io.terminus.parana.item.shop.api.bean.request.ShopSingleQueryByIdRequest;
import io.terminus.parana.item.shop.api.bean.response.ShopInfo;
import io.terminus.parana.item.shop.api.facade.ShopReadFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ItemExcelExportOssStrategyImpl implements ExcelExportStrategy {

    @Autowired
    private ExcelExportHelper excelExportHelper;

    @Autowired
    private ThirdPartyRegistry registry;

    @Override
    public String getType() {
        return ExcelExportType.ZQ_ITEM_EXPORT.getReportType();
    }

    @Autowired
    private ItemSearchFacade itemSearchFacade;
    @Autowired
    private SkuReadFacade skuReadFacade;
    @Autowired
    private ItemReadFacade itemReadFacade;
    @Autowired
    private ShopReadFacade shopReadFacade;
    @Autowired
    private BrandReadFacade brandReadFacade;
    @Autowired
    private BackCategoryReadFacade backCategoryReadFacade;
    private ItemDao itemDao;
    private List<ItemApiOutputProcessor> orderedItemApiOutputProcessorList;
    private ItemApiInfoConverter itemApiInfoConverter;

    @Autowired
    private IpmInventoryReadFacade ipmInventoryReadFacade;

    @Autowired
    private VendorItemChannlRelationReadFacade vendorItemChannlRelationReadFacade;

    @Autowired
    private EnterpriseAuthenticationReadFacade enterpriseAuthenticationReadFacade;

    @Autowired
    private ItemComponentAdaptor itemComponentAdaptor;
    @Autowired
    private AreaSkuReadDomainService areaSkuReadDomainService;
    @Autowired
    private AreaItemReadDomainService areaItemReadDomainService;



    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        ItemSearchParam itemSearchRequest = JSON.parseObject(requestJson, ItemSearchParam.class);
        log.info("item.export,itemSearchRequest:{}", itemSearchRequest);
        //组装完的表格数据
        List<ItemSearchShopExcelTemplate> excelDataList = excelDataList(itemSearchRequest);
        log.info("组装数据结果集：" + excelDataList);
        //组装完的搜索表头
        //List<Object> searchHead = searchHead(itemSearchRequest);
        //log.info("搜索表头结果集：" + searchHead);
        log.info("item.export,导出结果条数：" + excelDataList.size());
        Long endTime = System.currentTimeMillis();
        log.info("item.export.data.build,time:{}", endTime - beginTime);

        String filePath = null;
        InputStream is = null;
        try {
            ByteArrayOutputStream outputStream = null;
//            if (excelDataList.isEmpty()) {
//                outputStream = excelExportHelper.downloadTemplateCustom(ItemSearchShopExcelTemplate.class, searchHead);
//            } else {
//                outputStream = excelExportHelper.downloadTemplateCustom(excelDataList, ItemSearchShopExcelTemplate.class, searchHead);
//            }
            if (excelDataList.isEmpty()) {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(ItemSearchShopExcelTemplate.class);
            } else {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(excelDataList, ItemSearchShopExcelTemplate.class);
            }
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            log.info("filePath：{}", filePath);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("item.export, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("item.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("item.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }

    public Paging<ItemDO> search(ItemSearchParam param) {
        param.setTenantId(param.getTenantId());
        handle(param);
        return Assert.take(itemSearchFacade.search(param));
    }

    public void handle(ItemSearchParam param) {
        // 兼容参数处理
        if (null != param.getLowPriceYuan()) {
            param.setLowPrice(BigDecimal.valueOf(param.getLowPriceYuan()).multiply(BigDecimal.valueOf(100)).longValue());
        }
        if (null != param.getHighPriceYuan()) {
            param.setHighPrice(BigDecimal.valueOf(param.getHighPriceYuan()).multiply(BigDecimal.valueOf(100)).longValue());
        }
    }

    public Paging<FullItemWithInfo> searchExport(ItemSearchParam param) {
        List<FullItemWithInfo> dataInfoListResp = new ArrayList<>();
        int pageNo = param.getPageNo();
        while (true) {
            Paging<ItemDO> paging = search(param);
            if (paging.isEmpty()) {
                break;
            }
            log.info("paging.getData:{}", paging.getData());
            Set<Long> ids = paging.getData().stream().map(ItemDO::getId).collect(Collectors.toSet());
            ItemQueryByIdRequest itemReq = new ItemQueryByIdRequest();
            itemReq.setIdSet(ids);
            itemReq.setTenantId(param.getTenantId());
            log.info("itemReq:{}-{}", ids, itemReq);
            List<ItemInfo> itemRes = Assert.take(itemReadFacade.queryById(itemReq));
            log.info("itemRes:{}", itemRes);
            SkuQueryByItemRequest skuReq = new SkuQueryByItemRequest();
            skuReq.setItemIdSet(ids);
            skuReq.setTenantId(param.getTenantId());
            List<FullItemWithInfo> dataInfoList = null;
            try {
                List<SkuInfo> skuInfoList = Assert.take(skuReadFacade.queryByItem(skuReq));
                //使用stream将sku列表根据itemId转换为分组的map
                Map<Long, List<SkuInfo>> skuInfoMap = skuInfoList.stream().collect(Collectors.groupingBy(SkuInfo::getItemId));
                dataInfoList = new ArrayList<>();
                for (ItemInfo itemRe : itemRes) {
                    FullItemWithInfo resp = new FullItemWithInfo();
                    resp.setItemInfo(itemRe);
                    resp.setSkuInfo(skuInfoMap.get(itemRe.getId()));
                    dataInfoList.add(resp);
                }
                dataInfoListResp.addAll(dataInfoList);
            } catch (Exception e) {
                log.error("品牌商商品导出：{}", Throwables.getStackTraceAsString(e.fillInStackTrace()));
            }
            pageNo++;
            param.setPageNo(pageNo);
        }
        return new Paging<>((long) dataInfoListResp.size(), dataInfoListResp);
    }

    public List<ItemSearchShopExcelTemplate> excelDataList(ItemSearchParam param) {
        log.info("excelDataList ItemSearchParam :{}", param);
        Paging<FullItemWithInfo> dataInfoList = searchExport(param);
        //组装表格相关数据
        List<ItemSearchShopExcelTemplate> excelDataList = new ArrayList<>();
        if(dataInfoList.isEmpty()){
            return excelDataList;
        }
        Set<Long> itemIds = dataInfoList.getData().stream().filter(item -> item.getItemInfo() != null).map(item -> item.getItemInfo().getId()).collect(Collectors.toSet());
        List<AreaItem> areaItemList = areaItemReadDomainService.findByOperatorIdAndItemIds(null, itemIds);
        Map<Long, List<AreaItem>> areaItemMap = Maps.newHashMap();
        Map<String, List<AreaSku>> areaSkuMap = Maps.newHashMap();
        Map<Long, ShopInfo> shopInfoMap = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(areaItemList)){
            areaItemMap = areaItemList.stream().collect(Collectors.groupingBy(AreaItem::getItemId));
            Set<Long> operatorIds = areaItemList.stream().map(AreaItem::getOperatorId).collect(Collectors.toSet());
            List<AreaSku> areaSkuList = areaSkuReadDomainService.findByOperatorIdsAndItemIds(new ArrayList<>(operatorIds), new ArrayList<>(itemIds));
            if(CollectionUtil.isNotEmpty(areaSkuList)){
                areaSkuMap = areaSkuList.stream().collect(Collectors.groupingBy(m -> m.getOperatorId() + "_" + m.getItemId()));
            }
            ShopQueryByIdRequest request = new ShopQueryByIdRequest();
            request.setIdSet(operatorIds);
            request.setTenantId(1);
            List<ShopInfo> shopInfos = Assert.take(shopReadFacade.queryById(request));
            shopInfoMap = AssembleDataUtils.list2map(shopInfos, ShopInfo::getId);
        }


        //使用stream的方式拿到所有的skuId 需要判断skuInfo是否为空
        Set<Long> skuIds = dataInfoList.getData().stream().filter(item -> item.getSkuInfo() != null).flatMap(item -> item.getSkuInfo().stream().map(SkuInfo::getId)).collect(Collectors.toSet());
        //使用stream的方式拿到所有的类目Id
        Set<Long> categoryIds = dataInfoList.getData().stream().map(item -> item.getItemInfo().getCategoryId()).collect(Collectors.toSet());
        InventoryQueryByEntityAndVendorIdRequest req = new InventoryQueryByEntityAndVendorIdRequest();
        req.setVendorId(Long.valueOf(param.getShopId()));
        req.setSkuIdSet(skuIds);
        //查询库存
        List<InventoryEntityResponseInfo> inventoryEntityResponseInfoList = Assert.take(ipmInventoryReadFacade.queryByEntityAndVendorId(req));
        //查询类目
        Map<Long, String> categoryNameMap = itemComponentAdaptor.getCategoryNameMap(categoryIds, param.getTenantId());
        //使用stream将库存列表根据skuId转换为map
        Map<Long, InventoryEntityResponseInfo> entityResponseInfoMap = inventoryEntityResponseInfoList.stream().collect(Collectors.toMap(p -> Long.valueOf(p.getEntityId()), p -> p));

        for (FullItemWithInfo datum : dataInfoList.getData()) {
            //这里查询销售渠道 写到这里是因为销售渠道是商品级别的 节省查询次数
            String salesChannel = null;
            //获取销售渠道
            VendorItemChannlRelationQueryRequest vendorItemChannlRelationQueryRequest = new VendorItemChannlRelationQueryRequest();
            vendorItemChannlRelationQueryRequest.setId(datum.getItemInfo().getId());
            vendorItemChannlRelationQueryRequest.setVendorId(Long.valueOf(param.getShopId()));
            List<VendorItemChannlRelationInfoResponse> vendorItemChannlRelationInfoResponses = Assert.take(vendorItemChannlRelationReadFacade.findItemChannl(vendorItemChannlRelationQueryRequest));
            //取出来所有的销售渠道id
            List<Long> channelIds = vendorItemChannlRelationInfoResponses.stream().map(VendorItemChannlRelationInfoResponse::getChannelId).collect(Collectors.toList());
            if (channelIds.size() > 0) {
                //根据销售渠道id查询销售渠道名称
                List<EnterpriseAuthenticationInfo> enterpriseAuthenticationInfoList = Assert.take(enterpriseAuthenticationReadFacade.getEnterpriseAuthenticationInfoByIds(channelIds));
                //将销售渠道名称用逗号拼接
                salesChannel = enterpriseAuthenticationInfoList.stream().map(EnterpriseAuthenticationInfo::getCompanyName).collect(Collectors.joining(","));
            }
            if (datum.getSkuInfo() == null) {
                continue;
            }
            for (SkuInfo skuInfo : datum.getSkuInfo()) {
                ItemSearchShopExcelTemplate data = new ItemSearchShopExcelTemplate();
                ItemInfo itemInfo = datum.getItemInfo();
                if (itemInfo == null) {
                    continue;
                }

                log.info("excelDataList long itemInfo :{}", itemInfo);
                log.info("excelDataList long skuInfo :{}", skuInfo);
                //skuId
                data.setSkuId(skuInfo.getId().toString());
                StringBuilder attrVal = new StringBuilder();
                //从skuInfo.getAttributes()中获取规格值用逗号拼接
                if(CollectionUtil.isNotEmpty(skuInfo.getAttributes())){
                    for (SkuAttributeInfo attribute : skuInfo.getAttributes()) {
                        attrVal.append(attribute.getAttrVal()).append(",");
                    }
                }
                data.setAttrVal(attrVal.toString());
                //规格国际编码
                data.setItemTaxCode(skuInfo.getSkuCode());
                data.setSkuCode(skuInfo.getBarcode());
                //起售数量
                Long minQuantity = null;
                if (ObjectUtil.isNotNull(skuInfo.getExtra())) {
                    minQuantity = skuInfo.getExtra().get("minQuantity") == null ? null : Long.valueOf(skuInfo.getExtra().get("minQuantity").toString());
                    data.setMinQuantity(String.valueOf(minQuantity));
                }
                //库存
                InventoryEntityResponseInfo inventoryEntityResponseInfo = entityResponseInfoMap.get(skuInfo.getId());
                if (null != inventoryEntityResponseInfo) {
                    //实际库存
                    data.setRealQuantity(String.valueOf(inventoryEntityResponseInfo.getRealQuantity()));
                }
                 if(CollectionUtil.isNotEmpty(skuInfo.getExtraPrice()) && skuInfo.getExtraPrice().containsKey("centralizedPurchasePrice")){
                    data.setCentralizedPurchasePrice(skuInfo.getExtraPrice().get("centralizedPurchasePrice")/100.0);
                }
                //商品名称
                data.setName(itemInfo.getName());
                data.setBrandName(itemInfo.getBrandName());
                //商品类目
                data.setCategory(categoryNameMap.get(itemInfo.getCategoryId()));
                //类目id
                data.setCategoryId(String.valueOf(itemInfo.getCategoryId()));
                //商品类型
                data.setType(ItemType.fromValue(itemInfo.getType()).getDescription());
                //创建时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date createdAt = itemInfo.getCreatedAt();
                String format = sdf.format(createdAt != null ? createdAt : "");
                data.setCreatedAt(format);
                //销售渠道
                data.setSalesChannel(salesChannel);
                String itemid = itemInfo.getId().toString();
                data.setItemid(itemid != null ? itemid : "");
                String supportReturn = itemInfo.getExtra().get("supportReturn");
                if (supportReturn != null) {
                    data.setSupportReturn("1".equals(supportReturn) ? "是" : "否");
                }
                data.setTaxCode(itemInfo.getTaxcode());
                data.setTaxName(itemInfo.getTaxname());
                data.setVatrate(itemInfo.getVatrate() + "");
                data.setZqbjurl1(itemInfo.getZqbjurl1());
                data.setZqbjurl2(itemInfo.getZqbjurl2());
                data.setZqbjurl3(itemInfo.getZqbjurl3());
                log.info("excelDataList long data :{}", data);
                //供货价 建议零售价
                data.setBasePrice(skuInfo.getExtraPrice()==null && skuInfo.getExtraPrice().get("defaultBasePrice") == null
                        ?"":(skuInfo.getExtraPrice().get("defaultBasePrice")/100.0 + ""));
                data.setSuggestedRetailPrice(skuInfo.getOriginalPrice()==null?"":(skuInfo.getOriginalPrice()/100.0 + ""));
                List<AreaItem> areaItems = areaItemMap.get(datum.getItemInfo().getId());
                if(CollectionUtil.isEmpty(areaItems)){
                    excelDataList.add(data);
                }else{
                    for (AreaItem areaItem : areaItems) {
                        if(areaItem.getSourceType() == 0 || (areaItem.getSourceType() == 1 && areaItem.getOperatorId() ==1)){
                            List<AreaSku> areaSkus = areaSkuMap.get(areaItem.getOperatorId() + "_" + datum.getItemInfo().getId());
                            for (AreaSku areaSku : areaSkus) {
                                if(areaSku.getSkuId().equals(skuInfo.getId())){
                                    //供货价 建议零售价
                                    data.setBasePrice(areaSku.getBasePrice()==null?"":(areaSku.getBasePrice()/100.0 + ""));
                                    data.setSuggestedRetailPrice(areaSku.getOriginalPrice()==null?"":(areaSku.getOriginalPrice()/100.0 + ""));
                                    data.setOperatorId(areaSku.getOperatorId() + "");
                                    data.setOperatorName(shopInfoMap.get(areaSku.getOperatorId()).getName());
                                    ItemSearchShopExcelTemplate newBean = BeanUtil.toBean(data, ItemSearchShopExcelTemplate.class);
                                    excelDataList.add(newBean);
                                }
                            }
                        }
                    }

                }
            }
        }
        return excelDataList;
    }

    public List<Object> searchHead(ItemSearchParam param) {
        log.info("searchHead ItemSearchParam :{}", param);
        //在这里组装Excel所需要的搜索结果表头
        List<Object> searchHead = new ArrayList<>();
        searchHead.add("商品名称");
        searchHead.add(param.getName() == null ? "--" : param.getName());
        searchHead.add("商品ID");
        searchHead.add(param.getId() == null ? "--" : param.getId());
        searchHead.add("商品条码");
        searchHead.add(param.getBarcode() == null ? "--" : param.getBarcode());
        searchHead.add("未发布区域运营");
        if (param.getNoExistedOpeartorId() != null) {
            ShopSingleQueryByIdRequest shopInfoReq = new ShopSingleQueryByIdRequest();
            shopInfoReq.setTenantId(param.getTenantId());
            shopInfoReq.setTenantIdLong(param.getTenantIdLong());
            shopInfoReq.setId(param.getNoExistedOpeartorId());
            log.info("shopInfoReq 101: {}", shopInfoReq);
            Response<ShopInfo> shopInfoRes = shopReadFacade.querySingleById(shopInfoReq);
            log.info("shopInfoRes 102: {}", shopInfoRes);
            if (shopInfoRes.isSuccess()) {
                searchHead.add(shopInfoRes.getResult().getName());
            } else {
                searchHead.add("--");
            }
        } else {
            log.info("shopInfoRes 103: 空");
            searchHead.add("--");
        }
        searchHead.add("商品类型");
        if (param.getType() == null) {
            searchHead.add("全部");
        } else if ("1".equals(param.getType())) {
            searchHead.add("普通商品");
        } else if ("2".equals(param.getType())) {
            searchHead.add("虚拟商品");
        } else if ("3".equals(param.getType())) {
            searchHead.add("服务商品");
        }
        searchHead.add("商品状态");
        if ("1_-1_-2".equals(param.getStatus())) {
            searchHead.add("全部");
        } else if ("-1".equals(param.getStatus())) {
            searchHead.add("正常");
        } else if ("-2".equals(param.getStatus())) {
            searchHead.add("停止销售");
        }
        searchHead.add("品牌");
        if (param.getBrandId() != null) {
            BrandGetRequest brandInfoReq = new BrandGetRequest();
            brandInfoReq.setId(param.getBrandId());
            log.info("brandInfoReq 104: {}", brandInfoReq);
            Response<BrandInfo> brandInfoRes = brandReadFacade.findById(brandInfoReq);
            log.info("brandInfoRes 105: {}", brandInfoRes);
            if (brandInfoRes.isSuccess()) {
                searchHead.add(brandInfoRes.getResult().getName());
            } else {
                searchHead.add("--");
            }
        } else {
            log.info("brandInfoRes 106: 空");
            searchHead.add("--");
        }
        searchHead.add("所属分类");
        if (param.getCategoryId() != null) {
            List<String> categoryIds = Arrays.asList(param.getCategoryId().split("_"));
            BackCategoryQueryByIdsRequest backCategoryReq = new BackCategoryQueryByIdsRequest();
            backCategoryReq.setIds(categoryIds.stream().map(Long::valueOf).collect(Collectors.toSet()));
            log.info("backCategoryReq 107: {}", backCategoryReq);
            Response<Map<Long, String>> backCategoryInfo = backCategoryReadFacade.findByIds(backCategoryReq);
            log.info("backCategoryInfo 108: {}", backCategoryInfo);
            if (backCategoryInfo.isSuccess()) {
                StringBuilder backCategory = new StringBuilder();
                for (Map.Entry<Long, String> longStringEntry : backCategoryInfo.getResult().entrySet()) {
                    Object val = ((Map.Entry) longStringEntry).getValue();
                    backCategory.append(val.toString()).append(",");
                }
                log.info("backCategory 109: {}", backCategory.deleteCharAt(backCategory.lastIndexOf(",")));
                searchHead.add(backCategory.deleteCharAt(backCategory.lastIndexOf(",")));
            } else {
                searchHead.add("--");
            }
        } else {
            log.info("backCategoryInfo 110: 空");
            searchHead.add("--");
        }
        return searchHead;
    }

    private <T> T wrap(T t) {
        if (CollectionUtils.isEmpty(orderedItemApiOutputProcessorList)) {
            return t;
        }

        for (ItemApiOutputProcessor processor : orderedItemApiOutputProcessorList) {
            t = processor.wrapCore(t);
        }

        return t;
    }

}
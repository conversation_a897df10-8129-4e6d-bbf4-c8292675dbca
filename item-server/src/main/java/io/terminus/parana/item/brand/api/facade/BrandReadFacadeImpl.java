package io.terminus.parana.item.brand.api.facade;

import com.google.common.base.MoreObjects;
import io.terminus.api.utils.ParamUtil;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.brand.api.bean.request.BrandGetRequest;
import io.terminus.parana.item.brand.api.bean.request.BrandMultiGetRequest;
import io.terminus.parana.item.brand.api.bean.request.BrandQueryRequest;
import io.terminus.parana.item.brand.api.bean.response.BrandInfo;
import io.terminus.parana.item.brand.api.converter.BrandConverter;
import io.terminus.parana.item.brand.api.manager.BrandService;
import io.terminus.parana.item.brand.model.Brand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2018-07-13 上午12:23
 */
@Slf4j
@Component
public class BrandReadFacadeImpl implements BrandReadFacade {
    @Autowired
    private BrandConverter converter;
    @Autowired
    private BrandService brandService;

    @Override
    public Response<BrandInfo> findById(BrandGetRequest request) {
        try {
            Brand brand = brandService.findById(request.getId());
            return Response.ok(converter.domain2info(brand));
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.find.fail");
        }
    }

    @Override
    public Response<List<BrandInfo>> findByIds(BrandMultiGetRequest request) {
        try {
            List<Brand> brands = brandService.findByIds(request.getIds());
            if (CollectionUtils.isNotEmpty(brands)) {
                return Response.ok(brands.stream().map(
                        brand -> converter.domain2info(brand)).collect(Collectors.toList()));
            }
            return Response.ok(Collections.emptyList());
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.find.fail");
        }
    }

    @Override
    public Response<BrandInfo> findByName(BrandQueryRequest request) {
        ParamUtil.notBlank(request.getName(), "brand.name.required");
        try {
            Brand brand = brandService.findByName(request.getName(), request.getExtensionType());
            return Response.ok(converter.domain2info(brand));
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.find.fail");
        }
    }

    @Override
    public Response<Boolean> exist(BrandQueryRequest request) {
        ParamUtil.notBlank(request.getName(), "brand.name.required");
        try {
            return Response.ok(brandService.exist(request.getName(), request.getExtensionType()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.find.fail");
        }
    }

    @Override
    public Response<BrandInfo> findByOuterId(BrandQueryRequest request) {
        ParamUtil.notBlank(request.getOuterId(), "brand.outerId.required");
        try {
            Brand brand = brandService.findByOuterId(request.getOuterId(), request.getExtensionType());
            return Response.ok(converter.domain2info(brand));
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.find.fail");
        }
    }

    @Override
    public Response<List<BrandInfo>> findByNamePrefix(BrandQueryRequest request) {
        //  if (StringUtil.isBlank(request.getNamePrefix())) {
        //      return Response.ok(Collections.emptyList());
        //  }
        Integer count = MoreObjects.firstNonNull(request.getCount(), 10);
        try {
            List<Brand> brands = brandService.findByNamePrefix(request.getNamePrefix(), count);
            if (CollectionUtils.isNotEmpty(brands)) {
                return Response.ok(brands.stream().map(
                        brand -> converter.domain2info(brand)).collect(Collectors.toList()));
            }
            return Response.ok(Collections.emptyList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.find.fail");
        }
    }

    @Override
    public Response<Paging<BrandInfo>> pagingByNamePrefix(BrandQueryRequest request) {
        PageInfo info = PageInfo.of(request.getPageNo(), request.getPageSize());
        try {
            Paging<Brand> brands = brandService.pagingByNamePrefix(request.getNamePrefix(),request.getId(), request.getOrderBy(),info);
            if (CollectionUtils.isNotEmpty(brands.getData())) {
                List<BrandInfo> data = brands.getData().stream().map(
                        brand -> converter.domain2info(brand)).collect(Collectors.toList());
                return Response.ok(new Paging<>(brands.getTotal(), data));
            }
            return Response.ok(Paging.empty());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.find.fail");
        }
    }

    @Override
    public Response<List<BrandInfo>> findByNames(BrandMultiGetRequest request) {
        try {
            List<Brand> brands = brandService.findByNames(request.getNames());
            if (CollectionUtils.isNotEmpty(brands)) {
                return Response.ok(brands.stream().map(
                        brand -> converter.domain2info(brand)).collect(Collectors.toList()));
            }
            return Response.ok(Collections.emptyList());
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("brand.find.fail");
        }
    }
}

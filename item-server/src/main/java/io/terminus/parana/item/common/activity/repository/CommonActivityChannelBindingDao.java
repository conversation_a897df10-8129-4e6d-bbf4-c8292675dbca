package io.terminus.parana.item.common.activity.repository;

import io.terminus.parana.item.common.activity.model.CommonActivityChannelBinding;
import io.terminus.parana.item.common.base.AbstractMybatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class CommonActivityChannelBindingDao extends AbstractMybatisDao<CommonActivityChannelBinding> {

    /**
     * 根据渠道IDs查询所关联常用活动IDs
     * @param channelIds 渠道IDs
     * @param operatorId 区域运营ID
     * @return 所关联常用活动IDs
     */
    public List<Long> findCommonRelationIdsByChannelIds(List<Long> channelIds, Long operatorId) {
        Map<String, Object> map = new HashMap<>(8);
        map.put("channelIds", channelIds);
        map.put("operatorId", operatorId);
        return sqlSession.selectList(sqlId("findCommonRelationIdsByChannelIds"), map);
    }

    /**
     * 根据常用活动ID查询所关联渠道IDs
     * @param commonRelationId 常用活动ID
     * @param operatorId 区域运营ID
     * @return 所关联渠道IDs
     */
    public List<Long> findChannelIdsByCommonRelationId(Long commonRelationId, Long operatorId) {
        Map<String, Object> map = new HashMap<>(8);
        map.put("commonRelationId", commonRelationId);
        map.put("operatorId", operatorId);
        return sqlSession.selectList(sqlId("findChannelIdsByCommonRelationId"), map);
    }

    /**
     * 根据常用活动ID删除所关联的渠道IDs
     * @param commonRelationId 常用活动ID
     * @return 删除结果
     */
    public int deleteByCommonRelationId(Long commonRelationId) {
        return sqlSession.delete(sqlId("deleteByCommonRelationId"), commonRelationId);
    }

    /**
     *
     * @param operatorId
     * @return
     */

    /**
     * 查询已关联的渠道IDs
     * @param operatorId 区域运营ID
     * @param ids 常用活动id
     * @return 渠道IDs
     */
    public List<Long> findCommonChannelList(Long operatorId,List<Long> commonRelationIds) {
        Map<String, Object> map = new HashMap<>(8);
        map.put("commonRelationIds", commonRelationIds);
        map.put("operatorId", operatorId);
        return sqlSession.selectList(sqlId("findCommonChannelList"), map);
    }

}

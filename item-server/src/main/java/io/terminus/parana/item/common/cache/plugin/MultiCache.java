package io.terminus.parana.item.common.cache.plugin;

import io.terminus.parana.item.common.cache.CacheOperate;

import java.util.Set;

/**
 * 双入参数据的缓存接口定义。双入参对象是指在缓存过程中，存在次级缓存关系的复杂缓存。<br>
 * <p>
 * 例如：
 * <p>
 * 缓存对象CacheObject存在两个关键属性：A和B，且具备以下特性：<br>
 * 1. 通过A和B两个属性叠加，可获得唯一值；<br>
 * 2. 通过A属性，可获得值集合，包括1所获得的结果<br>
 * </p>
 * </p>
 * 此接口定义的目的，主要在于解决缓存失效时，面向输入参的级联缓存失效问题。
 * <p>
 * <b>***注意：接口严格区分类型TI1和TI2，同类型时注意顺序区分！***</b>
 * </p>
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-15
 */
public interface MultiCache<TI1, TI2, TR> extends CacheOperate<TR> {

    /**
     * 获得缓存对象
     *
     * @param input1 入参1
     * @param input2 入参2
     * @param args   附加参数
     * @return 缓存结果
     */
    TR get(TI1 input1, TI2 input2, Object... args);

    /**
     * 从数据库中获取数据
     *
     * @param input1 入参1
     * @param input2 入参2
     * @param args   其它参数
     * @return 数据结果
     */
    TR databaseGet(TI1 input1, TI2 input2, Object... args);

    /**
     * 移除缓存
     *
     * @param input1 入参1
     * @param input2 入参2
     */
    void remove(TI1 input1, TI2 input2);

    /**
     * 移除缓存
     *
     * @param inputSet 入参集合
     * @param input2     附加参数
     */
    void remove(Set<TI1> inputSet, TI2 input2);

    /**
     * 移除缓存
     *
     * @param input 第一个入参
     */
    void remove(TI1 input);

    /**
     * 生成缓存key
     *
     * @param input1 入参1
     * @param input2 入参2
     * @return 缓存key
     */
    String generateKey(TI1 input1, TI2 input2);
}

package io.terminus.parana.item.common.activity.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CommonActivityGoodsBinding implements Serializable {

    private static final long serialVersionUID = -2270575960783285126L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("常用活动id")
    private Long commonRelationId;

    @ApiModelProperty("商品id")
    private Long goodsId;

    @ApiModelProperty("创建时间")
    private Date createdAt;

    @ApiModelProperty("更新时间")
    private Date updatedAt;

    @ApiModelProperty("区域运营ID")
    private Long operatorId;

    @ApiModelProperty("排序值")
    private Integer sort;

}

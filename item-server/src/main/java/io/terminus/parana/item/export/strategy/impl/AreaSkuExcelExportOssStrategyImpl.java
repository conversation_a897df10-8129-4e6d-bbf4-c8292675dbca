package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.item.area.service.AreaSkuExtendsReadService;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.export.dto.ParanaAreaSkuExtendsListExportDTO;
import io.terminus.parana.item.export.dto.ParanaAreaSkuExtendsListExportDTO;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.price.api.bean.request.AreaSkuExtendsfindBySkuIdRequest;
import io.terminus.parana.item.price.api.bean.response.AreaSkuExtendsAllInfo;
import io.terminus.parana.item.search.docobject.ChoiceItemDO;
import io.terminus.parana.item.search.facade.ChoiceItemSearchFacade;
import io.terminus.parana.item.search.request.ChoiceItemSearchRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class AreaSkuExcelExportOssStrategyImpl implements ExcelExportStrategy {

    private static final int MAX_SIZE = 1000;

    @Autowired
    private ExcelExportHelper excelExportHelper;
    @Autowired
    private ThirdPartyRegistry registry;
    @Autowired
    private ChoiceItemSearchFacade choiceItemSearchFacade;
    @Autowired
    private AreaSkuExtendsReadService areaSkuExtendsReadService;

    @Override
    public String getType() {
        return ExcelExportType.SKU_LIST_EXPORT.getReportType();
    }

    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        ChoiceItemSearchRequest request = JSON.parseObject(requestJson, ChoiceItemSearchRequest.class);
        log.info("area sku list.export,ChoiceItemSearchRequest:{}", request);
        log.info("area sku list.export,ChoiceItemSearchRequest:filename{}", name);
        List<ParanaAreaSkuExtendsListExportDTO> dtoList = exportData(request);
        log.info("parana zq sku list.export,导出结果条数：" + dtoList.size());
        Long endTime = System.currentTimeMillis();
        log.info("parana zq sku list.export.data.build,time:{}", endTime - beginTime);

        String filePath = null;
        InputStream is = null;
        try {
            ByteArrayOutputStream outputStream = excelExportHelper.generateExcel(dtoList, ParanaAreaSkuExtendsListExportDTO.class);
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("parana zq sku list.export, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("parana zq sku list.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
            throw new RuntimeException(e.getMessage());
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("parana zq sku list.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }

    /**
     * 组装数据导出
     *
     * @param request
     * @return
     */
    private List<ParanaAreaSkuExtendsListExportDTO> exportData(ChoiceItemSearchRequest request) {
        List<ParanaAreaSkuExtendsListExportDTO> exportDTOS = new ArrayList<>();
        int pageNo = request.getPageNo();
        while (true) {
            Response<Paging<ChoiceItemDO>> search = choiceItemSearchFacade.distributorSearch(request);
            Paging<ChoiceItemDO> itemPaging = Assert.take(search);
            if (itemPaging.isEmpty() || CollectionUtils.isEmpty(itemPaging.getData())) {
                break;
            }
            log.info("distributor.item.export,searchExport. search result total:{}", itemPaging.getData().size());
            List<ParanaAreaSkuExtendsListExportDTO> ParanaAreaSkuExtendsListExportDTOs = this.excelDataList(itemPaging,request);
            if (!CollectionUtils.isEmpty(ParanaAreaSkuExtendsListExportDTOs)) {
                exportDTOS.addAll(ParanaAreaSkuExtendsListExportDTOs);
            }
            log.info("distributor.item.export,searchExport. search dataInfoListResp total:{}", ParanaAreaSkuExtendsListExportDTOs.size());
            pageNo++;
            request.setPageNo(pageNo);
        }
        return exportDTOS;
    }

    public List<ParanaAreaSkuExtendsListExportDTO> excelDataList(Paging<ChoiceItemDO> pageSearchResult,ChoiceItemSearchRequest request) {
        List<ParanaAreaSkuExtendsListExportDTO> exportDTOS = new ArrayList<>();
        log.info("distributor.item.export,excelDataList. pageSearchResult total:{}", pageSearchResult.getData());
        if (CollectionUtils.isEmpty(pageSearchResult.getData())) {
            return Lists.newArrayList();
        }
        pageSearchResult.getData().forEach(v -> {
            AreaSkuExtendsfindBySkuIdRequest skuIdRequest = new AreaSkuExtendsfindBySkuIdRequest();
            skuIdRequest.setTenantId(request.getTenantId());
            skuIdRequest.setReqHeardDistributorIds(request.getReqHeardDistributorIds());
            skuIdRequest.setItemId(v.getItemId());
            AreaSkuExtendsAllInfo allInfo = areaSkuExtendsReadService.findExtendsBySkuId(skuIdRequest);
            if (!ObjectUtils.isEmpty(allInfo)) {
                if (!CollectionUtils.isEmpty(allInfo.getParanaSkuInfoResponses())) {
                    allInfo.getParanaSkuInfoResponses().forEach(l -> {
                        ParanaAreaSkuExtendsListExportDTO dto = new ParanaAreaSkuExtendsListExportDTO();
                        dto.setItemId(v.getItemId());
                        dto.setItemName(v.getName());
                        dto.setCategoryName(allInfo.getOutCategoryName());
                        dto.setBrandName(allInfo.getOutBrandName());
                        dto.setOutProjectCategoryName(allInfo.getOutProjectCategoryName());
                        dto.setUnit(allInfo.getUnit());
                        dto.setVatrate(allInfo.getVatrate());
                        dto.setTaxCode(allInfo.getTaxCode());
                        dto.setTaxName(allInfo.getTaxName());
                        dto.setSkuId(l.getId());
                        dto.setPmodel(l.getPmodel());
                        dto.setBarcode(l.getBarcode());
                        dto.setCommoditymodel(l.getCommoditymodel());
                        dto.setColour(l.getColour());
                        dto.setUniversalName(allInfo.getUniversalName());
                        exportDTOS.add(dto);
                    });
                } else {
                    ParanaAreaSkuExtendsListExportDTO dto = new ParanaAreaSkuExtendsListExportDTO();
                    dto.setItemId(v.getItemId());
                    dto.setItemName(v.getName());
                    exportDTOS.add(dto);
                }
            } else {
                ParanaAreaSkuExtendsListExportDTO dto = new ParanaAreaSkuExtendsListExportDTO();
                dto.setItemId(v.getItemId());
                dto.setItemName(v.getName());
                exportDTOS.add(dto);
            }
        });
        return exportDTOS;
    }

}
package io.terminus.parana.item.common.flow;

/**
 * 状态流，用以校验&控制对象的状态转换
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2018-11-28
 */
public interface StatusFlow {

    /**
     * 判断从当前对象到下一个对象的状态流转是否合法
     *
     * @param clazz         目标类对象
     * @param currentStatus 当前状态
     * @param nextStatus    下一个状态
     * @param args          附加参数
     * @return 是否合法
     */
    boolean available(Class<?> clazz, Integer currentStatus, Integer nextStatus, Object... args);

    /**
     * 获取下一个状态（仅限于单状态流转）
     *
     * @param clazz         目标类对象
     * @param currentStatus 当前状态
     * @param expectStatus  预期目标对象
     * @param args          附加参数
     * @return 下一个对象
     */
    Integer nextStatus(Class<?> clazz, Integer currentStatus, Integer expectStatus, Object... args);
}

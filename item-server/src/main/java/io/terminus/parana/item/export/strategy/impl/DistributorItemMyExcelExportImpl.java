package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.inventory.api.bean.response.info.inventory.InventoryEntityResponseInfo;
import io.terminus.parana.ipm.api.bean.request.inventory.InventoryQueryByEntityAndOperatorIdRequest;
import io.terminus.parana.ipm.api.facade.IpmInventoryReadFacade;
import io.terminus.parana.item.category.manager.BackCategoryService;
import io.terminus.parana.item.category.model.BackCategory;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibSkuReadService;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.export.dto.ChoiceSearchDistributorMyExcelTemplate;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.item.model.SkuAttribute;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.item.service.SkuReadDomainService;
import io.terminus.parana.item.search.docobject.ChoiceItemDO;
import io.terminus.parana.item.search.facade.ChoiceItemSearchFacade;
import io.terminus.parana.item.search.request.ChoiceItemSearchRequest;
import io.terminus.parana.user.api.facade.AddressReadFacade;
import io.terminus.parana.user.api.request.address.FindAddressByIdsRequest;
import io.terminus.parana.user.api.response.AddressInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DistributorItemMyExcelExportImpl implements ExcelExportStrategy {

    @Autowired
    private ChoiceItemSearchFacade choiceItemSearchFacade;

    @Autowired
    private ItemReadDomainService itemReadDomainService;

    @Autowired
    private SkuReadDomainService skuReadDomainService;

    @Autowired
    private ChoiceLotLibSkuReadService choiceLotLibSkuReadService;

    @Autowired
    private BackCategoryService backCategoryService;

    @Autowired
    private ExcelExportHelper excelExportHelper;

    @Autowired
    private ThirdPartyRegistry registry;

    @Resource
    private AddressReadFacade addressReadFacade;
    @Autowired
    private IpmInventoryReadFacade ipmInventoryReadFacade;

    @Override
    public String getType() {
        return ExcelExportType.COMMODITY_LIBRARY_EXPORT.getReportType();
    }

    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        ChoiceItemSearchRequest request = JSON.parseObject(requestJson, ChoiceItemSearchRequest.class);
        List<ChoiceSearchDistributorMyExcelTemplate> choiceSearchDistributorExcelTemplates = searchExport(request);
        log.info("commodity_library_export,choiceSearchDistributorExcelTemplates:{} ：" , choiceSearchDistributorExcelTemplates.size());
        Long endTime = System.currentTimeMillis();
        log.info("commodity_library_export,time:{}", endTime - beginTime);
        String filePath = null;
        InputStream is = null;
        try {
            ByteArrayOutputStream outputStream = null;
            if (choiceSearchDistributorExcelTemplates.isEmpty()) {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(ChoiceSearchDistributorMyExcelTemplate.class);
            } else {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(choiceSearchDistributorExcelTemplates, ChoiceSearchDistributorMyExcelTemplate.class);
            }
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            log.info("commodity_library_export.filePath：{}", filePath);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("commodity_library_export, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("commodity_library_export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("commodity_library_export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }

    public List<ChoiceSearchDistributorMyExcelTemplate> searchExport(ChoiceItemSearchRequest request) {
        log.info("commodity_library_export,searchExport. param:{}", request);
        List<ChoiceSearchDistributorMyExcelTemplate> dataInfoListResp = new ArrayList<>();
        int pageNo = request.getPageNo();
        while(true){
            Response<Paging<ChoiceItemDO>> search = choiceItemSearchFacade.distributorSearch(request);
            Paging<ChoiceItemDO> itemPaging = Assert.take(search);
            if (itemPaging.isEmpty() || CollectionUtils.isEmpty(itemPaging.getData())) {
                break;
            }
            log.info("commodity_library_export,searchExport. search result total:{}", itemPaging.getData().size());
            List<ChoiceSearchDistributorMyExcelTemplate> choiceSearchDistributorExcelTemplates = excelDataList(itemPaging, request);
            if(!CollectionUtils.isEmpty(choiceSearchDistributorExcelTemplates)){
                dataInfoListResp.addAll(choiceSearchDistributorExcelTemplates);
            }
            log.info("commodity_library_export,searchExport. search dataInfoListResp total:{}", choiceSearchDistributorExcelTemplates.size());
            pageNo++;
            request.setPageNo(pageNo);
        }
        return dataInfoListResp;
    }

    public List<ChoiceSearchDistributorMyExcelTemplate> excelDataList(Paging<ChoiceItemDO>  pageSearchResult, ChoiceItemSearchRequest request){
        log.info("commodity_library_export,excelDataList. pageSearchResult total:{}", pageSearchResult.getData());

        if(CollectionUtils.isEmpty(pageSearchResult.getData())){
            return Lists.newArrayList();
        }
        int total = pageSearchResult.getData().size();
        Set<Long> choiceIds = new HashSet<>();
        Map<Long, ChoiceItemDO> choiceItemMap = new HashMap<>(total);
        List<Long> categoryIdList = new ArrayList<>();
        List<Long> operatorIds = new ArrayList<>();
        Set<Long> vendorIdList = new HashSet<>();
        for (ChoiceItemDO choiceItemDO : pageSearchResult.getData()) {
            choiceIds.add(choiceItemDO.getChoiceLotLibId());
            choiceItemMap.put(choiceItemDO.getItemId(), choiceItemDO);
            operatorIds.add(choiceItemDO.getOperatorId());
            categoryIdList.add(choiceItemDO.getCategoryId());
            vendorIdList.add(choiceItemDO.getVendorId());
        }
        Set<Long> itemIdsIds = choiceItemMap.keySet();
        log.info("commodity_library_export,searchExport. itemIdsIds:{}, choiceIds:{},categoryIdList:{},vendorIdList:{}, operatorIds:{},", itemIdsIds, choiceIds, categoryIdList,vendorIdList,operatorIds);
        return getDisposeData(choiceIds, categoryIdList, itemIdsIds, choiceItemMap, request,vendorIdList,operatorIds);
    }


    private List<ChoiceSearchDistributorMyExcelTemplate> getDisposeData(Set<Long> choiceIds, List<Long> categoryIdList, Set<Long> itemIdsIds, Map<Long, ChoiceItemDO> choiceItemMap, ChoiceItemSearchRequest request, Set<Long> vendorIdList, List<Long> operatorIds) {
        List<ChoiceSearchDistributorMyExcelTemplate> dataInfoListResp = new ArrayList<>();
        List<Item> itemList = itemReadDomainService.findByIdSet(itemIdsIds, request.getTenantId(), null, null);
        Map<Long, Item> itemMap = itemList.stream().collect(Collectors.toMap(Item::getId, Function.identity()));
  /*          // 区域商品
            List<AreaItem> areaItem = areaItemReadDomainService.findByOperatorIdAndItemIds(param.getOperatorId(), itemIdsIds);
            Map<Long, AreaItem> areaItemMap = areaItem.stream().collect(Collectors.toMap(AreaItem::getItemId, Function.identity()));

            // 区域商品sku
            List<AreaSku> areaSkuList = areaSkuReadDomainService.queryByItem(param.getOperatorId(), itemIdsIds);
            Map<Long, AreaSku> areaSkuMap = areaSkuList.stream().collect(Collectors.toMap(AreaSku::getSkuId, Function.identity()));
*/
        // 选品库商品sku
        List<ChoiceLotLibSkuModel> choiceLotLibSkuModelList = choiceLotLibSkuReadService.listByWhere(request.getOperatorId(), null, choiceIds, null, itemIdsIds);

        // 商品sku信息
        List<Sku> skuList = skuReadDomainService.findByItemIdSet(itemIdsIds, request.getTenantId(), null, null);

        //库存

        Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getId, Function.identity()));
        InventoryQueryByEntityAndOperatorIdRequest req = new InventoryQueryByEntityAndOperatorIdRequest();
        req.setSkuIdSet(AssembleDataUtils.list2set(skuList, Sku::getId));
        req.setOperatorIds(operatorIds);
        req.setVendorIdSet(vendorIdList);
        req.setOperatorId(request.getOperatorId());
        log.info("请求库存参数:{}",req);
        if(CollectionUtil.isEmpty(req.getSkuIdSet())){
            throw new ServiceException("我的清单暂时没有商品!");
        }
        List<InventoryEntityResponseInfo> list = Assert.take(ipmInventoryReadFacade.queryByEntityAndOperatorId(req));
        Map<String, InventoryEntityResponseInfo> inventoryEntityResponseInfoMap = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(list)){
            inventoryEntityResponseInfoMap = list.stream().collect(Collectors.toMap(InventoryEntityResponseInfo::getEntityId, Function.identity()));
        }

        // 分类名称
        Map<Long, List<BackCategory>> categoryMap = backCategoryService.batchFindAncestorsOf(categoryIdList);

        // 循环获取模板数据
        int tagNo = 0;
        for(ChoiceLotLibSkuModel choiceLotLibSkuModel : choiceLotLibSkuModelList){
            InventoryEntityResponseInfo entityResponseInfo = inventoryEntityResponseInfoMap.get(choiceLotLibSkuModel.getSkuId().toString());

            ChoiceSearchDistributorMyExcelTemplate template = new ChoiceSearchDistributorMyExcelTemplate();
            template.setId(tagNo++);
            template.setItemId(choiceLotLibSkuModel.getItemId());
            template.setSkuId(choiceLotLibSkuModel.getSkuId());
            log.info("commodity_library_export list_:{}",list);
            if (entityResponseInfo!=null){
                Long realQuantity = entityResponseInfo.getSellableQuantity();
                log.info("commodity_library_export realQuantity:{}",realQuantity);
                template.setStockNum(null==realQuantity?0L:realQuantity);
            }

            log.info("commodity_library_export template realQuantity:{}",template);


            ChoiceItemDO choiceItemDO = choiceItemDispose(choiceItemMap, choiceLotLibSkuModel, template);
            //是否可退
            template.setSupportReturn((choiceItemDO.getSupportReturn() == 1L) ? "是" : "否");//是否可退
            log.info("commodity_library_export,searchExport. choiceItemDO:{} ",choiceItemDO);
            template.setChoiceLotLibName(choiceItemDO.getChoiceLotLibName());
            template.setChoiceLotLibId(choiceItemDO.getChoiceLotLibId());

            // 状态处理
            if (choiceItemDO.getStatus() == 1) {
                template.setStatus("上架");
            } else if (choiceItemDO.getStatus() == -1) {
                template.setStatus("下架");
            } else if (choiceItemDO.getStatus() == -2) {
                template.setStatus("冻结");
            } else if (choiceItemDO.getStatus() == -5) {
                template.setStatus("审核中");
            } else if (choiceItemDO.getStatus() == -3) {
                template.setStatus("删除");
            }else {
                template.setStatus("选品库已过期");
            }
            // 商品处理
            itemDispose(itemMap, template);

            // 规格属性处理
            skuDispose(skuMap, choiceLotLibSkuModel, template);

            // 商品分类处理
            categoryDispose(categoryMap, template, choiceItemDO);

            if (template.getStatus().equals("选品库已过期")){
                template.setPreResellerGrossRate("");
            }else{
                //预估毛利率
                Long rate = choiceLotLibSkuModel.getPreResellerGrossRate() / 100L;
                template.setPreResellerGrossRate(rate+"%");
            }
            dataInfoListResp.add(template);
        }
        log.info("dataInfoListResp:{}",dataInfoListResp);
         dataInfoListResp = new ArrayList<>(dataInfoListResp.stream()
                .collect(Collectors.toMap(ChoiceSearchDistributorMyExcelTemplate::getSkuId, Function.identity(),
                        (existing, replacement) -> existing))
                .values());

        log.info("dataInfoList:{}",dataInfoListResp);
        return dataInfoListResp;
    }

    private void categoryDispose(Map<Long, List<BackCategory>> categoryMap, ChoiceSearchDistributorMyExcelTemplate template, ChoiceItemDO choiceItemDO) {
        if(!categoryMap.containsKey(choiceItemDO.getCategoryId()) || null == choiceItemDO.getCategoryId()){
            return;
        }
        // 分类处理
        List<BackCategory> backCategories = categoryMap.get(choiceItemDO.getCategoryId());
        int categorySize = backCategories.size();
        switch (categorySize) {
            case 1:
                template.setOneLevelCategoryName(backCategories.get(0).getName());
                break;
            case 2:
                template.setOneLevelCategoryName(backCategories.get(0).getName());
                template.setTwoLevelCategoryName(backCategories.get(1).getName());
                break;
            case 3:
                template.setOneLevelCategoryName(backCategories.get(0).getName());
                template.setTwoLevelCategoryName(backCategories.get(1).getName());
                template.setThreeLevelCategoryName(backCategories.get(2).getName());
                break;
            default:
                break;
        }
    }
    private static String converterDateStr(Date date){
        String val="";
        if(date!=null){
            try {
                SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                val=sdf.format(date);
            } catch (Exception e) {
            }
        }
        return val;
    }
    private void skuDispose(Map<Long, Sku> skuMap, ChoiceLotLibSkuModel choiceLotLibSkuModel, ChoiceSearchDistributorMyExcelTemplate template) {
        Sku sku = skuMap.get(choiceLotLibSkuModel.getSkuId());
        template.setWunit("0");
        if(sku == null){
            return;
        }


        if(null != sku.getGw()) {
            BigDecimal gw = new BigDecimal(sku.getGw()).multiply(new BigDecimal(0.001));
            template.setWunit(gw.toString());
        }
        //商品名称
        template.setName(sku.getName());
        template.setCbm("0");
        if(null != sku.getCbm()) {
            BigDecimal cbm = sku.getCbm().multiply(new BigDecimal(0.001));
            template.setCbm(cbm.toString());
        }

        List<SkuAttribute> attributes = sku.getAttributes();
        String keys = "";
        String vals = "";
        if(!CollectionUtils.isEmpty(attributes)){
            for (int i = 0; i < attributes.size(); i++) {
                SkuAttribute attribute = attributes.get(i);
                String attrVal = attribute.getAttrVal();
                String attrKey = attribute.getAttrKey();
                if(i == 0){
                    keys = attrKey;
                    vals = attrVal;
                }else {
                    vals = vals + ";" + attrVal;
                    keys = keys + ";" + attrKey;
                }
            }
        }
        template.setAttrKey(keys);
        template.setAttrVal(vals);

        if(CollectionUtil.isNotEmpty(sku.getExtraPrice()) && sku.getExtraPrice().containsKey("centralizedPurchasePrice")){
            template.setCentralizedPurchasePrice(sku.getExtraPrice().get("centralizedPurchasePrice")/100.0);
        }
    }




    private ChoiceItemDO choiceItemDispose(Map<Long, ChoiceItemDO> choiceItemMap, ChoiceLotLibSkuModel choiceLotLibSkuModel, ChoiceSearchDistributorMyExcelTemplate template) {
        if(!choiceItemMap.containsKey(template.getItemId())){
            return new ChoiceItemDO();
        }
        ChoiceItemDO choiceItemDO = choiceItemMap.get(template.getItemId());
        template.setChoiceName(choiceItemDO.getChoiceLotLibName());
        template.setBrandName(choiceItemDO.getBrandName());
        template.setOriginalPrice(converterAmountStr(choiceLotLibSkuModel.getOriginalPrice()));
        template.setDistributorPrice(converterAmountStr(choiceLotLibSkuModel.getDistributorPrice()));
        template.setMainImage(choiceItemDO.getMainImage());
        // 状态处理
        if (choiceItemDO.getStatus() == 1) {
            template.setStatus("上架");
        } else if (choiceItemDO.getStatus() == -1) {
            template.setStatus("下架");
        } else if (choiceItemDO.getStatus() == -2) {
            template.setStatus("冻结");
        } else if (choiceItemDO.getStatus() == -5) {
            template.setStatus("审核中");
        } else if (choiceItemDO.getStatus() == -3) {
            template.setStatus("供应商删除");
        }else {
            template.setStatus("选品库已过期");
        }
        return choiceItemDO;
    }

    private void itemDispose(Map<Long, Item> itemMap, ChoiceSearchDistributorMyExcelTemplate template) {
        if(!itemMap.containsKey(template.getItemId())){
            return;
        }
        Item item = itemMap.get(template.getItemId());
        // 销售区域处理
        template.setSalesArea("");
        if (!StringUtils.isEmpty(item.getSalesArea())) {
            List<Long> salesAreaIds = Arrays.stream(item.getSalesArea().split(",")).map(Long::valueOf).collect(Collectors.toList());
            List<AddressInfo> addressByIds = Lists.newArrayList();
            //////////////////////////
            if(!CollectionUtils.isEmpty(salesAreaIds)){
                FindAddressByIdsRequest findAddressByIdsRequest = new FindAddressByIdsRequest();
                findAddressByIdsRequest.setIds(salesAreaIds);
                Response<List<AddressInfo>> addressInfos = addressReadFacade.findByIds(findAddressByIdsRequest);
                if (addressInfos.isSuccess() && !CollectionUtils.isEmpty(addressInfos.getResult())) {
                    addressByIds.addAll(addressInfos.getResult());
                }
            }
            //////////////////////////
            List<String> salesAreaNameList = addressByIds.stream().map(AddressInfo::getName).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(salesAreaNameList)) {
                template.setSalesArea(Joiner.on(",").join(salesAreaNameList));
            }
        }
        if (item.getType() == 1) {
            template.setItemType("普通商品");
        } else if (item.getType() == 6) {
            template.setItemType("电子卡券");
        }
    }

    public String converterAmountStr(Long amount){
        String val="";
        if(amount!=null){
            BigDecimal num=new BigDecimal(amount);
            BigDecimal rate=new BigDecimal(100);
            val=num.divide(rate).toString();
        }
        return val;
    }
}

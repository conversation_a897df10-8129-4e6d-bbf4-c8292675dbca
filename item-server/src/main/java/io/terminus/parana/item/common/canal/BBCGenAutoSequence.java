package io.terminus.parana.item.common.canal;

import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
@Slf4j
public class BBCGenAutoSequence {
	private static long serial = 1;
	private static String localHostIp = "";
	private static String MyIP = "";

	static {
		String tmpip = "";
		try {
			Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
			while (netInterfaces.hasMoreElements()) {
				NetworkInterface ni = netInterfaces.nextElement();
				Enumeration<InetAddress> ips = ni.getInetAddresses();
				while (ips.hasMoreElements()) {
					String ip = ips.nextElement().getHostAddress();
					tmpip = ip;
					if (ip.startsWith("172.")) {
						MyIP = ip;
						break;
					}
				}
			}
		} catch (SocketException e) {
			log.error(Throwables.getStackTraceAsString(e));
		}
		if (MyIP == null || MyIP.length() == 0){
			MyIP = tmpip;
		}
	}
	
	public static String getLocalIP() {
		return MyIP;
	}
	
	private static String getPID() {
		String pid = System.getProperty("pid");
		if (pid == null) {
			// first, reliable with sun jdk (http://golesny.de/wiki/code:javahowtogetpid)
			final RuntimeMXBean rtb = ManagementFactory.getRuntimeMXBean();
			final String processName = rtb.getName();
			/* tested on: */
			/* - windows xp sp 2, java 1.5.0_13 */
			/* - mac os x 10.4.10, java 1.5.0 */
			/* - debian linux, java 1.5.0_13 */
			/* all return pid@host, e.g 2204@antonius */

			if (processName.indexOf('@') != -1) {
				pid = processName.substring(0, processName.indexOf('@'));
			} else {
				pid = "0";
			}
			System.setProperty("pid", pid);
		}
		return pid;
	}
	
	private synchronized static long getSerialID() {
		long sn = serial;
		
		serial++;

		if (serial >= 999999999)
			serial = 1;
		
		return sn;
	}

	public static void main(String[] args) {
		log.info(getNextval("PANARA_SKU"));
	}
	
	//应用模块的名称[英文]
	public static String getNextval(String appName) {
		if(appName == null)
			appName = "";
		appName = appName.trim().toUpperCase();
		
		if(localHostIp == null || localHostIp.length() == 0) {
			localHostIp = MyIP.replaceAll("\\.", "_") + "_" + getPID();
		}
		
		try {
			Date dd = new Date();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
			String sdate = sdf.format(dd);

			java.text.DecimalFormat format = new java.text.DecimalFormat("000000000");
			String keyID = sdate + format.format( getSerialID() );
			
			return localHostIp + appName + keyID;
		} catch (Exception e) {
			log.error(Throwables.getStackTraceAsString(e));
		}

		return localHostIp + appName + "20250215155322000000001";
	}
}

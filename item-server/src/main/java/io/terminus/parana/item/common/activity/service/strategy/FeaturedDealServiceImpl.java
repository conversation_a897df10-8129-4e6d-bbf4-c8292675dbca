package io.terminus.parana.item.common.activity.service.strategy;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.activity.api.bean.request.CommonActivityAddRequest;
import io.terminus.parana.item.common.activity.api.bean.request.CommonActivityUpdateRequest;
import io.terminus.parana.item.common.activity.api.converter.CommonActivityItemConverter;
import io.terminus.parana.item.common.activity.enums.CommonActivityStatus;
import io.terminus.parana.item.common.activity.enums.CommonActivityType;
import io.terminus.parana.item.common.activity.manager.CommonActivityManager;
import io.terminus.parana.item.common.activity.model.CommonActivityGoodsBinding;
import io.terminus.parana.item.common.activity.model.CommonActivityManage;
import io.terminus.parana.item.common.activity.service.CommonActivityManageService;
import io.terminus.parana.item.common.spi.IdGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 创建/更新活动：今日优惠
 *
 * <AUTHOR>
 * @date 2021-07-10
 */
@Component
@RequiredArgsConstructor
public class FeaturedDealServiceImpl implements AbstractCommonActivityStrategy {

    private final IdGenerator idGenerator;
    private final CommonActivityManager commonActivityManger;
    private final CommonActivityManageService commonActivityManageService;

    private final CommonActivityItemConverter commonActivityItemConverter;

    @Override
    public boolean create(CommonActivityAddRequest request) {
        // 校验起止时间内是否已有活动
        checkTimeValid(request.getCommonType(), request.getOperatorId(), request.getStartAt(), request.getExpiredAt(), null);

        // 常用活动信息
        Date date = new Date();
        CommonActivityManage commonActivityManage = new CommonActivityManage();
        BeanUtils.copyProperties(request, commonActivityManage);
        Long commonActivityManageId = idGenerator.nextValue(CommonActivityManage.class);
        commonActivityManage.setId(commonActivityManageId);
        commonActivityManage.setOperatorId(request.getOperatorId());
        commonActivityManage.setUpdatedBy(request.getUserId());
        commonActivityManage.setCreatedAt(date);
        commonActivityManage.setUpdatedAt(date);
        commonActivityManage.setStatus(CommonActivityStatus.OPEN.getValue());

        // 活动关联商品信息
        List<String> itemIds = request.getItemIds();
        List<CommonActivityGoodsBinding> commonActivityGoodsBindings = new ArrayList<>(itemIds.size());
        if (!CollectionUtils.isEmpty(itemIds)) {
            commonActivityGoodsBindings = commonActivityItemConverter.packItemBindInfo(itemIds, commonActivityManageId, date, request.getOperatorId());
        }

        return commonActivityManger.createCommonActivityAndItem(commonActivityManage, commonActivityGoodsBindings, null);
    }

    @Override
    public boolean isSupport(String type) {
        return Objects.equals(type, CommonActivityType.FEATURED_DEAL.getKey());
    }

    @Override
    public boolean update(CommonActivityUpdateRequest request) {
        // 校验起止时间内是否已有活动
        checkTimeValid(request.getCommonType(), request.getOperatorId(), request.getStartAt(), request.getExpiredAt(), request.getId());

        // 常用活动信息
        Date date = new Date();
        CommonActivityManage commonActivityManage = new CommonActivityManage();
        BeanUtils.copyProperties(request, commonActivityManage);
        commonActivityManage.setUpdatedBy(request.getUserId());
        commonActivityManage.setUpdatedAt(date);

        // 更新商品
        List<CommonActivityGoodsBinding> goodsBindList = new ArrayList<>(10);
        if (request.getIsUpdateItem()) {
            List<String> itemIds = request.getItemIds();
            goodsBindList = commonActivityItemConverter.packItemBindInfo(itemIds, request.getId(), date, request.getOperatorId());
        }

        return commonActivityManger.updateCommonActivityAndItem(commonActivityManage, goodsBindList, null);
    }

    /**
     * 校验起止时间内是否已有活动
     *
     * @param commonType 活动类型
     * @param operatorId 区域运营ID
     * @param startAt 开始时间
     * @param expiredAt 结束时间
     * @param excludeId 不需要查询的ID
     */
    private void checkTimeValid(String commonType, Long operatorId, Date startAt, Date expiredAt, Long excludeId) {
        List<Long> commonActivityIds = commonActivityManageService.checkTimeRangeValidActive(commonType, operatorId, startAt, expiredAt, excludeId);
        if (!CollectionUtils.isEmpty(commonActivityIds)) {
            throw new ServiceException("该起止时间范围内已存在其他活动，请选择别的时间试试吧");
        }
    }

}

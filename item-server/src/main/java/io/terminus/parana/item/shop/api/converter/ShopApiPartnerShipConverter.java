package io.terminus.parana.item.shop.api.converter;

import io.terminus.parana.item.common.base.JsonSupport;
import io.terminus.parana.item.item.enums.ItemBrandString;
import io.terminus.parana.item.partnership.api.bean.response.VendorPartnershipInfo;
import io.terminus.parana.item.partnership.api.bean.response.VendorWithPartnerShipInfo;
import io.terminus.parana.item.shop.api.bean.response.ShopInfo;
import io.terminus.parana.item.shop.enums.ShopType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * @author: lql
 * @version: 2020/8/26 4:22 下午
 */
@Slf4j
@Component
public class ShopApiPartnerShipConverter implements JsonSupport {

    public VendorWithPartnerShipInfo domain2info(VendorPartnershipInfo partnership, ShopInfo shopInfo) {

        VendorWithPartnerShipInfo vendor = new VendorWithPartnerShipInfo();

        /* ------------------partnership----------------------------- */
        vendor.setId(partnership.getId());
        vendor.setVendorId(partnership.getVendorId());
        vendor.setOperatorId(partnership.getOperatorId());
        vendor.setCooperationMode(partnership.getCooperationMode());
        vendor.setLogisticsMode(partnership.getLogisticsMode());
        vendor.setWarehouseCode(partnership.getWarehouseCode());
        vendor.setWarehouseCode(partnership.getWarehouseCode());
        vendor.setStatus(partnership.getStatus());
        vendor.setFeeRate(partnership.getFeeRate());
        vendor.setSettlementPeriod(partnership.getSettlementPeriod());
        vendor.setCreatedAt(partnership.getCreatedAt());
        vendor.setUpdatedAt(partnership.getUpdatedAt());
        vendor.setUpdatedBy(partnership.getUpdatedBy());
        vendor.setChannelType(partnership.getChannelType());
        vendor.setChannelItemAmount(partnership.getChannelItemAmount());
        vendor.setChannelItemId(partnership.getChannelItemId());
        vendor.setExtraJson(partnership.getExtraJson());
        /* --------------------------------------------------------- */
        vendor.setName(shopInfo.getName());
        vendor.setAddress(shopInfo.getAddress());
        vendor.setShopNames(shopInfo.getShopNames());


        if (shopInfo.getType() != null && ShopType.AREA_OPERATOR.getValue() == shopInfo.getType().intValue()) {
            vendor.setOperatorCreateBy(shopInfo.getCreatedBy());
        }else{
            vendor.setVendorCreatedBy(shopInfo.getCreatedBy());
        }

        Map<String, String> extra = shopInfo.getExtra();
        if (!CollectionUtils.isEmpty(extra)) {
            if (shopInfo.getType() != null && ShopType.AREA_OPERATOR.getValue() == shopInfo.getType().intValue()) {
                vendor.setSalesAreaName(extra.get("salesAreaName"));
                vendor.setIndustryId(shopInfo.getIndustryId());
            }
            vendor.setContactName(extra.get("contactName"));
            vendor.setContactMobile(extra.get("contactMobile"));

        }

        ItemBrandString isBrandEnum = ItemBrandString.fromValue(shopInfo.getIsBrand());
        if(null != isBrandEnum){
            vendor.setIsBrand(isBrandEnum.getDescription());
        }else{
            vendor.setIsBrand(shopInfo.getIsBrand());
        }

        ItemBrandString isDelieryEnum = ItemBrandString.fromValue(shopInfo.getIsDeliery());
        if (null != isDelieryEnum){
            vendor.setIsDeliery(isDelieryEnum.getDescription());
        }else{
            vendor.setIsDeliery(shopInfo.getIsDeliery());
        }

        vendor.setIsZq(shopInfo.getIsZq());

        return vendor;
    }
}

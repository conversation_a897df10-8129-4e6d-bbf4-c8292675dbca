package io.terminus.parana.item.shop.api.facade;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.item.partnership.api.bean.request.PartnershipVendorListPagingRequest;
import io.terminus.parana.item.partnership.api.bean.response.VendorWithPartnerShipInfo;
import io.terminus.parana.item.partnership.service.VendorPartnershipReadDomainService;
import io.terminus.parana.item.shop.api.bean.request.ShopSupplierRequest;
import io.terminus.parana.item.shop.api.bean.request.SupplierInfoPageRequest;
import io.terminus.parana.item.shop.api.bean.request.SupplierInfoQueryListRequest;
import io.terminus.parana.item.shop.api.bean.request.SupplierInfoQueryRequest;
import io.terminus.parana.item.shop.api.bean.response.ShopSupplierInfo;
import io.terminus.parana.item.shop.api.bean.response.SupplierInfoInfoResponse;
import io.terminus.parana.item.shop.api.converter.SupplierInfoApiConverter;
import io.terminus.parana.item.shop.model.SupplierInfoModel;
import io.terminus.parana.item.shop.service.SupplierInfoReadService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SupplierInfoReadFacadeImpl implements SupplierInfoReadFacade {

	private final SupplierInfoReadService supplierInfoReadService;
	private final SupplierInfoApiConverter supplierInfoApiConverter;
	private final VendorPartnershipReadDomainService vendorPartnershipReadDomainService;

	@Override
	public Response<SupplierInfoInfoResponse> view(SupplierInfoQueryRequest request) {
		SupplierInfoModel supplierInfoModel = supplierInfoApiConverter.get(request);
		supplierInfoModel.setTenantId(RequestContext.getTenantId().longValue());
		SupplierInfoModel model = supplierInfoReadService.view(supplierInfoModel);
		if(model == null){
			model = new SupplierInfoModel();
		}
		SupplierInfoInfoResponse supplierInfoInfoResponse = supplierInfoApiConverter.model2InfoResponse(model);
		supplierInfoInfoResponse.setContactAddress(model.getConcactAddress());
		supplierInfoInfoResponse.setContactName(model.getConcactName());
		supplierInfoInfoResponse.setContactMobile(model.getConcactMobile());
		supplierInfoInfoResponse.setContactCode(model.getConcactCode());
		supplierInfoInfoResponse.setExtraJson(model.getExtraJson());
		return Response.ok(supplierInfoInfoResponse);
	}

	@Override
	public Response<List<SupplierInfoInfoResponse>> list(SupplierInfoQueryListRequest request) {
		request.setTenantId(RequestContext.getTenantId());
		List<SupplierInfoModel> modelList = supplierInfoReadService.list(request);
		if(modelList == null){
			modelList = new ArrayList<>();
		}
		return Response.ok(supplierInfoApiConverter.modelList2InfoResponseList(modelList));
	}

	@Override
	public Response<Paging<SupplierInfoInfoResponse>> page(SupplierInfoPageRequest request) {
		request.setTenantId(RequestContext.getTenantId());
		Map<String, Object> params = JSONUtil.parseObj(request);
		PageInfo pageInfo = new PageInfo(request.getPageNo(), request.getPageSize());
		params.put("operatorId",request.getOperatorId());
		Paging<SupplierInfoModel> modelPage = supplierInfoReadService.page(params, pageInfo.getOffset(), pageInfo.getLimit());
		return Response.ok(supplierInfoApiConverter.modePage2InfoPage(modelPage));
	}

	@Override
	public Response<Paging<ShopSupplierInfo>> pageSeller(ShopSupplierRequest request) {
		Map<String, Object> params = JSONUtil.parseObj(request);
		PageInfo pageInfo = new PageInfo(request.getPageNo(), request.getPageSize());
		Paging<ShopSupplierInfo> infoPaging = new Paging<>();

		Paging<SupplierInfoModel> paging = supplierInfoReadService.selSeller(params, pageInfo.getOffset(), pageInfo.getLimit());
		for (SupplierInfoModel supplier : paging.getData()) {
			// 对每个 SupplierInfoModel 进行操作
			ShopSupplierInfo shopSupplierInfo = new ShopSupplierInfo();
			shopSupplierInfo.setApplyName(supplier.getApplyName());
			shopSupplierInfo.setApplyPhone(supplier.getApplyPhone());
			shopSupplierInfo.setBillingCycle(supplier.getBillingCycle());
			shopSupplierInfo.setEnterpriseBusinessAddress(supplier.getEnterpriseBusinessAddress());
		}
		return null;
	}

	@Override
	public SupplierInfoInfoResponse queryByOperation(Long id) {
		SupplierInfoModel supplierInfoModel = supplierInfoReadService.queryByOperation(id);
		if(supplierInfoModel == null){
			return null;
		} else {
			SupplierInfoInfoResponse supplierInfoInfoResponse = supplierInfoApiConverter.model2InfoResponse(supplierInfoModel);
			supplierInfoInfoResponse.setContactAddress(supplierInfoModel.getConcactAddress());
			supplierInfoInfoResponse.setContactCode(supplierInfoModel.getConcactCode());
			supplierInfoInfoResponse.setContactMobile(supplierInfoModel.getConcactMobile());
			supplierInfoInfoResponse.setContactName(supplierInfoModel.getConcactName());
			supplierInfoInfoResponse.setExtraJson(supplierInfoModel.getExtraJson());
			return supplierInfoInfoResponse;
		}

	}

	@Override
	public SupplierInfoInfoResponse queryByTenant(Long id) {
		SupplierInfoModel supplierInfoModel = supplierInfoReadService.queryByTenant(id);
		if (supplierInfoModel == null) {
			return null;
		} else {
			return supplierInfoApiConverter.model2InfoResponse(supplierInfoModel);
		}
	}

	@Override
	public Response<List<SupplierInfoInfoResponse>> listByRequest(SupplierInfoQueryListRequest request) {
		request.setTenantId(RequestContext.getTenantId());
		List<SupplierInfoModel> modelList = supplierInfoReadService.listByRequest(request);
		if(modelList == null){
			modelList = new ArrayList<>();
		}
		return Response.ok(supplierInfoApiConverter.modelList2InfoResponseList(modelList));
	}

	@Override
	public Response<Paging<VendorWithPartnerShipInfo>> getShopAuditPage(PartnershipVendorListPagingRequest request) {


		List<VendorWithPartnerShipInfo> vendorWithPartnerShipInfos = new ArrayList<>();

		Map<String, Object> params = JSONUtil.parseObj(request);
		PageInfo pageInfo = new PageInfo(request.getPageNo(), request.getPageSize());
		params.put("operatorId",request.getOperatorId());
		Paging<SupplierInfoModel> modelPage = supplierInfoReadService.page(params, pageInfo.getOffset(), pageInfo.getLimit());

		if (CollUtil.isEmpty(modelPage.getData())){
			return Response.ok(new Paging<>());
		}


		List<SupplierInfoModel> data = modelPage.getData();

		Set<Long> vendorIdSet = modelPage.getData().stream().map(SupplierInfoModel::getId).collect(Collectors.toSet());

		Map<Long, VendorWithPartnerShipInfo> vendorWithPartnerShipInfoMap = new HashMap<>();

		PartnershipVendorListPagingRequest listPagingRequest = new PartnershipVendorListPagingRequest();
		listPagingRequest.setVendorIds(vendorIdSet);
		listPagingRequest.setOperatorId(request.getOperatorId());
		List<VendorWithPartnerShipInfo> list = vendorPartnershipReadDomainService.getList(listPagingRequest);
		if (CollUtil.isNotEmpty(list)){
			vendorWithPartnerShipInfoMap = list.stream().collect(Collectors.toMap(VendorWithPartnerShipInfo::getVendorId, Function.identity(), (x, y) -> x));
		}

		for (SupplierInfoModel model:data){
			VendorWithPartnerShipInfo vendorWithPartnerShipInfo = new VendorWithPartnerShipInfo();
			vendorWithPartnerShipInfo.setVendorId(model.getId());
			vendorWithPartnerShipInfo.setApplyName(model.getApplyName());
			vendorWithPartnerShipInfo.setApplyPhone(model.getApplyPhone());
			vendorWithPartnerShipInfo.setAuditEndTime(model.getAuditEndTime());
			vendorWithPartnerShipInfo.setAuditOpinion(model.getAuditOpinion());
			vendorWithPartnerShipInfo.setAuditStatus(model.getAuditStatus());
			vendorWithPartnerShipInfo.setBillingCycle(model.getBillingCycle());
			vendorWithPartnerShipInfo.setContactMobile(model.getConcactMobile());
			vendorWithPartnerShipInfo.setContactName(model.getConcactName());
			vendorWithPartnerShipInfo.setContract(model.getContract());
			vendorWithPartnerShipInfo.setEnterpriseBusinessAddress(model.getEnterpriseBusinessAddress());
			vendorWithPartnerShipInfo.setEnterpriseBusinessAddressCode(model.getEnterpriseBusinessAddressCode());

			vendorWithPartnerShipInfo.setName(model.getEnterpriseNameAbbreviation());
			vendorWithPartnerShipInfo.setShopNames(model.getEnterpriseName());
			vendorWithPartnerShipInfo.setAppliedTime(model.getAppliedTime());
			vendorWithPartnerShipInfo.setSettleStatus(model.getSettleStatus());

			VendorWithPartnerShipInfo partnerShipInfo = vendorWithPartnerShipInfoMap.get(model.getId());
			if (partnerShipInfo!=null){
				vendorWithPartnerShipInfo.setId(partnerShipInfo.getId());
			}

			vendorWithPartnerShipInfos.add(vendorWithPartnerShipInfo);
		}
		return Response.ok(new Paging<>(modelPage.getTotal(),vendorWithPartnerShipInfos));
	}

}

package io.terminus.parana.item.open.service;

import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.item.model.FullItemBO;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.item.repository.SkuDao;
import io.terminus.parana.item.open.bo.OpenPagingCriteriaBO;
import io.terminus.parana.item.open.repository.ItemOpenDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-11-11
 */
@Slf4j
@Service
public class ItemOpenReadDomainService extends AbsServiceBase {

    private final ItemOpenDao itemOpenDao;
    private final SkuDao skuDao;

    public ItemOpenReadDomainService(ItemOpenDao itemOpenDao, SkuDao skuDao) {
        this.itemOpenDao = itemOpenDao;
        this.skuDao = skuDao;
    }

    private List<FullItemBO> assembleData(List<Item> itemList) {
        Set<Long> itemIdSet = AssembleDataUtils.list2set(itemList, Item::getId);
        List<Sku> skuList = skuDao.findByItemIdSet(itemIdSet);

        Map<Long, List<Sku>> skuGroupByItemMap = AssembleDataUtils.list2group(skuList, Sku::getItemId);

        ArrayList<FullItemBO> resultList = new ArrayList<>(itemList.size());
        for (Item item : itemList) {
            FullItemBO bo = new FullItemBO();
            bo.setItem(item);
            bo.setSkuList(skuGroupByItemMap.get(item.getId()));
            resultList.add(bo);
        }

        return resultList;
    }

    public List<FullItemBO> pagingOfVernier(OpenPagingCriteriaBO criteriaBO) {
        try {
            List<Item> itemList = itemOpenDao.openPagingOfVernier(criteriaBO);
            if (CollectionUtils.isEmpty(itemList)) {
                return Collections.emptyList();
            }

            return assembleData(itemList);
        } catch (Exception e) {
            log.error("fail to paging with: {}, cause: {}", criteriaBO, printErrorStack(e));
            throw new ServiceException("item.open.paging.fail");
        }
    }

    public Paging<FullItemBO> paging(OpenPagingCriteriaBO criteriaBO) {
        try {
            Paging<Item> paging = itemOpenDao.openPaging(criteriaBO);
            if (paging.isEmpty()) {
                return Paging.empty();
            }

            List<FullItemBO> resultList = assembleData(paging.getData());
            return new Paging<>(paging.getTotal(), resultList);
        } catch (Exception e) {
            log.error("fail to paging with: {}, cause: {}", criteriaBO, printErrorStack(e));
            throw new ServiceException("item.open.paging.fail");
        }
    }
}

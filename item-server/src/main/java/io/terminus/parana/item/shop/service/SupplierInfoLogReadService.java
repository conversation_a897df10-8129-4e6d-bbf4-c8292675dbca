package io.terminus.parana.item.shop.service;

import java.util.List;
import java.util.Map;

import io.terminus.parana.item.shop.model.SupplierInfoLogModel;
import io.terminus.parana.item.shop.repository.SupplierInfoLogDao;
import org.springframework.stereotype.Component;


import io.terminus.common.model.Paging;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class SupplierInfoLogReadService {

	private final SupplierInfoLogDao supplierInfoLogDao;
	/**
	 * 查询供应商详情日志表
	 */
	public SupplierInfoLogModel view(SupplierInfoLogModel model) {
		return supplierInfoLogDao.queryOne(model);
	}
	/**
	 * 查询供应商详情日志表列表
	 */
	public List<SupplierInfoLogModel> list(SupplierInfoLogModel model) {
		return supplierInfoLogDao.listByModel(model);
	}
	/**
	 * 分页查询供应商详情日志表列表
	 */
	public Paging<SupplierInfoLogModel> page(Map<String, Object> params, Integer offset, Integer limit) {
		return supplierInfoLogDao.page(params, offset, limit);
	}

	/**
	 * 查询供应商详情日志表
	 */
	public SupplierInfoLogModel getViewByVendorId(Long vendorId) {
		return supplierInfoLogDao.getViewByVendorId(vendorId);
	}

}

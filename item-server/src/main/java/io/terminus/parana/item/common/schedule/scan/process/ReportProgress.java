package io.terminus.parana.item.common.schedule.scan.process;

import java.util.function.Consumer;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-09-04
 */
public interface ReportProgress {

    /**
     * 注册进度回调方法
     *
     * @param callback 回调方法
     */
    void registerProgressCallback(Consumer<Long> callback);

    /**
     * 设置进度报告步长
     *
     * @param length 步长
     */
    void setReportLength(int length);
}

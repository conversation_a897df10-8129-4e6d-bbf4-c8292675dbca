package io.terminus.parana.item.common.schedule.scan.process;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.util.List;

/**
 * 桥接交换区数据
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-29
 */
@Getter
public class BridgeExchangeData<T> {

    private static final BridgeExchangeData<Object> EMPTY = new BridgeExchangeData<>(false, null, 0);

    @ApiModelProperty("工作是否可以结束了")
    private final Boolean done;

    @ApiModelProperty("数据集合")
    private final List<T> elements;

    @ApiModelProperty("数据个数")
    private final Integer size;

    private BridgeExchangeData(Boolean done, List<T> elements, Integer size) {
        this.done = done;
        this.elements = elements;
        this.size = size;
    }

    public static <T> BridgeExchangeData<T> make(<PERSON><PERSON><PERSON> done, List<T> elements, Integer size) {
        return new BridgeExchangeData<>(done, elements, size);
    }

    public static <T> BridgeExchangeData<T> empty() {
        return (BridgeExchangeData<T>) EMPTY;
    }
}

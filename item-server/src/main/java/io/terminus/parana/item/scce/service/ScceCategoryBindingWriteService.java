package io.terminus.parana.item.scce.service;


import io.terminus.parana.item.scce.dao.ScceCategoryBindingDao;
import io.terminus.parana.item.scce.model.ScceCategoryBindingModel;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ScceCategoryBindingWriteService {

	private final ScceCategoryBindingDao scceCategoryBindingDao;
	/**
	 * 创建
	 */
	public Boolean create(ScceCategoryBindingModel model) {
		return scceCategoryBindingDao.createModel(model);
	}
	/**
	 * 修改
	 */
	public Boolean update(ScceCategoryBindingModel model) {
		return scceCategoryBindingDao.updateModel(model);
	}
}

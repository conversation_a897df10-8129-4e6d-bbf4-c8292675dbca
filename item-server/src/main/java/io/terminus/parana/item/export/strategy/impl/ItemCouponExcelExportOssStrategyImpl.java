package io.terminus.parana.item.export.strategy.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.card.api.bean.request.ItemCouponPagingRequest;
import io.terminus.parana.item.card.api.bean.response.ItemCouponInfo;
import io.terminus.parana.item.card.api.facade.ItemCouponReadFacade;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.export.dto.ItemCouponExcelTemplate;
import io.terminus.parana.item.export.dto.ItemSearchShopExcelTemplate;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;


@Service
@Slf4j
public class ItemCouponExcelExportOssStrategyImpl implements ExcelExportStrategy {

    @Autowired
    private ExcelExportHelper excelExportHelper;

    @Autowired
    private ThirdPartyRegistry registry;

    @Override
    public String getType() {
        return ExcelExportType.ITEM_COUPON_EXPORT.getReportType();
    }


    @Autowired
    private ItemCouponReadFacade itemCouponReadFacade;



    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        ItemCouponPagingRequest request = JSON.parseObject(requestJson, ItemCouponPagingRequest.class);
        log.info("item.coupin.export,request:{}", request);
        //组装完的表格数据
        List<ItemCouponExcelTemplate> excelDataList = excelDataList(request);
        log.info("组装数据结果集：" + excelDataList);
        //组装完的搜索表头
        //List<Object> searchHead = searchHead(itemSearchRequest);
        //log.info("搜索表头结果集：" + searchHead);
        log.info("item.export,导出结果条数：" + excelDataList.size());
        Long endTime = System.currentTimeMillis();
        log.info("item.export.data.build,time:{}", endTime - beginTime);

        String filePath = null;
        InputStream is = null;
        try {
            ByteArrayOutputStream outputStream = null;
            if (excelDataList.isEmpty()) {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(ItemCouponExcelTemplate.class);
            } else {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(excelDataList, ItemCouponExcelTemplate.class);
            }
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            log.info("filePath：{}", filePath);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("item.coupon.export, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("item.coupon.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("item.coupon.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }

    public List<ItemCouponInfo> searchExport(ItemCouponPagingRequest request) {
        List<ItemCouponInfo> dataInfoListResp = new ArrayList<>();
        int pageNo = 1;
        request.setPageNo(pageNo);
        request.setPageSize(500);
        while (true) {
            Paging<ItemCouponInfo> paging = Assert.take(itemCouponReadFacade.paging(request));
            if (CollectionUtils.isEmpty(paging.getData())) {
                break;
            }
            dataInfoListResp.addAll(paging.getData());
            pageNo++;
            request.setPageNo(pageNo);
        }
        return dataInfoListResp;
    }

    public List<ItemCouponExcelTemplate> excelDataList(ItemCouponPagingRequest request) {
        List<ItemCouponInfo> dataInfoList = searchExport(request);
        List<ItemCouponExcelTemplate> excelDataList = new ArrayList<>();
        for (ItemCouponInfo info : dataInfoList) {
            ItemCouponExcelTemplate data = JSON.parseObject(JSON.toJSONString(info), ItemCouponExcelTemplate.class);
            data.setStatus(info.getStatus()==1?"有效":"失效");
            data.setCompanyName(info.getCompanyId()==1?"品诺":"通卡数科");
            if(info.getBasePrice() != null){
                data.setBasePrice(info.getBasePrice()/100.0 + "");
            }
            if(info.getOriginalPrice() != null){
                data.setOriginalPrice(info.getOriginalPrice()/100.0 + "");
            }
            if(info.getCentralizedPurchasePrice() != null){
                data.setCentralizedPurchasePrice(info.getCentralizedPurchasePrice()/100.0 + "");
            }
            excelDataList.add(data);
        }
        return excelDataList;
    }

}
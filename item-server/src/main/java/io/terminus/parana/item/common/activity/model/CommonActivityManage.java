package io.terminus.parana.item.common.activity.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CommonActivityManage implements Serializable {

    private static final long serialVersionUID = 1097976516448300942L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("常用活动名称")
    private String commonName;

    @ApiModelProperty("常用活动类型（智能推荐、今日优惠、为你推荐）")
    private String commonType;

    @ApiModelProperty("状态（-2、已删除，-1、已失效，0、未开始，1、进行中，2、已停止）")
    private Integer status;

    @ApiModelProperty("活动开始时间")
    private Date startAt;

    @ApiModelProperty("活动结束时间")
    private Date expiredAt;

    @ApiModelProperty("创建时间")
    private Date createdAt;

    @ApiModelProperty("更新时间")
    private Date updatedAt;

    @ApiModelProperty("更新者id")
    private Long updatedBy;

    @ApiModelProperty("区域运营ID")
    private Long operatorId;

}

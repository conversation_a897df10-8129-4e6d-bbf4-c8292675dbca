package io.terminus.parana.item.shop;

import io.terminus.parana.item.common.spi.IdGeneratorRegister;
import io.terminus.parana.item.shop.common.ShopIdAssembleStrategy;
import io.terminus.parana.item.shop.extension.DefaultShopDomainWriteExtension;
import io.terminus.parana.item.shop.extension.ShopDomainWriteExtension;
import io.terminus.parana.item.shop.model.Shop;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">Caily</a>
 * @date 2018-05-22
 */
@Configuration
public class ShopCoreAutoConfig implements InitializingBean {

    @Autowired
    private IdGeneratorRegister idGeneratorRegister;

    @Override
    public void afterPropertiesSet() {
        idGeneratorRegister.registerAssembleStrategy(Shop.class, new ShopIdAssembleStrategy());
    }

    @Bean
    @ConditionalOnMissingBean
    public ShopDomainWriteExtension shopDomainWriteExtension() {
        return new DefaultShopDomainWriteExtension();
    }

}

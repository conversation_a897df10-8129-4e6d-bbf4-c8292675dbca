package io.terminus.parana.item.open.repository;

import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.open.model.AppleItemCategoryModel;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AppleItemCategoryDao extends MyBatisDao<AppleItemCategoryModel> {

    public AppleItemCategoryModel queryByCategoryId(Long categoryId) {
        return this.sqlSession.selectOne("queryByCategoryId",categoryId);
    }

    @Override
    public Boolean update(AppleItemCategoryModel model) {
        return this.sqlSession.update("updateOne", model) >= 0;
    }

    public List<AppleItemCategoryModel> listUpAll() {
        return this.sqlSession.selectList("listUpAll");
    }

    public List<AppleItemCategoryModel> listChildren() {
        return this.sqlSession.selectList("listChildren");
    }
}

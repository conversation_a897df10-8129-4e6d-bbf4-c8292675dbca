package io.terminus.parana.item.shop.api.converter;

import java.util.List;
import java.util.Map;

import io.terminus.parana.item.shop.api.bean.request.*;
import io.terminus.parana.item.shop.api.bean.response.SupplierInfoLogInfoResponse;
import io.terminus.parana.item.shop.model.SupplierInfoLogModel;
import org.mapstruct.Mapper;


import io.terminus.common.model.Paging;

@Mapper(componentModel = "spring")
public interface SupplierInfoLogApiConverter {

	SupplierInfoLogModel get(SupplierInfoLogCreateRequest request);

	SupplierInfoLogModel get(SupplierInfoLogUpdateRequest request);

	SupplierInfoLogModel get(SupplierInfoLogDeleteRequest request);

	SupplierInfoLogModel get(SupplierInfoLogQueryRequest request);

	SupplierInfoLogModel get(SupplierInfoLogPageRequest request);

	SupplierInfoLogInfoResponse model2InfoResponse(SupplierInfoLogModel model);

	List<SupplierInfoLogInfoResponse> modelList2InfoResponseList(List<SupplierInfoLogModel> modelList);

	Paging<SupplierInfoLogInfoResponse> modePage2InfoPage(Paging<SupplierInfoLogModel> model);
}

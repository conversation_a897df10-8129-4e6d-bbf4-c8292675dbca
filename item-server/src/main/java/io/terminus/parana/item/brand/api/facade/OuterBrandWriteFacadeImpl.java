package io.terminus.parana.item.brand.api.facade;

import com.google.common.base.Throwables;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandCreateRequest;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandDeleteRequest;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandExcelImportRequest;
import io.terminus.parana.item.brand.api.bean.request.OuterBrandUpdateRequest;
import io.terminus.parana.item.brand.api.service.OuterBrandWriteService;
import io.terminus.parana.item.category.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class OuterBrandWriteFacadeImpl implements OuterBrandWriteFacade {

    @Autowired
    private OuterBrandWriteService outerservice;

    @Override
    public Response<Long> createOuterBrand(OuterBrandCreateRequest paranaOuterBrand) {
        paranaOuterBrand.setOperatorId(Constant.ZQ_DEFAULT_OPERATOR_ID);
        paranaOuterBrand.checkParam();
        try {
            return Response.ok(outerservice.create(paranaOuterBrand)) ;
        }catch (ServiceException e){
            return Response.fail(e.getMessage());
        }catch (Exception e){
            log.error("创建外部品牌失败！ param:{} ,case:{}",paranaOuterBrand, Throwables.getStackTraceAsString(e));
            return Response.fail("创建外部品牌失败！");
        }
    }

    @Override
    public Response<Boolean> deleteOuterBrand(OuterBrandDeleteRequest paranaOuterBrand) {
        try {
            paranaOuterBrand.setOperatorId(Constant.ZQ_DEFAULT_OPERATOR_ID);
            return Response.ok(outerservice.delete(paranaOuterBrand));
        }catch (ServiceException e){
            return Response.fail(e.getMessage());
        }catch (Exception e){
            log.error("删除外部品牌失败！ param:{} ,case:{}",paranaOuterBrand, Throwables.getStackTraceAsString(e));
            return Response.fail("创建外部品牌失败！");
        }
    }



    @Override
    public Response<Boolean> updateOuterBrand(OuterBrandUpdateRequest outerBrandUpdateRequest) {

        return Response.ok(outerservice.updateOuterBrand(outerBrandUpdateRequest));
    }

    @Override
    public Response<Boolean> outerBrandExcelImport(List<OuterBrandExcelImportRequest> request) {
        try {
            return Response.ok(outerservice.OuterBrandExcelImport(request));
        }catch (ServiceException e){
            return Response.fail(e.getMessage());
        }catch (Exception e){
            log.error("导入外部品牌失败！ param:{} ,case:{}",request, Throwables.getStackTraceAsString(e));
            return Response.fail("导入外部品牌失败!");
        }
    }

    @Override
    public Response<Boolean> recoverOuterBrand(OuterBrandDeleteRequest outerBrandDeleteRequest) {
        outerBrandDeleteRequest.setOperatorId(Constant.ZQ_DEFAULT_OPERATOR_ID);
        return Response.ok(outerservice.recoverOuterBrand(outerBrandDeleteRequest));
    }

}

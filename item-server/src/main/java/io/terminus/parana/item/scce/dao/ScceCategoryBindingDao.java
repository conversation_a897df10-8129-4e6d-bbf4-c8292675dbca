package io.terminus.parana.item.scce.dao;

import io.terminus.common.model.Paging;
import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.scce.model.ScceCategoryBindingModel;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public class ScceCategoryBindingDao extends MyBatisDao<ScceCategoryBindingModel> {

	/**
	 *	创建
	 */
	public Boolean createModel(ScceCategoryBindingModel model) {
		return this.create(model);
	}
	/**
	 *	修改
	 */
	public Boolean updateModel(ScceCategoryBindingModel model) {
		return this.update(model);
	}
	/**
	 *	查看
	 */
	public ScceCategoryBindingModel queryOne(ScceCategoryBindingModel model){
		return this.sqlSession.selectOne(sqlId("queryOne"), model);
	}
	/**
	 *	列表查询
	 */
	public List<ScceCategoryBindingModel> listByModel(ScceCategoryBindingModel model){
		return this.list(model);
	}
	/**
	 *	分页查询
	 */
	public Paging<ScceCategoryBindingModel> page(Map<String, Object> params, Integer offset, Integer limit){
		return this.paging(offset, limit, params);
	}
}

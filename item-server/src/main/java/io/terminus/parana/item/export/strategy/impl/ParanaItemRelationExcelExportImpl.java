package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.export.dto.ParanaItemRelationExportDTO;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.item.api.bean.request.item.ParanaItemRelationPageRequest;
import io.terminus.parana.item.item.api.bean.response.item.ParanaItemRelationInfoResponse;
import io.terminus.parana.item.item.api.facade.ParanaItemRelationReadFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.List;

@Service
@Slf4j
public class ParanaItemRelationExcelExportImpl implements ExcelExportStrategy {

    @Autowired
    private ExcelExportHelper excelExportHelper;
    @Autowired
    private ThirdPartyRegistry registry;
    @Autowired
    private ParanaItemRelationReadFacade paranaItemRelationReadFacade;
    @Override
    public String getType() {
        return ExcelExportType.PARANA_ITEM_RELATION_EXPORT.getReportType();
    }

    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        ParanaItemRelationPageRequest request = JSON.parseObject(requestJson, ParanaItemRelationPageRequest.class);
        log.info("一品多商导出参数:{}", request);
        String filePath;
        InputStream is = null;
        try {
            List<ParanaItemRelationExportDTO> dtoList = exportData(request);
            log.info("一品多商导出,导出结果条数：" + dtoList.size());
            Long endTime = System.currentTimeMillis();
            log.info("一品多商导出,time:{}", endTime - beginTime);
            ByteArrayOutputStream outputStream = excelExportHelper.generateExcel(dtoList, ParanaItemRelationExportDTO.class);
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            is.close();
        } catch (Exception e) {
            log.error("一品多商导出失败, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                }
            }
            throw new RuntimeException(e.getMessage());
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("一品多商导出失败, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }

    private List<ParanaItemRelationExportDTO> exportData(ParanaItemRelationPageRequest request){
        List<ParanaItemRelationExportDTO> list = Lists.newArrayList();
        request.setPageNo(1);
        request.setPageSize(200);
        while (true){
            try {
                Paging<ParanaItemRelationInfoResponse> paging = Assert.take(paranaItemRelationReadFacade.page(request));
                if(paging.isEmpty()){
                    break;
                }
                for (ParanaItemRelationInfoResponse datum : paging.getData()) {
                    for (int i = 0; i < datum.getRelationSkuIdList().size(); i++) {
                        ParanaItemRelationExportDTO dto = new ParanaItemRelationExportDTO();
                        dto.setItemId(datum.getItemId().toString());
                        dto.setSkuId(datum.getSkuId().toString());
                        dto.setItemName(datum.getName());
                        dto.setAttrName(datum.getAttr());
                        dto.setVendorName(datum.getVendorName());
                        dto.setBasePrice(datum.getDefaultBasePrice() /100.0 + "");
                        dto.setOriginalPrice(datum.getOriginalPrice() /100.0 + "");
                        dto.setRealQuantity(datum.getSellableQuantity().toString());
                        if(datum.getItemStatus() != null){
                            if(datum.getItemStatus() == 1){
                                dto.setItemStatus("上架");
                            }else if(datum.getItemStatus() == -1){
                                dto.setItemStatus("下架");
                            }else if(datum.getItemStatus() == -5){
                                dto.setItemStatus("审核中");
                            }
                        }
                        dto.setStatus("Y".equals(datum.getStatus()) ? "是":"否");
                        dto.setCreateBy(datum.getCreatedBy());
                        dto.setCreateAt(DateUtil.format(datum.getCreatedAt(),"yyyy-MM-dd HH:mm:ss"));
                        dto.setUpdateBy(datum.getUpdatedBy());
                        dto.setUpdateAt(DateUtil.format(datum.getUpdatedAt(),"yyyy-MM-dd HH:mm:ss"));
                        if(CollectionUtil.isNotEmpty(datum.getRelationItemIdList()) && i < datum.getRelationItemIdList().size()){
                            dto.setRelationItemId(datum.getRelationItemIdList().get(i).toString());
                        }
                        if(CollectionUtil.isNotEmpty(datum.getRelationItemNameList()) && i < datum.getRelationItemNameList().size()){
                            dto.setRelationItemName(datum.getRelationItemNameList().get(i));
                        }
                        dto.setRelationSkuId(datum.getRelationSkuIdList().get(i).toString());
                        if(CollectionUtil.isNotEmpty(datum.getRelationAttrList()) && i < datum.getRelationAttrList().size()){
                            dto.setRelationAttrName(datum.getRelationAttrList().get(i));
                        }
                        if(CollectionUtil.isNotEmpty(datum.getRelationVendorNameList()) && i < datum.getRelationVendorNameList().size()){
                            dto.setRelationVendorName(datum.getRelationVendorNameList().get(i));
                        }
                        if(CollectionUtil.isNotEmpty(datum.getRelationDefaultBasePriceList()) && i < datum.getRelationDefaultBasePriceList().size()){
                            dto.setRelationBasePrice((datum.getRelationDefaultBasePriceList().get(i) /100.0)+"");
                        }
                        if(CollectionUtil.isNotEmpty(datum.getRelationOriginalPriceList()) && i < datum.getRelationOriginalPriceList().size()){
                            dto.setRelationOriginalPrice((datum.getRelationOriginalPriceList().get(i) /100.0)+"");
                        }
                        if(CollectionUtil.isNotEmpty(datum.getRelationSellableQuantityList()) && i < datum.getRelationSellableQuantityList().size()){
                            dto.setRelationRealQuantity(datum.getRelationSellableQuantityList().get(i).toString());
                        }
                        if(CollectionUtil.isNotEmpty(datum.getRelationItemStatusList()) && i < datum.getRelationItemStatusList().size()){
                            Integer relationItemStatus = datum.getRelationItemStatusList().get(i);
                            if(relationItemStatus != null){
                                if(relationItemStatus == 1){
                                    dto.setRelationItemStatus("上架");
                                }else if(relationItemStatus == -1){
                                    dto.setRelationItemStatus("下架");
                                }else if(relationItemStatus== -5){
                                    dto.setRelationItemStatus("审核中");
                                }
                            }
                        }
                        list.add(dto);
                    }
                }
            }catch (Exception e){
                log.error("一品多商导出 第{}页失败, error {}",request.getPageNo(), Throwables.getStackTraceAsString(e));
            }finally {
                request.setPageNo(request.getPageNo() + 1);
                //加多一个判断 防止死循环
                if(request.getPageNo() > 500){
                    break;
                }
            }
        }
        return list;
    }
}

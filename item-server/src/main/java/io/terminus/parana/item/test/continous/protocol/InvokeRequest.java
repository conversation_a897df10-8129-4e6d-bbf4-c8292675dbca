package io.terminus.parana.item.test.continous.protocol;

import lombok.Data;

import java.io.Serializable;

/**
 * 进行单元测试的请求协议对象
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-21
 */
@Data
public class InvokeRequest implements Serializable {
    private static final long serialVersionUID = 6162519478671749612L;

    /**
     * 测试方法所在的类
     */
    private Class testClass;

    /**
     * 测试的方法名
     */
    private String methodName;
}

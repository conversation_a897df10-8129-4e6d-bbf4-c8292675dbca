package io.terminus.parana.item.common.schedule.scan.process;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RangeScanResultContainer<T> {

    private List<T> elements;

    private int lastProcessed;

    public static <T> RangeScanResultContainer<T> initialize() {
        return (RangeScanResultContainer<T>) new RangeScanResultContainer(null, 0);
    }
}

package io.terminus.parana.item.scce.api.converter;

import io.terminus.common.model.Paging;
import io.terminus.parana.item.scce.api.bean.request.*;
import io.terminus.parana.item.scce.api.bean.response.ScceCategoryBindingInfoResponse;
import io.terminus.parana.item.scce.model.ScceCategoryBindingModel;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ScceCategoryBindingApiConverter {

	ScceCategoryBindingModel get(ScceCategoryBindingCreateRequest request);

	ScceCategoryBindingModel get(ScceCategoryBindingUpdateRequest request);

	ScceCategoryBindingModel get(ScceCategoryBindingDeleteRequest request);

	ScceCategoryBindingModel get(ScceCategoryBindingQueryRequest request);

	ScceCategoryBindingModel get(ScceCategoryBindingPageRequest request);

	ScceCategoryBindingInfoResponse model2InfoResponse(ScceCategoryBindingModel model);

	List<ScceCategoryBindingInfoResponse> modelList2InfoResponseList(List<ScceCategoryBindingModel> modelList);

	Paging<ScceCategoryBindingInfoResponse> modePage2InfoPage(Paging<ScceCategoryBindingModel> model);
}

package io.terminus.parana.item.common.activity.api.facade.manage;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.channel.model.Channel;
import io.terminus.parana.item.channel.service.ChannelReadDomainService;
import io.terminus.parana.item.common.activity.api.bean.request.CommonActivityQueryDetailRequest;
import io.terminus.parana.item.common.activity.api.bean.request.CommonActivityQueryListRequest;
import io.terminus.parana.item.common.activity.api.bean.response.CommonActivityManageInfo;
import io.terminus.parana.item.common.activity.api.bean.response.CommonActivityManageListInfo;
import io.terminus.parana.item.common.activity.api.converter.CommonActivityManageConverter;
import io.terminus.parana.item.common.activity.api.facade.CommonActivityReadFacade;
import io.terminus.parana.item.common.activity.enums.CommonActivityStatus;
import io.terminus.parana.item.common.activity.enums.CommonActivityType;
import io.terminus.parana.item.common.activity.model.CommonActivityManage;
import io.terminus.parana.item.common.activity.service.CommonActivityChannelBindingService;
import io.terminus.parana.item.common.activity.service.CommonActivityGoodsBindingService;
import io.terminus.parana.item.common.activity.service.CommonActivityManageService;
import io.terminus.parana.item.favorites.api.bean.request.CommonActivityItemListQueryPageRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 常用活动读服务
 *
 * <AUTHOR>
 * @date 2021-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonActivityReadFacadeImpl implements CommonActivityReadFacade {

    private final ChannelReadDomainService channelReadDomainService;
    private final CommonActivityManageService commonActivityManageService;
    private final CommonActivityChannelBindingService commonActivityChannelBindingService;
    private final CommonActivityGoodsBindingService commonActivityGoodsBindingService;

    private final CommonActivityManageConverter commonActivityManageConverter;

    @Override
    public Response<CommonActivityManageInfo> findById(CommonActivityQueryDetailRequest request) {
        // 查询常用活动详情信息
        CommonActivityManageInfo data = commonActivityManageConverter.domain2info(commonActivityManageService.findById(request.getId()));
        // 查询所关联的商品IDs
        data.setItemIds(getItemIds(data.getId(), data.getOperatorId()));
        // 查询所关联的渠道IDs
        List<Long> channelIds = commonActivityChannelBindingService.findChannelIdsByCommonRelationId(data.getId(), data.getOperatorId());
        if (!CollectionUtils.isEmpty(channelIds)) {
            data.setChannelIds(channelIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        return Response.ok(data);
    }

    @Override
    public Response<Paging<CommonActivityManageListInfo>> paging(CommonActivityQueryListRequest request) {
        log.info("CommonActivityReadFacadeImpl.paging() request：{}", request);
        // 根据条件筛选出
        Long operatorId = request.getOperatorId();
        Paging<CommonActivityManage> paging = commonActivityManageService.paging(
                request.getChannels(), request.getCommonName(),
                request.getCommonType(), request.getStatus(),
                request.getStartAt(), request.getExpiredAt(), operatorId,
                request.getPageNo(), request.getPageSize(), request.getFlowingStatus());
        // 如果数据为空直接返回
        List<CommonActivityManage> commonActivityManageList = paging.getData();
        if (CollectionUtils.isEmpty(commonActivityManageList)) {
            return Response.ok(new Paging<>(paging.getTotal(), Collections.emptyList()));
        }
        // 如果是今日优惠则不需要查询渠道信息
        boolean flag = !CommonActivityType.FEATURED_DEAL.getKey().equals(request.getCommonType());
        // 查询渠道名称和商品IDs
        Map<Long, String> channelMap = new HashMap<>(16);
        List<CommonActivityManageListInfo> resultList = new ArrayList<>(commonActivityManageList.size());
        for (CommonActivityManage data : commonActivityManageList) {
            CommonActivityManageListInfo result = new CommonActivityManageListInfo();
            BeanUtils.copyProperties(data, result);
            if (flag) {
                List<Long> channelIds = commonActivityChannelBindingService
                        .findChannelIdsByCommonRelationId(data.getId(), operatorId);
                result.setChannelNames(handleChannelNames(channelMap, new HashSet<>(channelIds)));
            }
            result.setItemIds(getItemIds(data.getId(), data.getOperatorId()));
            result.setStatus(takeFlowingStatus(data.getStatus(), data.getStartAt(), data.getExpiredAt()));
            resultList.add(result);
        }
        return Response.ok(new Paging<>(paging.getTotal(), resultList));
    }

    /**
     * 获取渠道名称
     * @param channelMap 渠道信息
     * @param channelIds 渠道IDs
     * @return 渠道名称
     */
    private String handleChannelNames(Map<Long, String> channelMap, Set<Long> channelIds) {
        if (CollectionUtils.isEmpty(channelIds)) {
            return "";
        }
        Set<Long> oldChannelIds = new HashSet<>(channelIds);
        channelIds.removeAll(channelMap.keySet());
        if (!CollectionUtils.isEmpty(channelIds)) {
            List<Channel> channelList = channelReadDomainService.findByIds(channelIds);
            channelList.forEach(channel -> channelMap.put(channel.getId(), channel.getName()));
        }
        return oldChannelIds.stream().map(channelMap::get).collect(Collectors.joining(","));
    }

    /**
     * 处理已失效状态
     *
     * @return 状态值 {@link CommonActivityStatus}
     */
    private Integer takeFlowingStatus(Integer status, Date startAt, Date expiredAt) {
        if (!Objects.equals(status, CommonActivityStatus.OPEN.getValue())) {
            return status;
        }

        Date date = DateTime.now().toDate();
        if (date.before(startAt)) {
            return CommonActivityStatus.CLOSE.getValue();
        } else if (date.after(expiredAt)) {
            return CommonActivityStatus.INVALID.getValue();
        } else {
            return CommonActivityStatus.OPEN.getValue();
        }
    }

    /**
     * 拼接区域运营和商品ID，例：1:100546105615
     * @param commonRelationId 常用活动ID
     * @param operatorId 区域运营ID
     * @return 拼接后的商品IDs
     */
    private String getItemIds(Long commonRelationId, Long operatorId) {
        log.info("拼接区域运营和商品ID commonRelationId:{}, operatorId:{}", commonRelationId, operatorId);
        // 查询所关联的商品IDs
        List<Long> itemIds = commonActivityGoodsBindingService.findItemIdsByCommonRelationId(commonRelationId, operatorId);
        if (!CollectionUtils.isEmpty(itemIds)) {
            return itemIds.stream().map(itemId -> String.format("%d:%d", operatorId, itemId)).collect(Collectors.joining(","));
        }
        return null;
    }

    /**
     * 查询活动列表
     * @param request {@link CommonActivityItemListQueryPageRequest}
     * @return
     */
    @Override
    public Response<List<Long>> queryCommonActivityItems(CommonActivityItemListQueryPageRequest request) {
        List<Long> itemList = new ArrayList<>();
        Long operatorId = request.getOperatorId();
        List<Long> activityIds = null;
        if(!CommonActivityType.FEATURED_DEAL.getKey().equals(request.getCommonType())) {
            activityIds = commonActivityChannelBindingService
                    .findCommonRelationIdsByChannelIds(Arrays.asList(request.getChannelId()), operatorId);
            log.info("queryCommonActivityItems activityIds {}", activityIds);
            if(null == activityIds || activityIds.isEmpty()) {
                return Response.ok(itemList);
            }
        }

        List<CommonActivityManage> commonActivityManageList = commonActivityManageService
                .queryNormalCommonActivityManageList(activityIds, request.getCommonType(), operatorId);
        log.info("queryCommonActivityItems commonActivityManageList {}-{}", CommonActivityType.FEATURED_DEAL.getKey(), activityIds);
        if(null != commonActivityManageList && !commonActivityManageList.isEmpty()) {
            CommonActivityManage commonActivityManage = commonActivityManageList.get(0);
            itemList = commonActivityGoodsBindingService.findItemIdsByCommonRelationId(commonActivityManage.getId(), commonActivityManage.getOperatorId());
        }
        return Response.ok(itemList);
    }
}

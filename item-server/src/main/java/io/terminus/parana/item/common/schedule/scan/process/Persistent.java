package io.terminus.parana.item.common.schedule.scan.process;

import java.util.List;

/**
 * 持久化处理
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-29
 */
public interface Persistent<T> extends ThreadProtocol, Runnable {

    /**
     * 将数据持久化处理
     *
     * @param elements 元素集合
     * @param size     集合大小
     */
    void flush(List<T> elements, int size);

    /**
     * 注册交换区数据拉取能力
     *
     * @param puller 拉取能力
     */
    void registerBridgePuller(BridgePuller<T> puller);
}

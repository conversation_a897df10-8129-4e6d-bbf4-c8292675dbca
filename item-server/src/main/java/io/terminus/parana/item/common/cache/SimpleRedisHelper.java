package io.terminus.parana.item.common.cache;

import lombok.RequiredArgsConstructor;
import redis.clients.jedis.Jedis;
import redis.clients.util.Pool;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">Caily</a>
 * @date 2018-09-05
 */
@RequiredArgsConstructor
public class SimpleRedisHelper {

    private final Pool<Jedis> jedisPool;

    public void setValue(String key, String value) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.set(key, value);
        }
    }

    public void setValue(String key, String value, int timeout) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.setex(key, timeout, value);
        }
    }

    public boolean exists(String... key) {
        if (key == null || key.length < 1) {
            throw new IllegalArgumentException("match.key.is.empty");
        }

        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.exists(key) == key.length;
        }
    }

    public String getValue(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.get(key);
        }
    }

    public void remove(String... key) {
        if (key == null || key.length < 1) {
            return;
        }
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.del(key);
        }
    }
}

package io.terminus.parana.item.common.cache;

import com.google.common.collect.Sets;
import io.terminus.parana.item.common.cache.plugin.IndexCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Jedis;
import redis.clients.util.Pool;

import java.util.Set;

/**
 * 带索引缓存的双入参缓存抽象实现
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-16
 */
public abstract class AbstractMultiCacheWithIndex<TI1, TI2, TR> extends AbstractMultiCache<TI1, TI2, TR> implements IndexCache<TI1, TI2> {

    @Autowired
    private Pool<Jedis> jedisPool;

    public AbstractMultiCacheWithIndex(String namespace) {
        super(namespace);
    }

    @Override
    public TR get(TI1 input1, TI2 input2, Object... args) {
        addMember(input1, input2);
        return super.get(input1, input2, args);
    }

    @Override
    public String generateIndexCacheKey(TI1 parent) {
        return namespace + "IDX:" + parent.toString();
    }

    @Override
    public void remove(TI1 input) {
        Set<TI2> children = getChildren(input);

        if (CollectionUtils.isEmpty(children)) {
            return;
        }

        Set<String> cacheKeySet = Sets.newHashSetWithExpectedSize(children.size());
        for (TI2 ti2 : children) {
            cacheKeySet.add(generateKey(input, ti2));
        }

        release(cacheKeySet);
        releaseIndexCache(input);
    }

    @Override
    public Pool<Jedis> getJedisPool() {
        return this.jedisPool;
    }
}

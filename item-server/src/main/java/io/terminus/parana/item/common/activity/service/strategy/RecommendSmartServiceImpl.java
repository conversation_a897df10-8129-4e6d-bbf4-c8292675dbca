package io.terminus.parana.item.common.activity.service.strategy;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.channel.model.Channel;
import io.terminus.parana.item.channel.service.ChannelReadDomainService;
import io.terminus.parana.item.common.activity.api.bean.request.CommonActivityAddRequest;
import io.terminus.parana.item.common.activity.api.bean.request.CommonActivityUpdateRequest;
import io.terminus.parana.item.common.activity.api.converter.CommonActivityItemConverter;
import io.terminus.parana.item.common.activity.enums.CommonActivityStatus;
import io.terminus.parana.item.common.activity.enums.CommonActivityType;
import io.terminus.parana.item.common.activity.manager.CommonActivityManager;
import io.terminus.parana.item.common.activity.model.CommonActivityChannelBinding;
import io.terminus.parana.item.common.activity.model.CommonActivityGoodsBinding;
import io.terminus.parana.item.common.activity.model.CommonActivityManage;
import io.terminus.parana.item.common.spi.IdGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 创建/更新活动：智能推荐、为你推荐
 *
 * <AUTHOR>
 * @date 2021-07-10
 */
@Component
@RequiredArgsConstructor
public class RecommendSmartServiceImpl implements AbstractCommonActivityStrategy {

    private final IdGenerator idGenerator;
    private final CommonActivityManager commonActivityManger;
    private final ChannelReadDomainService channelReadDomainService;

    private final CommonActivityItemConverter commonActivityItemConverter;

    @Override
    public boolean create(CommonActivityAddRequest request) {
        if (CollectionUtils.isEmpty(request.getChannelIds())) {
            throw new ServiceException("请先选择渠道");
        }

        // 常用活动信息
        Date date = new Date();
        Long operatorId = request.getOperatorId();
        CommonActivityManage commonActivityManage = new CommonActivityManage();
        BeanUtils.copyProperties(request, commonActivityManage);
        Long commonActivityManageId = idGenerator.nextValue(CommonActivityManage.class);
        commonActivityManage.setId(commonActivityManageId);
        commonActivityManage.setOperatorId(operatorId);
        commonActivityManage.setUpdatedBy(request.getUserId());
        commonActivityManage.setCreatedAt(date);
        commonActivityManage.setUpdatedAt(date);
        commonActivityManage.setStatus(CommonActivityStatus.OPEN.getValue());

        // 活动关联商品信息
        List<String> itemIds = request.getItemIds();
        List<CommonActivityGoodsBinding> goodsBindList = new ArrayList<>(itemIds.size());
        if (!CollectionUtils.isEmpty(itemIds)) {
            goodsBindList = commonActivityItemConverter.packItemBindInfo(itemIds, commonActivityManageId, date, operatorId);
        }

        // 活动关联渠道信息
        List<Long> channelIds = request.getChannelIds();
        List<CommonActivityChannelBinding> channelBindList = packChannelBindInfo(channelIds, commonActivityManageId, date, operatorId);

        return commonActivityManger.createCommonActivityAndItem(commonActivityManage, goodsBindList, channelBindList);
    }

    @Override
    public boolean isSupport(String type) {
        return Objects.equals(type, CommonActivityType.RECOMMEND_SMART.getKey()) ||
                Objects.equals(type, CommonActivityType.RECOMMEND_TO_YOU.getKey());
    }

    @Override
    public boolean update(CommonActivityUpdateRequest request) {
        if (CollectionUtils.isEmpty(request.getChannelIds())) {
            throw new ServiceException("请先选择渠道");
        }

        // 常用活动信息
        Date date = new Date();
        CommonActivityManage commonActivityManage = new CommonActivityManage();
        BeanUtils.copyProperties(request, commonActivityManage);
        commonActivityManage.setUpdatedBy(request.getUserId());
        commonActivityManage.setUpdatedAt(date);

        // 更新商品
        List<CommonActivityGoodsBinding> goodsBindList = new ArrayList<>(10);
        if (request.getIsUpdateItem()) {
            List<String> itemIds = request.getItemIds();
            goodsBindList = commonActivityItemConverter.packItemBindInfo(itemIds, request.getId(), date, request.getOperatorId());
        }

        // 更新渠道
        List<Long> channelIds = request.getChannelIds();
        List<CommonActivityChannelBinding> channelBindList = packChannelBindInfo(channelIds, commonActivityManage.getId(), date, request.getOperatorId());

        return commonActivityManger.updateCommonActivityAndItem(commonActivityManage, goodsBindList, channelBindList);
    }

    /**
     * 封装常用活动所关联的渠道信息
     *
     * @param channelIds             渠道IDs
     * @param commonActivityManageId 常用活动ID
     * @param date                   触发时间
     * @return 商品信息列表
     */
    private List<CommonActivityChannelBinding> packChannelBindInfo(List<Long> channelIds, Long commonActivityManageId, Date date, Long operatorId) {
        List<CommonActivityChannelBinding> resultList = new ArrayList<>(channelIds.size());
        for (Long channelId : channelIds) {
            CommonActivityChannelBinding commonActivityChannelBinding = new CommonActivityChannelBinding();
            commonActivityChannelBinding.setId(idGenerator.nextValue(CommonActivityChannelBinding.class));
            commonActivityChannelBinding.setChannelId(channelId);
            commonActivityChannelBinding.setCommonRelationId(commonActivityManageId);
            commonActivityChannelBinding.setCreatedAt(date);
            commonActivityChannelBinding.setUpdatedAt(date);
            commonActivityChannelBinding.setOperatorId(operatorId);
            resultList.add(commonActivityChannelBinding);
        }
        return resultList;
    }

}

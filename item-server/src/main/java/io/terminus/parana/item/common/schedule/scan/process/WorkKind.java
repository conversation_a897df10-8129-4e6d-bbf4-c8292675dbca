package io.terminus.parana.item.common.schedule.scan.process;

import io.terminus.parana.item.common.enums.ConditionReadable;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-10-18
 */
@Getter
@AllArgsConstructor
public enum WorkKind implements ConditionReadable {
    PULLER(0, "数据拉取"),
    PUSHER(1, "数据推送");

    private int value;
    private String description;
}

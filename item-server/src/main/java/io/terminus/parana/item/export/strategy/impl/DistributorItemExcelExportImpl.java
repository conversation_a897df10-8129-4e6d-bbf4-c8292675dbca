package io.terminus.parana.item.export.strategy.impl;

import io.terminus.parana.item.choicelot.api.bean.response.DistributorImportResultLogs;
import io.terminus.parana.item.export.dto.DistributorItemBatchExcelTemplate;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class DistributorItemExcelExportImpl implements ExcelExportStrategy {


    @Override
    public String getType() {
        return ExcelExportType.COMMODITY_LIBRARY_IMPORT.getReportType();
    }

    @Override
    public String execute(String requestJson, String name) {

        return requestJson;
    }


    public   List<DistributorItemBatchExcelTemplate> convertExport( List<DistributorImportResultLogs> templateList){
        List<DistributorItemBatchExcelTemplate> distributorItemBatchExcelTemplates = new ArrayList<>();
        if(templateList.size()>0){
            for (DistributorImportResultLogs logs:templateList) {
                DistributorItemBatchExcelTemplate template = new DistributorItemBatchExcelTemplate();
                template.setNumber(logs.getNumber());
                template.setItemId(logs.getItemId());
                template.setSelectionLib(logs.getSelectionLib());
                template.setImportCheck(logs.getImportCheck());
                template.setErrorDescription(logs.getErrorDescription());
                distributorItemBatchExcelTemplates.add(template);
            }
        }
        return distributorItemBatchExcelTemplates;
    }
}

package io.terminus.parana.item.common.schedule.scan.process;

/**
 * 发号器定义
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-06
 */
public interface Impulse {

    /**
     * 从发号器获取下个id区间
     *
     * @param processed 上次任务处理的数量（影响任务分发策略）
     * @return 任务分片
     */
    ImpulseExchangeData getNext(int processed);

    /**
     * 获取已经处理的计数值
     *
     * @return 已处理计数值
     */
    long getProcessed();

    /**
     * 初始化，设置起始和结束id区间
     *
     * @param start 开始
     * @param end   结束
     */
    void init(long start, long end);

    /**
     * 停止过程（软停止）
     */
    void stop();
}

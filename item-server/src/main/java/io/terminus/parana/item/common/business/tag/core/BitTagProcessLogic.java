package io.terminus.parana.item.common.business.tag.core;

import io.terminus.parana.item.common.business.tag.ExecuteType;
import io.terminus.parana.item.common.extension.ProcessResultPack;

import java.util.Map;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-02-27
 */
public interface BitTagProcessLogic {

    /**
     * @param executeType
     * @param model
     * @param <Model>
     * @return
     */
    <Model> ProcessResultPack<Model> process(ExecuteType executeType, Model model);

    /**
     * 执行事务处理的扩展能力
     *
     * @param paramMap 附加参数
     * @return 执行是否成功
     */
    boolean transactional(Map<Integer, Object> paramMap);
}

package io.terminus.parana.item.util.excel.impor.m1;

import io.terminus.parana.trade.common.model.Extras;
import io.terminus.parana.trade.common.model.ShippingAddress;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

@Component
public class ExportConverter {

    public static final BigDecimal ONE_HUNDRED = BigDecimal.TEN.multiply(BigDecimal.TEN);

    public String converterStr(Object obj){
        String val="";
        if(obj!=null){
            val=String.valueOf(obj);
        }
        return val;
    }

    public String converterAmountStr(Long amount){
        String val="";
        if(amount!=null){
            BigDecimal num=new BigDecimal(amount);
            BigDecimal rate=new BigDecimal(100);
            val=num.divide(rate).toString();
        }
        return val;
    }

    public String converterAmountStrV2(Long amount){
        String val="";
        if(amount!=null){
            BigDecimal num=new BigDecimal(amount);
            BigDecimal rate=new BigDecimal(10000);
            val=num.divide(rate).toString();
        }
        return val;
    }

    public String converterAmountStr2(Long amount){
        String val="";
        if(amount!=null){
            BigDecimal num=new BigDecimal(amount);
            BigDecimal rate=new BigDecimal(1);
            val=num.divide(rate).toString();
        }
        return val;
    }

    public String converterAmountStr(Double amount){
        String val="";
        if(amount!=null){
            BigDecimal num=new BigDecimal(amount);
            BigDecimal rate=new BigDecimal(100);
            val=num.divide(rate).toString();
        }
        return val;
    }

    public String converterAmountStr(String amount){
        String val="";
        if(StringUtils.isNotBlank(amount) && !amount.equals("null")){
            BigDecimal num=new BigDecimal(amount);
            BigDecimal rate=new BigDecimal(100);
            val=num.divide(rate).toString();
        }
        return val;
    }

    public String converterAmountStr2(String amount){
        String val="";
        if(StringUtils.isNotBlank(amount) && !amount.equals("null")){
            BigDecimal num=new BigDecimal(amount);
            BigDecimal rate=new BigDecimal(10000);
            val=num.divide(rate).setScale(4,RoundingMode.HALF_UP).toString();
        }
        return val;
    }

    public String converterExtraStr(Extras extras, String key){
        String val="";
        if(extras!=null){
            Map<String,String> extraMap= extras.getExtraMap();
            if(extraMap!=null&&!extraMap.isEmpty()){
                val=converterAmountStr(extraMap.get(key));
            }
        }
        return val;
    }

    /**
     * 获取收货地址
     * @param shippingAddress
     * @return
     */
    public String converterAddresstr(ShippingAddress shippingAddress){
        String val="";
        if(shippingAddress!=null){
            String province= Optional.ofNullable(shippingAddress.getProvince()).orElse("");
            String city=Optional.ofNullable(shippingAddress.getCity()).orElse("");
            String region=Optional.ofNullable(shippingAddress.getRegion()).orElse("");
            String street=Optional.ofNullable(shippingAddress.getStreet()).orElse("");
            String detail=Optional.ofNullable(shippingAddress.getDetail()).orElse("");
            StringBuffer sb=new StringBuffer();
            sb.append(province+" ");
            sb.append(city+" ");
            sb.append(region+" ");
            sb.append(street+" ");
            sb.append(detail);
            val=sb.toString();
        }
        return val;
    }

    public String converterDateStr(Date date){
        String val="";
        if(date!=null){
            try {
                SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                val=sdf.format(date);
            } catch (Exception e) {
            }
        }
        return val;
    }

}

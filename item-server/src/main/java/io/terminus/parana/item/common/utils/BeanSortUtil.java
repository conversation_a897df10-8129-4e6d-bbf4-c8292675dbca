package io.terminus.parana.item.common.utils;

import org.springframework.core.annotation.Order;
import org.springframework.core.annotation.OrderUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * Bean对象排序输出工具
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-03-25
 */
public abstract class BeanSortUtil {

    private static final Integer DEFAULT_ORDER = 0;

    private static class OrderPair<T> {

        public OrderPair(int order, T value) {
            this.order = order;
            this.value = value;
        }

        private int order;

        private T value;

        public int getOrder() {
            return order;
        }

        public T getValue() {
            return value;
        }
    }

    @Order()
    public static <T> List<T> order(Collection<T> source) {

        if (CollectionUtils.isEmpty(source)) {
            return Collections.emptyList();
        }

        List<OrderPair<T>> orderPairList = new ArrayList<>(source.size());

        for (T t : source) {
            Integer order = OrderUtils.getOrder(t.getClass(), DEFAULT_ORDER);
            orderPairList.add(new OrderPair<>(order, t));
        }

        orderPairList.sort(Comparator.comparingInt(OrderPair::getOrder));

        return AssembleDataUtils.list2list(orderPairList, OrderPair::getValue);
    }
}

package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.eascs.user.enterprise.api.bean.request.param.EnterpriseAuthenticationQueryParam;
import com.eascs.user.enterprise.api.bean.response.EnterpriseAuthenticationInfo;
import com.eascs.user.enterprise.api.facade.EnterpriseAuthenticationReadFacade;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.terminus.common.model.Paging;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.inventory.api.bean.response.info.inventory.InventoryEntityResponseInfo;
import io.terminus.parana.ipm.api.bean.request.inventory.InventoryQueryByEntityAndOperatorIdRequest;
import io.terminus.parana.ipm.api.facade.IpmInventoryReadFacade;
import io.terminus.parana.item.area.enums.AreaItemStatus;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.service.AreaSkuReadDomainService;
import io.terminus.parana.item.category.manager.BackCategoryService;
import io.terminus.parana.item.category.model.BackCategory;
import io.terminus.parana.item.choicelot.utils.MarkupCalculateUtils;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.export.dto.AreaItemSearchExcelTemplate;
import io.terminus.parana.item.export.param.AreaItemSearchParam;
import io.terminus.parana.item.export.param.ItemSearchParam;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.item.api.bean.request.item.ItemQueryByIdRequest;
import io.terminus.parana.item.item.api.bean.request.item.VendorItemChannlRelationQueryRequest;
import io.terminus.parana.item.item.api.bean.request.sku.SkuQueryByItemRequest;
import io.terminus.parana.item.item.api.bean.response.item.FullAreaItemWithInfo;
import io.terminus.parana.item.item.api.bean.response.item.GroupedOtherAttributeInfo;
import io.terminus.parana.item.item.api.bean.response.item.ItemInfo;
import io.terminus.parana.item.item.api.bean.response.item.VendorItemChannlRelationInfoResponse;
import io.terminus.parana.item.item.api.bean.response.sku.SkuAttributeInfo;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.item.api.converter.output.ItemApiInfoConverter;
import io.terminus.parana.item.item.api.facade.SkuReadFacade;
import io.terminus.parana.item.item.api.facade.VendorItemChannlRelationReadFacade;
import io.terminus.parana.item.item.enums.ItemStatus;
import io.terminus.parana.item.item.model.FullAreaItemBO;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.item.service.SkuReadDomainService;
import io.terminus.parana.item.plugin.third.api.inventory.api.InventoryReadApi;
import io.terminus.parana.item.search.docobject.AreaItemDO;
import io.terminus.parana.item.search.docobject.ItemDO;
import io.terminus.parana.item.search.facade.AreaItemSearchFacade;
import io.terminus.parana.item.search.facade.ItemSearchFacade;
import io.terminus.parana.item.third.info.InventoryEntityResponse;
import io.terminus.parana.item.third.param.ThirdInventoryQueryByOperatorRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AeaItemExcelExportOssStrategyImpl implements ExcelExportStrategy {

    @Override
    public String getType() {
        return ExcelExportType.AREA_ITEM_EXPORT.getReportType();
    }

    @Autowired
    private ExcelExportHelper excelExportHelper;
    @Autowired
    private ThirdPartyRegistry registry;

    private ItemSearchFacade itemSearchFacade;
    @Autowired
    private AreaItemSearchFacade areaItemSearchFacade;
    @Autowired
    private SkuReadFacade skuReadFacade;
    @Autowired
    private SkuReadDomainService skuReadDomainService;
    @Autowired
    private ItemReadDomainService itemReadDomainService;
    @Autowired
    private ItemApiInfoConverter itemApiInfoConverter;
    @Autowired
    private BackCategoryService backCategoryService;
    @Autowired
    private InventoryReadApi inventoryReadApi;
    @Autowired
    private VendorItemChannlRelationReadFacade vendorItemChannlRelationReadFacade;
    @Autowired
    private EnterpriseAuthenticationReadFacade enterpriseAuthenticationReadFacade;
    @Autowired
    private AreaSkuReadDomainService areaSkuReadDomainService;
    @Autowired
    private IpmInventoryReadFacade ipmInventoryReadFacade;
    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        AreaItemSearchParam itemSearchRequest = JSON.parseObject(requestJson, AreaItemSearchParam.class);
        log.info("item.export,itemSearchRequest:{}", itemSearchRequest);
        //组装完的表格数据
        List<AreaItemSearchExcelTemplate> excelDataList = excelDataList(itemSearchRequest);
        log.info("item.export,excelDataList:{}", excelDataList);
        log.info("组装数据结果集：" + excelDataList);
        //组装完的搜索表头
        log.info("item.export,导出结果条数：" + excelDataList.size());
        Long endTime = System.currentTimeMillis();
        log.info("item.export.data.build,time:{}", endTime - beginTime);

        String filePath = null;
        InputStream is = null;
        try {
            ByteArrayOutputStream outputStream = null;
            if (excelDataList.isEmpty()) {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(AreaItemSearchExcelTemplate.class);
            } else {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(excelDataList, AreaItemSearchExcelTemplate.class);
            }
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            log.info("filePath：{}", filePath);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("item.export, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("item.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("item.export, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }

    public Paging<ItemDO> search(ItemSearchParam param) {
        param.setTenantId(param.getTenantId());
        handle(param);
        return Assert.take(itemSearchFacade.search(param));
    }

    public Paging<AreaItemDO> areaItemSearch(AreaItemSearchParam param) {
        param.setTenantId(param.getTenantId());
//        handle(param);
        return Assert.take(areaItemSearchFacade.search(param));
    }

    public void handle(ItemSearchParam param) {
        // 兼容参数处理
        if (null != param.getLowPriceYuan()) {
            param.setLowPrice(BigDecimal.valueOf(param.getLowPriceYuan()).multiply(BigDecimal.valueOf(100)).longValue());
        }
        if (null != param.getHighPriceYuan()) {
            param.setHighPrice(BigDecimal.valueOf(param.getHighPriceYuan()).multiply(BigDecimal.valueOf(100)).longValue());
        }
    }

    public Paging<FullAreaItemWithInfo> searchExport(AreaItemSearchParam param) {
        if (StringUtils.isEmpty(param.getStatus())){
            param.setStatus("1_-1_-2");
        }
        List<FullAreaItemWithInfo> dataInfoListResp = new ArrayList<>();
        int pageNo = param.getPageNo();
        while (true) {
            Paging<AreaItemDO> paging = areaItemSearch(param);
            if (paging.isEmpty()) {
                break;
            }
            //添加库存
            fillInventory(paging);
            log.info("paging.getData:{}", paging.getData());
            //获取供应商商品ID
            Set<Long> ids = paging.getData().stream().map(AreaItemDO::getItemId).collect(Collectors.toSet());

            Map<Long, AreaItemDO> areaItemMap = paging.getData().stream().collect(Collectors.toMap(AreaItemDO::getItemId, Function.identity()));

            ItemQueryByIdRequest itemReq = new ItemQueryByIdRequest();
            itemReq.setIdSet(ids);
            itemReq.setTenantId(param.getTenantId());
            log.info("itemReq:{}-{}", ids, itemReq);
            List<Item> itemList = itemReadDomainService.findByIdSet(ids, param.getTenantId(), null, null);
            log.info("itemRes:{}", itemList);
            SkuQueryByItemRequest skuReq = new SkuQueryByItemRequest();
            skuReq.setItemIdSet(ids);
            skuReq.setTenantId(param.getTenantId());
            List<Sku> skuList = skuReadDomainService.findByItemIdSet(ids, param.getTenantId(), null, null);
            if (skuList == null) {
                skuList = Lists.newArrayList();
            }
            Map<Long, List<Sku>> skuMapByItemId = skuList.stream().collect(Collectors.groupingBy(Sku::getItemId));
            //保存当前页的所有商品sku的库存数据  key为skuid  值为库存信息
            Map<Long, InventoryEntityResponse> inventoryMap = Maps.newHashMap();
            Set<Long> skuIds = skuList.stream().map(Sku::getId).collect(Collectors.toSet());
            Set<Long> vendorIds = skuList.stream().map(Sku::getShopId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(skuIds)) {
                //查询sku库存信息
                ThirdInventoryQueryByOperatorRequest request = new ThirdInventoryQueryByOperatorRequest();
                request.setSkuIdSet(skuIds);
                request.setOperatorId(param.getOperatorId());
                request.setVendorIdSet(vendorIds);
                List<InventoryEntityResponse> list = inventoryReadApi.queryByOperator(request);
                if (CollectionUtil.isNotEmpty(list)) {
                    inventoryMap = list.stream().collect(Collectors.toMap(p -> Long.valueOf(p.getEntityId()), p -> p));
                }
            }

            List<FullAreaItemWithInfo> dataInfoList = new ArrayList<>();
            for (Item itemRe : itemList) {
                FullAreaItemBO resp = new FullAreaItemBO();
                resp.setItem(itemRe);
                resp.setSkuList(skuMapByItemId.get(itemRe.getId()));
                FullAreaItemWithInfo fullAreaItemWithInfo = itemApiInfoConverter.get(resp);
                fullAreaItemWithInfo.setAreaItem(areaItemMap.get(itemRe.getId()));
                if (CollectionUtil.isNotEmpty(fullAreaItemWithInfo.getSkuInfoList())) {
                    for (SkuInfo info : fullAreaItemWithInfo.getSkuInfoList()) {
                        InventoryEntityResponse inventory = inventoryMap.get(info.getId());
                        if (inventory != null) {
                            info.setRealQuantity(inventory.getRealQuantity());
                        }
                    }
                }
                dataInfoList.add(fullAreaItemWithInfo);
            }
            dataInfoListResp.addAll(dataInfoList);
            pageNo++;
            param.setPageNo(pageNo);
        }

        return new Paging<>((long) dataInfoListResp.size(), dataInfoListResp);
    }

    public List<AreaItemSearchExcelTemplate> excelDataList(AreaItemSearchParam param) {
        log.info("excelDataList AreaItemSearchParam :{}", JSONUtil.toJsonStr(param));
        Paging<FullAreaItemWithInfo> dataInfoList = searchExport(param);
        //组装表格相关数据
        List<AreaItemSearchExcelTemplate> excelDataList = new ArrayList<>();
        List<FullAreaItemWithInfo> searchResult = dataInfoList.getData();
        List<ItemInfo> itemInfoList = searchResult.stream().map(FullAreaItemWithInfo::getItemInfo).collect(Collectors.toList());
        List<Long> categoryIdList = itemInfoList.stream().map(ItemInfo::getCategoryId).distinct().collect(Collectors.toList());
        Map<Long, List<BackCategory>> categoryMap = backCategoryService.batchFindAncestorsOf(categoryIdList);

        for (FullAreaItemWithInfo datum : searchResult) {
            List<SkuInfo> skuInfoList = datum.getSkuInfoList();
            for (SkuInfo skuInfo : skuInfoList) {
                AreaItemSearchExcelTemplate data = new AreaItemSearchExcelTemplate();
                AreaSku area = areaSkuReadDomainService.findByOperatorIdAndSkuId(param.getOperatorId(), skuInfo.getId());
                if (area!=null){
                    data.setPrice(area.getOriginalPrice() / 100.0);//建议零售价
                    data.setSupplyPrice(area.getBasePrice()/100.0);
                }
                if(CollectionUtil.isNotEmpty(skuInfo.getExtraPrice()) && skuInfo.getExtraPrice().containsKey("centralizedPurchasePrice")){
                    data.setCentralizedPurchasePrice(skuInfo.getExtraPrice().get("centralizedPurchasePrice")/100.0);
                }
                log.info("excelDataList skuInfo :{}", skuInfo);
                data.setAttrKey(skuInfo.getId().toString());
                ItemInfo itemInfo = datum.getItemInfo();
                String itemId = itemInfo.getId().toString();
                //查询销售渠道
                StringBuilder companyName = new StringBuilder();
                try {
                    VendorItemChannlRelationQueryRequest req = new VendorItemChannlRelationQueryRequest();
                    req.setId(itemInfo.getId());
                    List<VendorItemChannlRelationInfoResponse> take = Assert.take(vendorItemChannlRelationReadFacade.findItemChannl(req));
                    if (CollectionUtil.isNotEmpty(take))
                    {
                        for (VendorItemChannlRelationInfoResponse vendorItemChannlRelationInfoResponse : take) {
                            EnterpriseAuthenticationQueryParam queryParam = new EnterpriseAuthenticationQueryParam();
                            queryParam.setId(vendorItemChannlRelationInfoResponse.getChannelId());
                            EnterpriseAuthenticationInfo authenticationInfo = Assert.take(enterpriseAuthenticationReadFacade.view(queryParam));
                            if (authenticationInfo!=null)
                            {
                                companyName.append(authenticationInfo.getCompanyName()+";");
                            }

                        }
                    }
                } catch (Exception e) {
                    log.error("error_sku_:::{}",Throwables.getStackTraceAsString(e));
                }
                data.setSalesChannel(companyName.toString());
                //是否可退
                data.setSupportReturn("1".equals(itemInfo.getExtra().get("supportReturn")) ? "是" : "否");
                GroupedOtherAttributeInfo otherAttributes = new GroupedOtherAttributeInfo();
                    if (null != itemInfo.getOtherAttributes() && itemInfo.getOtherAttributes().size() > 0) {
                        if (itemInfo.getOtherAttributes().get(0).getGroup().equals("USER_DEFINED")) {
                            otherAttributes.setOtherAttributes(itemInfo.getOtherAttributes().get(0).getOtherAttributes());
                            log.info("excelDataList otherAttributes :{}", otherAttributes);
                        }
                }
                //获取销售渠道
                //String salesChannel = itemInfo.getSalesChannel();
                //data.setSalesChannel(salesChannel!=null?salesChannel:"");
                if (itemInfo.getType() == 1){
                    data.setType("普通商品");
                }else if (itemInfo.getType() == 6){
                    data.setType("电子卡券");
                }
                data.setItemId(itemId);
                String name = itemInfo.getName();
                data.setName(name);
                data.setVendorName("中台");
                if(
                        Objects.equals(param.getOperatorId(), 1L) // 中台
                        || Objects.equals(param.getOperatorId(), 2100000001L) // 怡通数科运营prod
                        || Objects.equals(param.getOperatorId(), 2100274001L) // 怡通数科运营dev
                ){
                    String shopName = itemInfo.getShopName();
                    data.setVendorName(shopName);
                }

                String skuCode = skuInfo.getSkuCode();
                data.setItemTaxCode(skuCode);
                data.setSkuCode(skuInfo.getBarcode());
                JSONObject skuJson = JSON.parseObject(skuInfo.getPriceJson());
                // 利润率
                Long rate = 0L;
                if (skuJson != null && skuJson.getLong("defaultBasePrice") != null && data.getPrice() != null) {
                    rate = MarkupCalculateUtils.getProfitRate(skuInfo.getOriginalPrice(), skuJson.getLong("defaultBasePrice"));
                }
                // data.setRate(rate/100.0);
                //库存数量
//                Long realQuantity = skuInfo.getRealQuantity();
//                data.setRealQuantity(realQuantity!=null?realQuantity:0L);
                // 起购数量
                String minQuantity = skuInfo.getExtra().get("minQuantity");
                data.setMinQuantity(minQuantity!=null?minQuantity:"");
                List<SkuAttributeInfo> attributes = skuInfo.getAttributes();
                StringBuilder vals = new StringBuilder();
                if (!CollectionUtils.isEmpty(attributes)) {
                    for (int i = 0; i < attributes.size(); i++) {
                        SkuAttributeInfo attribute = attributes.get(i);
                        String attrKey = attribute.getAttrKey();
                        String attrVal = attribute.getAttrVal();//属性值, 如红色
                        vals.append(attrKey).append(":").append(attrVal).append(";");
                    }
                }
                data.setAttrVal(vals.toString());//属性值, 如红色
                data.setBrandName(itemInfo.getBrandName());//品牌名称
                String status = "";
                AreaItemDO areaItem = datum.getAreaItem();//areaItem信息
                if (areaItem != null) {
                    if (ItemStatus.fromValue(areaItem.getStatus()) != null) {
                        status = AreaItemStatus.fromValue(areaItem.getStatus()).getDescription();
                    }
                    if (!ObjectUtils.isEmpty(areaItem.getSourceType())) {
                        //商品来源 0.品牌商供品运营  1.品牌商供品平台  2.运营供品平台
                        Integer sourceType = areaItem.getSourceType();
                        if (0 == sourceType) {
                            data.setSourceType("自有品牌商");
                        }else if (1 == sourceType) {
                            data.setSourceType("中台");
                        }else if (2 == sourceType) {
                            data.setSourceType("中台");
                        }
                    }
                    data.setRealQuantity(areaItem.getSellableQuantity());
                }
                data.setStatus(status);//商品状态
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                if (null != itemInfo.getCreatedAt()) {
                    data.setCreatedAt(sdf.format(itemInfo.getCreatedAt()));//创建时间
                    log.info("excelDataList setCreatedAt :{}", sdf.format(itemInfo.getCreatedAt()));
                }

                // 类目
                try {
                    List<Long> categoryIds = itemInfo.getCategoryIds();
                    List<BackCategory> backCategories = categoryMap.get(itemInfo.getCategoryId());
                    int categorySize = backCategories.size();
                    switch (categorySize) {
                        case 1:
                            data.setOneLevelCategoryId(categoryIds.get(0));
                            data.setOneLevelCategoryName(backCategories.get(0).getName());
                            break;
                        case 2:
                            data.setOneLevelCategoryId(categoryIds.get(0));
                            data.setOneLevelCategoryName(backCategories.get(0).getName());
                            data.setTwoLevelCategoryId(categoryIds.get(1));
                            data.setTwoLevelCategoryName(backCategories.get(1).getName());
                            break;
                        case 3:
                            data.setOneLevelCategoryId(categoryIds.get(0));
                            data.setOneLevelCategoryName(backCategories.get(0).getName());
                            data.setTwoLevelCategoryId(categoryIds.get(1));
                            data.setTwoLevelCategoryName(backCategories.get(1).getName());
                            data.setThreeLevelCategoryId(categoryIds.get(2));
                            data.setThreeLevelCategoryName(backCategories.get(2).getName());
                            break;
                    }
                }catch (Exception e){
                    log.error("商品类目数据错误：{} 查询类目：{}",JSON.toJSONString(itemInfo),JSON.toJSONString(categoryMap.get(itemInfo.getCategoryId())));
                }
                excelDataList.add(data);
            }

        }
        return excelDataList;
    }

    private Paging<AreaItemDO> fillInventory(Paging<AreaItemDO> areaItemPaging) {
        Map<String, List<InventoryEntityResponseInfo>> inventoryMap = getBatchOperatorInventories(areaItemPaging.getData());
        for (AreaItemDO areaItemDO : areaItemPaging.getData()) {
            Integer sourceType = areaItemDO.getSourceType();
            if (!ObjectUtils.isEmpty(sourceType)) {
                if (0 == sourceType) {
                    if (inventoryMap.containsKey(areaItemDO.getOperatorId() + "-" + areaItemDO.getItemId())) {
                        List<InventoryEntityResponseInfo> infos = inventoryMap.get(areaItemDO.getOperatorId() + "-" + areaItemDO.getItemId());
                        areaItemDO.setSellableQuantity(infos.stream().filter(e -> !ObjectUtils.isEmpty(e.getSellableQuantity())).mapToLong(InventoryEntityResponseInfo::getSellableQuantity).sum());
                    }
                }else if (1 == sourceType) {
                    if (inventoryMap.containsKey("1" + "-" + areaItemDO.getItemId())) {
                        List<InventoryEntityResponseInfo> infos = inventoryMap.get("1" + "-" + areaItemDO.getItemId());
                        areaItemDO.setSellableQuantity(infos.stream().filter(e -> !ObjectUtils.isEmpty(e.getSellableQuantity())).mapToLong(InventoryEntityResponseInfo::getSellableQuantity).sum());
                    }
                }else if (2 == sourceType) {
                    if (inventoryMap.containsKey(areaItemDO.getSourceOperatorId() + "-" + areaItemDO.getItemId())) {
                        List<InventoryEntityResponseInfo> infos = inventoryMap.get(areaItemDO.getSourceOperatorId() + "-" + areaItemDO.getItemId());
                        areaItemDO.setSellableQuantity(infos.stream().filter(e -> !ObjectUtils.isEmpty(e.getSellableQuantity())).mapToLong(InventoryEntityResponseInfo::getSellableQuantity).sum());
                    }
                }
            }
        }
        return areaItemPaging;
    }

    public Map<String, List<InventoryEntityResponseInfo>> getBatchOperatorInventories(List<AreaItemDO> models) {
        Map<String, List<InventoryEntityResponseInfo>> inventoryEntityResponse = Maps.newHashMap();

        Set<Long> itemIdSet = models.stream().map(AreaItemDO::getItemId).collect(Collectors.toSet());
        SkuQueryByItemRequest skuRequest = new SkuQueryByItemRequest();
        skuRequest.setItemIdSet(itemIdSet);
        skuRequest.setTenantId(RequestContext.getTenantId());
        List<SkuInfo> skuInfos = Assert.take(skuReadFacade.queryByItem(skuRequest));
        Map<Long, Long> skuIdAndItemIdMap = Maps.newHashMap();
        Set<Long> skuIds = Sets.newHashSet();
        if (!CollectionUtils.isEmpty(skuInfos)) {
            skuIdAndItemIdMap = skuInfos.stream().collect(Collectors.toMap(SkuInfo::getId, SkuInfo::getItemId));
            skuIds = skuInfos.stream().map(SkuInfo::getId).collect(Collectors.toSet());
        }
        Set<Long> vendorIds = models.stream().map(AreaItemDO::getVendorId).collect(Collectors.toSet());
        List<Long> operatorIds = models.stream().map(AreaItemDO::getOperatorId).collect(Collectors.toList());
        List<Long> sourceOperatorIds = models.stream().map(AreaItemDO::getSourceOperatorId).collect(Collectors.toList());
        operatorIds.addAll(sourceOperatorIds);
        operatorIds.add(1L);
        InventoryQueryByEntityAndOperatorIdRequest req = new InventoryQueryByEntityAndOperatorIdRequest();
        req.setOperatorIds(operatorIds);
        req.setSkuIdSet(skuIds);
        req.setVendorIdSet(vendorIds);
        log.info("getBatchOperatorInventories queryByEntityAndOperatorId req is {}", JSONUtil.toJsonStr(req));
        List<InventoryEntityResponseInfo> list = Assert.take(ipmInventoryReadFacade.queryByEntityAndOperatorId(req));
        log.info("getBatchOperatorInventories queryByEntityAndOperatorId resp is {}", JSONUtil.toJsonStr(list));
        if (!CollectionUtils.isEmpty(list)) {
            for (InventoryEntityResponseInfo inventoryEntityResponseInfo : list) {
                if (skuIdAndItemIdMap.containsKey(Long.valueOf(inventoryEntityResponseInfo.getEntityId()))) {
                    Long itemId = skuIdAndItemIdMap.get(Long.valueOf(inventoryEntityResponseInfo.getEntityId()));
                    List<InventoryEntityResponseInfo> lists = null;
                    if (inventoryEntityResponse.containsKey(inventoryEntityResponseInfo.getOperatorId() + "-" + itemId)) {
                        lists = inventoryEntityResponse.get(inventoryEntityResponseInfo.getOperatorId() + "-" + itemId);
                    }else {
                        lists = Lists.newArrayList();
                    }
                    lists.add(inventoryEntityResponseInfo);
                    inventoryEntityResponse.put(inventoryEntityResponseInfo.getOperatorId() + "-" + itemId, lists);
                }
            }
        }
        return inventoryEntityResponse;
    }

}

package io.terminus.parana.item.util.excel.describer;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.util.excel.consts.ColumnValueType;
import io.terminus.parana.item.web.excel.ValueParsePackage;
import lombok.RequiredArgsConstructor;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-03-03
 */
@RequiredArgsConstructor
abstract class AbstractFieldDescriber implements Describer {

    private final Class model;

    @SuppressWarnings("unchecked")
    Method getValueSetMethod(Field field) throws NoSuchMethodException {
        return model.getMethod(buildValueSetMethodName(field), field.getType());
    }

    protected static Object getValue(ValueParsePackage valueParsePackage, ColumnValueType columnValueType) {
        switch (columnValueType) {
            case INTEGER:
                return valueParsePackage.integerValue();
            case LONG:
                return valueParsePackage.longValue();
            case STRING:
                return valueParsePackage.stringValue();
            case DOUBLE:
                return valueParsePackage.doubleValue();
        }

        throw new ServiceException("unreachable");
    }

    static ColumnValueType resolveValueType(Class<?> type) {
        if (type.equals(String.class)) {
            return ColumnValueType.STRING;
        } else if (type.equals(Integer.class)) {
            return ColumnValueType.INTEGER;
        } else if (type.equals(Long.class)) {
            return ColumnValueType.LONG;
        } else if (type.equals(Double.class)) {
            return ColumnValueType.DOUBLE;
        } else {
            throw new ServiceException("unsupported.field.type");
        }
    }

    private String buildValueSetMethodName(Field field) {
        return "set" + upperCase(field.getName());
    }

    private static String upperCase(String str) {
        char[] ch = str.toCharArray();
        if (ch[0] >= 'a' && ch[0] <= 'z') {
            ch[0] = (char) (ch[0] - 32);
        }
        return new String(ch);
    }
}

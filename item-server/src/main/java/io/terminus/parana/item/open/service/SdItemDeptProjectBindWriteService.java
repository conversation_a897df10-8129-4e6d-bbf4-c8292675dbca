package io.terminus.parana.item.open.service;

import io.terminus.parana.item.open.dao.SdItemDeptProjectBindDao;
import io.terminus.parana.item.open.model.SdItemDeptProjectBindBO;
import org.springframework.stereotype.Component;


import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class SdItemDeptProjectBindWriteService {

	private final SdItemDeptProjectBindDao sdItemDeptProjectBindDao;
	/**
	 * 创建整购区运营跟sd平台项目绑定关系表
	 */
	public Boolean create(SdItemDeptProjectBindBO model) {
		return sdItemDeptProjectBindDao.createModel(model);
	}
	/**
	 * 修改整购区运营跟sd平台项目绑定关系表
	 */
	public Boolean update(SdItemDeptProjectBindBO model) {
		return sdItemDeptProjectBindDao.updateModel(model);
	}
	/**
	 * 删除整购区运营跟sd平台项目绑定关系表
	 */
	public Boolean delete(SdItemDeptProjectBindBO model) {
		return sdItemDeptProjectBindDao.deleteById(model);
	}
}

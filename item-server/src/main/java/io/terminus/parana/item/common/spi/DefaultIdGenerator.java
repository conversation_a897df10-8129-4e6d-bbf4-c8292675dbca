package io.terminus.parana.item.common.spi;

import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.constants.SequenceNames;
import io.terminus.shard.sequence.Sequence;

import java.util.Map;


/**
 * <AUTHOR> herf="mailto:<EMAIL>">Caily</a>
 * @date 2018-05-22
 */
public class DefaultIdGenerator implements IdGenerator, IdGeneratorRegister {

    private final Sequence sequence;
    private final Map<Class<?>, IdAssembleStrategy> assembleStrategyMap;

    private DefaultIdGenerator(Sequence sequence) {
        this.sequence = sequence;
        assembleStrategyMap = Maps.newHashMap();
    }

    @Override
    public void registerAssembleStrategy(Class<?> entity, IdAssembleStrategy idAssembleStrategy) {
        if (assembleStrategyMap.containsKey(entity)) {
            throw new ServiceException("entity.registered.before");
        }

        assembleStrategyMap.put(entity, idAssembleStrategy);
    }

    @Override
    public Long nextValue(Class<?> entity, Long... routeArgs) {
        // 基础主键id
        Long id = sequence.nextValue(SequenceNames.name(entity));

        if (assembleStrategyMap.containsKey(entity)) {
            IdAssembleStrategy assembly = assembleStrategyMap.get(entity);
            return assembly.assemble(id, routeArgs);
        }

        return id;
    }

    private static volatile DefaultIdGenerator instance;

    public static DefaultIdGenerator singleton(Sequence sequence) {
        if (instance == null) {
            synchronized (DefaultIdGenerator.class) {
                if (instance == null) {
                    instance = new DefaultIdGenerator(sequence);
                }
            }
        }

        return instance;
    }
}